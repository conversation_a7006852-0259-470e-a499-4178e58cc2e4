#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكون SerpAPI عبر RapidAPI - بديل متقدم لـ Google Search API
يوفر نتائج Google الحقيقية بدون حدود صارمة ومشاكل Captcha
"""

import aiohttp
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from urllib.parse import quote
from .logger import logger
from .database import db
from config.settings import BotConfig

class SerpAPISearch:
    """مكون SerpAPI عبر RapidAPI - بديل متقدم لـ Google Search"""
    
    def __init__(self):
        """تهيئة مكون SerpAPI"""
        # استخدام SerpAPI مباشرة (أفضل من RapidAPI)
        self.serpapi_key = getattr(BotConfig, 'SERPAPI_KEY', '')
        self.rapidapi_key = getattr(BotConfig, 'RAPIDAPI_KEY', '')  # احتياطي
        self.rapidapi_host = getattr(BotConfig, 'SERPAPI_RAPIDAPI_HOST', 'serpapi.p.rapidapi.com')

        # استخدام SerpAPI مباشرة أولاً
        if self.serpapi_key:
            self.base_url = "https://serpapi.com/search.json"
            self.use_rapidapi = False
        else:
            self.base_url = f"https://{self.rapidapi_host}/search.json"
            self.use_rapidapi = True
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'last_reset': datetime.now(),
            'daily_limit': 500,  # حد يومي افتراضي
            'daily_usage': 0
        }
        
        # تخزين مؤقت للنتائج
        self.cache = {}
        self.cache_duration = 1800  # 30 دقيقة
        
        # التحقق من صحة المفتاح
        if not self.serpapi_key and not self.rapidapi_key:
            logger.warning("⚠️ لا يوجد مفتاح SerpAPI أو RapidAPI - سيتم تعطيل SerpAPI")
            self.enabled = False
        else:
            self.enabled = True
            if self.use_rapidapi:
                logger.info(f"🔍 تم تهيئة SerpAPI عبر RapidAPI بنجاح")
            else:
                logger.info(f"🔍 تم تهيئة SerpAPI مباشرة بنجاح (مفتاح: {self.serpapi_key[:8]}...)")
    
    async def search(self, query: str, num_results: int = 10, **kwargs) -> List[Dict]:
        """
        البحث باستخدام SerpAPI عبر RapidAPI
        :param query: استعلام البحث
        :param num_results: عدد النتائج المطلوبة
        :param kwargs: معاملات إضافية
        :return: قائمة النتائج
        """
        if not self.enabled:
            logger.debug("🚫 SerpAPI غير مفعل")
            return []
        
        # فحص التخزين المؤقت
        cache_key = self._generate_cache_key(query, num_results, kwargs)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            logger.info(f"📦 استخدام نتائج مخزنة مؤقتاً لـ: {query}")
            return cached_result
        
        # فحص الحد اليومي
        if not self._check_daily_limit():
            logger.warning("⚠️ تم تجاوز الحد اليومي لـ SerpAPI")
            return []
        
        try:
            self.usage_stats['total_searches'] += 1
            self.usage_stats['daily_usage'] += 1
            
            # إعداد المعاملات
            params = {
                'q': query,
                'num': min(num_results, 100),  # SerpAPI يدعم حتى 100
                'hl': 'en',  # اللغة الإنجليزية (أفضل للألعاب)
                'gl': 'us',  # المنطقة الجغرافية (الولايات المتحدة)
                'google_domain': 'google.com',
                'safe': 'active',
                **kwargs
            }

            # إضافة مفتاح API
            if self.use_rapidapi:
                # استخدام RapidAPI
                headers = {
                    'X-RapidAPI-Key': self.rapidapi_key,
                    'X-RapidAPI-Host': self.rapidapi_host,
                    'Accept': 'application/json'
                }
            else:
                # استخدام SerpAPI مباشرة
                params['api_key'] = self.serpapi_key
                headers = {
                    'Accept': 'application/json',
                    'User-Agent': 'Gaming News Bot/1.0'
                }

            # إضافة معاملات افتراضية للألعاب
            if 'tbm' not in params:
                params['tbm'] = 'nws'  # البحث في الأخبار
            if 'tbs' not in params:
                params['tbs'] = 'qdr:w'  # الأسبوع الماضي
            
            logger.info(f"🔍 البحث في SerpAPI عن: '{query}'")
            logger.debug(f"URL: {self.base_url}")
            logger.debug(f"المعاملات: {params}")
            logger.debug(f"الرؤوس: {headers}")

            async with aiohttp.ClientSession() as session:
                async with session.get(self.base_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._process_search_results(data, query)
                        
                        # حفظ في التخزين المؤقت
                        self._cache_result(cache_key, results)
                        
                        self.usage_stats['successful_searches'] += 1
                        logger.info(f"✅ SerpAPI نجح: {len(results)} نتيجة لـ '{query}'")
                        
                        return results
                    
                    elif response.status == 429:
                        logger.warning("⚠️ تم تجاوز حد الطلبات في SerpAPI")
                        self.usage_stats['failed_searches'] += 1
                        return []
                    
                    elif response.status == 403:
                        logger.error("❌ خطأ في مفتاح RapidAPI أو انتهاء الاشتراك")
                        self.usage_stats['failed_searches'] += 1
                        return []
                    
                    else:
                        logger.error(f"❌ خطأ غير متوقع في SerpAPI: {response.status}")
                        error_text = await response.text()
                        logger.debug(f"تفاصيل الخطأ: {error_text}")
                        self.usage_stats['failed_searches'] += 1
                        return []
        
        except Exception as e:
            logger.error(f"❌ خطأ في SerpAPI: {e}")
            self.usage_stats['failed_searches'] += 1
            return []
    
    def _process_search_results(self, data: Dict, query: str) -> List[Dict]:
        """معالجة نتائج البحث من SerpAPI"""
        results = []
        
        try:
            # معالجة نتائج الأخبار
            if 'news_results' in data:
                for item in data['news_results']:
                    result = self._process_news_result(item, query)
                    if result:
                        results.append(result)
            
            # معالجة النتائج العادية
            if 'organic_results' in data:
                for item in data['organic_results']:
                    result = self._process_organic_result(item, query)
                    if result:
                        results.append(result)
            
            # ترتيب حسب الصلة والحداثة
            results = self._sort_results_by_relevance(results, query)
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتائج SerpAPI: {e}")
        
        return results
    
    def _process_news_result(self, item: Dict, query: str) -> Optional[Dict]:
        """معالجة نتيجة خبر واحدة"""
        try:
            result = {
                'title': item.get('title', ''),
                'link': item.get('link', ''),
                'snippet': item.get('snippet', ''),
                'source': item.get('source', ''),
                'date': item.get('date', ''),
                'thumbnail': item.get('thumbnail', ''),
                'search_engine': 'SerpAPI-News',
                'search_time': datetime.now(),
                'relevance_score': self._calculate_relevance(item, query),
                'content_type': 'news'
            }
            
            # فلترة النتائج منخفضة الجودة
            if len(result['title']) < 10 or not result['link']:
                return None
            
            # فحص الصلة بالألعاب
            gaming_keywords = ['game', 'gaming', 'video', 'player', 'console', 'pc', 'mobile']
            text_to_check = f"{result['title']} {result['snippet']}".lower()
            
            if not any(keyword in text_to_check for keyword in gaming_keywords):
                return None
            
            return result
            
        except Exception as e:
            logger.debug(f"خطأ في معالجة نتيجة خبر: {e}")
            return None
    
    def _process_organic_result(self, item: Dict, query: str) -> Optional[Dict]:
        """معالجة نتيجة بحث عادية"""
        try:
            result = {
                'title': item.get('title', ''),
                'link': item.get('link', ''),
                'snippet': item.get('snippet', ''),
                'displayed_link': item.get('displayed_link', ''),
                'search_engine': 'SerpAPI-Organic',
                'search_time': datetime.now(),
                'relevance_score': self._calculate_relevance(item, query),
                'content_type': 'organic'
            }
            
            # فلترة النتائج منخفضة الجودة
            if len(result['title']) < 10 or not result['link']:
                return None
            
            return result
            
        except Exception as e:
            logger.debug(f"خطأ في معالجة نتيجة عادية: {e}")
            return None
    
    def _calculate_relevance(self, item: Dict, query: str) -> float:
        """حساب درجة الصلة للنتيجة"""
        try:
            score = 0.0
            query_words = query.lower().split()
            
            title = item.get('title', '').lower()
            snippet = item.get('snippet', '').lower()
            
            # نقاط للكلمات المطابقة في العنوان
            for word in query_words:
                if word in title:
                    score += 2.0
                if word in snippet:
                    score += 1.0
            
            # نقاط إضافية للمصادر الموثوقة
            trusted_sources = ['ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com']
            link = item.get('link', '').lower()
            
            if any(source in link for source in trusted_sources):
                score += 3.0
            
            # نقاط للحداثة
            if 'date' in item:
                score += 1.0
            
            return min(score, 10.0)  # حد أقصى 10
            
        except Exception:
            return 1.0
    
    def _sort_results_by_relevance(self, results: List[Dict], query: str) -> List[Dict]:
        """ترتيب النتائج حسب الصلة"""
        try:
            return sorted(results, key=lambda x: x.get('relevance_score', 0), reverse=True)
        except Exception:
            return results
    
    def _generate_cache_key(self, query: str, num_results: int, kwargs: Dict) -> str:
        """إنشاء مفتاح للتخزين المؤقت"""
        cache_data = f"{query}_{num_results}_{json.dumps(kwargs, sort_keys=True)}"
        return f"serpapi_{hash(cache_data)}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[List[Dict]]:
        """الحصول على نتيجة مخزنة مؤقتاً"""
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_duration):
                return cached_data['results']
            else:
                del self.cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, results: List[Dict]):
        """حفظ النتيجة في التخزين المؤقت"""
        self.cache[cache_key] = {
            'results': results,
            'timestamp': datetime.now()
        }
        
        # تنظيف التخزين المؤقت القديم
        if len(self.cache) > 100:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]
    
    def _check_daily_limit(self) -> bool:
        """فحص الحد اليومي"""
        now = datetime.now()
        
        # إعادة تعيين العداد اليومي
        if now.date() > self.usage_stats['last_reset'].date():
            self.usage_stats['daily_usage'] = 0
            self.usage_stats['last_reset'] = now
        
        return self.usage_stats['daily_usage'] < self.usage_stats['daily_limit']
    
    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            **self.usage_stats,
            'success_rate': (
                self.usage_stats['successful_searches'] / 
                max(self.usage_stats['total_searches'], 1) * 100
            ),
            'cache_size': len(self.cache),
            'enabled': self.enabled
        }
    
    async def test_connection(self) -> Dict:
        """اختبار الاتصال بـ SerpAPI"""
        try:
            test_results = await self.search("gaming news test", num_results=1)
            
            return {
                'status': 'success' if test_results else 'failed',
                'message': f'تم العثور على {len(test_results)} نتيجة' if test_results else 'لم يتم العثور على نتائج',
                'api_key_valid': bool(test_results),
                'usage_stats': self.get_usage_stats()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في الاختبار: {str(e)}',
                'api_key_valid': False,
                'usage_stats': self.get_usage_stats()
            }

# إنشاء مثيل عام
serpapi_search = SerpAPISearch()
