#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصور الذكي - تحسين استهلاك API وجودة الصور
"""

import asyncio
import hashlib
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import aiofiles

from .logger import logger
from .image_guard import AIImageGenerator
from config.settings import BotConfig
import re

# استيراد الوحدات الجديدة
try:
    from .game_image_analyzer import game_image_analyzer
    from .manual_image_generator import manual_image_generator
    from .licensed_image_manager import licensed_image_manager, LicensedImage
except ImportError:
    # في حالة عدم توفر الوحدات الجديدة
    game_image_analyzer = None
    manual_image_generator = None
    licensed_image_manager = None

@dataclass
class ImageGenerationPolicy:
    """سياسة إنشاء الصور"""
    max_images_per_article: int = 1  # صورة واحدة فقط لكل مقال
    max_daily_generations: int = 50  # حد أقصى 50 صورة يومياً
    min_article_quality_score: float = 4.0  # حد أدنى لجودة المقال (مخفض للاختبار)
    cache_duration_hours: int = 24  # مدة التخزين المؤقت
    reuse_similar_images: bool = True  # إعادة استخدام الصور المشابهة
    priority_keywords: List[str] = None  # كلمات مفتاحية ذات أولوية

    def __post_init__(self):
        if self.priority_keywords is None:
            self.priority_keywords = [
                'breaking news', 'exclusive', 'review', 'announcement',
                'update', 'release', 'trailer', 'gameplay'
            ]

class SmartImageManager:
    """مدير الصور الذكي - تحسين استهلاك API وجودة الصور"""
    
    def __init__(self, policy: ImageGenerationPolicy = None):
        self.policy = policy or ImageGenerationPolicy()
        self.ai_generator = AIImageGenerator()
        
        # مسارات التخزين
        self.cache_dir = "cache/images"
        self.metadata_file = "cache/image_metadata.json"
        self.daily_stats_file = "cache/daily_image_stats.json"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs("cache", exist_ok=True)
        
        # إحصائيات يومية
        self.daily_stats = self._load_daily_stats()

        # إضافة إحصائيات جديدة إذا لم تكن موجودة
        if 'manual_fallbacks' not in self.daily_stats:
            self.daily_stats['manual_fallbacks'] = 0
        if 'game_analysis_success' not in self.daily_stats:
            self.daily_stats['game_analysis_success'] = 0
        
        # تخزين مؤقت للصور
        self.image_cache = self._load_image_cache()
        
        logger.info(f"🎨 تم تهيئة مدير الصور الذكي - حد أقصى {self.policy.max_daily_generations} صورة/يوم")
    
    def _load_daily_stats(self) -> Dict:
        """تحميل إحصائيات اليوم"""
        try:
            if os.path.exists(self.daily_stats_file):
                with open(self.daily_stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)

                # فحص إذا كانت الإحصائيات من اليوم الحالي
                today = datetime.now().strftime('%Y-%m-%d')
                if stats.get('date') == today:
                    # إضافة Pollinations.AI إذا لم يكن موجوداً
                    if 'pollinations' not in stats.get('api_calls', {}):
                        stats['api_calls']['pollinations'] = 0
                    return stats

            # إنشاء إحصائيات جديدة لليوم
            return {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'images_generated': 0,
                'api_calls': {
                    'pollinations': 0,  # الطريقة الأساسية الجديدة
                    'freepik': 0,
                    'fluxai': 0,
                    'total': 0
                },
                'cache_hits': 0,
                'cache_misses': 0,
                'articles_processed': 0
            }
        except Exception as e:
            logger.warning(f"خطأ في تحميل إحصائيات اليوم: {e}")
            return {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'images_generated': 0,
                'api_calls': {'pollinations': 0, 'freepik': 0, 'fluxai': 0, 'total': 0},
                'cache_hits': 0,
                'cache_misses': 0,
                'articles_processed': 0
            }
    
    def _save_daily_stats(self):
        """حفظ إحصائيات اليوم"""
        try:
            with open(self.daily_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.daily_stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"خطأ في حفظ إحصائيات اليوم: {e}")
    
    def _load_image_cache(self) -> Dict:
        """تحميل تخزين الصور المؤقت"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    cache = json.load(f)
                    
                # تنظيف الصور المنتهية الصلاحية
                current_time = datetime.now()
                valid_cache = {}
                
                for key, data in cache.items():
                    created_time = datetime.fromisoformat(data.get('created_at', '2020-01-01'))
                    if current_time - created_time < timedelta(hours=self.policy.cache_duration_hours):
                        valid_cache[key] = data
                
                return valid_cache
            
            return {}
        except Exception as e:
            logger.warning(f"خطأ في تحميل تخزين الصور: {e}")
            return {}
    
    def _save_image_cache(self):
        """حفظ تخزين الصور المؤقت"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.image_cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"خطأ في حفظ تخزين الصور: {e}")
    
    def _calculate_article_quality_score(self, article: Dict) -> float:
        """حساب نقاط جودة المقال"""
        score = 0.0
        
        # جودة العنوان (30%)
        title = article.get('title', '')
        if len(title) > 20 and len(title) < 100:
            score += 3.0
        
        # جودة المحتوى (40%)
        content = article.get('content', '')
        word_count = len(content.split())
        if word_count > 500:
            score += 4.0
        elif word_count > 200:
            score += 2.0
        
        # وجود كلمات مفتاحية (20%)
        keywords = article.get('keywords', [])
        if keywords and len(keywords) > 2:
            score += 2.0
        
        # أولوية المحتوى (10%)
        title_lower = title.lower()
        if any(keyword in title_lower for keyword in self.policy.priority_keywords):
            score += 1.0
        
        return min(10.0, score)
    
    def _generate_content_hash(self, article: Dict) -> str:
        """إنشاء hash للمحتوى للبحث عن صور مشابهة"""
        # استخدام العنوان والكلمات المفتاحية لإنشاء hash
        content_string = f"{article.get('title', '')}{article.get('keywords', [])}"
        return hashlib.md5(content_string.encode()).hexdigest()
    
    def _should_generate_image(self, article: Dict) -> Tuple[bool, str]:
        """تحديد ما إذا كان يجب إنشاء صورة للمقال"""
        
        # فحص الحد اليومي
        if self.daily_stats['images_generated'] >= self.policy.max_daily_generations:
            return False, f"تم الوصول للحد اليومي ({self.policy.max_daily_generations} صورة)"
        
        # فحص جودة المقال
        quality_score = self._calculate_article_quality_score(article)
        if quality_score < self.policy.min_article_quality_score:
            return False, f"جودة المقال منخفضة ({quality_score:.1f}/{self.policy.min_article_quality_score})"
        
        # فحص وجود صورة مشابهة في التخزين المؤقت
        if self.policy.reuse_similar_images:
            content_hash = self._generate_content_hash(article)
            if content_hash in self.image_cache:
                return False, "توجد صورة مشابهة في التخزين المؤقت"
        
        return True, f"مؤهل لإنشاء صورة (جودة: {quality_score:.1f}/10)"
    
    async def generate_smart_image_for_article(self, article: Dict) -> Optional[Dict]:
        """إنشاء صورة ذكية للمقال مع تحسين الاستهلاك"""
        
        self.daily_stats['articles_processed'] += 1
        
        # فحص ما إذا كان يجب إنشاء صورة
        should_generate, reason = self._should_generate_image(article)
        
        logger.info(f"🎨 تقييم المقال '{article.get('title', '')[:50]}...': {reason}")
        
        if not should_generate:
            # البحث عن صورة مشابهة في التخزين المؤقت
            if self.policy.reuse_similar_images:
                cached_image = self._find_similar_cached_image(article)
                if cached_image:
                    self.daily_stats['cache_hits'] += 1
                    self._save_daily_stats()
                    logger.info("♻️ تم استخدام صورة مشابهة من التخزين المؤقت")
                    return cached_image
            
            self.daily_stats['cache_misses'] += 1
            self._save_daily_stats()
            return None
        
        # إنشاء صورة جديدة - التدرج الصحيح: APIs مرخصة → ذكاء اصطناعي → يدوي
        try:
            # 🎯 الأولوية الأولى: البحث عن صور مرخصة من APIs الألعاب الرسمية
            if licensed_image_manager:
                try:
                    # استخراج اسم اللعبة من المقال
                    game_name = self._extract_game_name_from_article(article)
                    if game_name:
                        logger.info(f"🎯 البحث عن صور مرخصة لـ: {game_name}")
                        licensed_images = await licensed_image_manager.get_licensed_images_for_game(game_name, 1)

                        if licensed_images:
                            # تحويل الصورة المرخصة إلى تنسيق النظام
                            licensed_img = licensed_images[0]
                            image_result = {
                                'url': licensed_img.url,
                                'description': f'{licensed_img.image_type} for {licensed_img.game_name}',
                                'source': licensed_img.source,
                                'license': licensed_img.license_type,
                                'attribution': licensed_img.attribution,
                                'width': licensed_img.width,
                                'height': licensed_img.height,
                                'format': 'JPEG',
                                'generation_method': 'licensed_official',
                                'api_used': licensed_img.source.lower(),
                                'safe_for_adsense': licensed_img.safe_for_adsense,
                                'copyright_free': licensed_img.copyright_free,
                                'game_name': licensed_img.game_name,
                                'image_type': licensed_img.image_type
                            }

                            logger.info(f"✅ تم العثور على صورة مرخصة من {licensed_img.source}")

                            # حفظ في التخزين المؤقت
                            content_hash = self._generate_content_hash(article)
                            self.image_cache[content_hash] = {
                                **image_result,
                                'created_at': datetime.now().isoformat(),
                                'article_title': article.get('title', ''),
                                'quality_score': self._calculate_article_quality_score(article)
                            }

                            self.daily_stats['images_generated'] += 1
                            self.daily_stats['cache_misses'] += 1

                            # إضافة إحصائيات جديدة للصور المرخصة
                            if 'licensed_images_used' not in self.daily_stats:
                                self.daily_stats['licensed_images_used'] = 0
                            self.daily_stats['licensed_images_used'] += 1

                            self._save_image_cache()
                            self._save_daily_stats()

                            logger.info(f"🎯 تم استخدام صورة مرخصة رسمية ({self.daily_stats['images_generated']}/{self.policy.max_daily_generations})")
                            return image_result
                        else:
                            logger.info("🔍 لم يتم العثور على صور مرخصة، التحول للذكاء الاصطناعي...")
                    else:
                        logger.info("🤔 لم يتم تحديد اسم اللعبة، التحول للذكاء الاصطناعي...")

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في البحث عن الصور المرخصة: {e}")
                    logger.info("🔄 التحول للذكاء الاصطناعي بسبب خطأ في APIs المرخصة...")

            # 🤖 الأولوية الثانية: الذكاء الاصطناعي
            logger.info("🤖 محاولة إنشاء صورة بالذكاء الاصطناعي...")

            # تحليل اللعبة لفهم الأسلوب البصري
            game_analysis = None
            if game_image_analyzer:
                try:
                    game_analysis = await game_image_analyzer.analyze_game_for_image_generation(article)
                    if game_analysis and game_analysis.get('confidence_score', 0) > 0.5:
                        self.daily_stats['game_analysis_success'] += 1
                        logger.info(f"🎮 تم تحليل اللعبة بنجاح: {game_analysis.get('game_name', 'غير محدد')}")
                    else:
                        logger.info("🎮 تحليل اللعبة لم ينجح، استخدام الطريقة التقليدية")
                except Exception as e:
                    logger.warning(f"⚠️ خطأ في تحليل اللعبة: {e}")

            # إنشاء prompt محسن بناءً على التحليل
            if game_analysis and game_analysis.get('enhanced_prompt'):
                optimized_prompt = {'prompt': game_analysis['enhanced_prompt']}
                logger.info("🎨 استخدام prompt محسن من تحليل اللعبة")
            else:
                optimized_prompt = self._create_high_quality_prompt(article)
                logger.info("🎨 استخدام prompt تقليدي")

            # محاولة إنشاء الصورة بالذكاء الاصطناعي
            image_result = await self._generate_with_best_api(optimized_prompt)
            
            if image_result:
                # حفظ في التخزين المؤقت
                content_hash = self._generate_content_hash(article)
                self.image_cache[content_hash] = {
                    **image_result,
                    'created_at': datetime.now().isoformat(),
                    'article_title': article.get('title', ''),
                    'quality_score': self._calculate_article_quality_score(article)
                }
                
                self.daily_stats['images_generated'] += 1
                self.daily_stats['cache_misses'] += 1
                
                self._save_image_cache()
                self._save_daily_stats()
                
                logger.info(f"✅ تم إنشاء صورة عالية الجودة للمقال ({self.daily_stats['images_generated']}/{self.policy.max_daily_generations})")

                return image_result
            else:
                # فشل الذكاء الاصطناعي - استخدام النظام اليدوي كبديل
                logger.warning("⚠️ فشل في إنشاء الصورة بالذكاء الاصطناعي، التحول للنظام اليدوي...")

                if manual_image_generator:
                    # تعيين اسم الموقع للعلامة المائية
                    website_name = getattr(BotConfig, 'WEBSITE_NAME', 'Gaming News')
                    manual_image_generator.set_website_name(website_name)

                    # إنشاء صورة يدوية
                    manual_result = await manual_image_generator.generate_manual_image(article)

                    if manual_result:
                        # حفظ في التخزين المؤقت
                        content_hash = self._generate_content_hash(article)
                        self.image_cache[content_hash] = {
                            **manual_result,
                            'created_at': datetime.now().isoformat(),
                            'article_title': article.get('title', ''),
                            'quality_score': self._calculate_article_quality_score(article),
                            'fallback_method': 'manual_generation'
                        }

                        self.daily_stats['images_generated'] += 1
                        self.daily_stats['cache_misses'] += 1
                        self.daily_stats['manual_fallbacks'] = self.daily_stats.get('manual_fallbacks', 0) + 1

                        self._save_image_cache()
                        self._save_daily_stats()

                        logger.info("✅ تم إنشاء صورة يدوية كبديل بنجاح")
                        return manual_result
                    else:
                        logger.error("❌ فشل في إنشاء الصورة اليدوية أيضاً")
                        return None
                else:
                    logger.error("❌ النظام اليدوي غير متوفر")
                    return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
            return None
    
    def _find_similar_cached_image(self, article: Dict) -> Optional[Dict]:
        """البحث عن صورة مشابهة في التخزين المؤقت"""
        article_keywords = set(article.get('keywords', []))
        article_title_words = set(article.get('title', '').lower().split())
        
        best_match = None
        best_score = 0
        
        for cached_data in self.image_cache.values():
            # حساب التشابه بناءً على العنوان والكلمات المفتاحية
            cached_title_words = set(cached_data.get('article_title', '').lower().split())
            
            # حساب نقاط التشابه
            title_similarity = len(article_title_words & cached_title_words) / max(len(article_title_words), 1)
            
            total_score = title_similarity
            
            if total_score > best_score and total_score > 0.3:  # حد أدنى للتشابه
                best_score = total_score
                best_match = cached_data
        
        return best_match
    
    def _create_high_quality_prompt(self, article: Dict) -> Dict:
        """إنشاء prompt عالي الجودة للمقال"""
        title = article.get('title', '')
        keywords = article.get('keywords', [])
        
        # استخراج العناصر الرئيسية
        game_elements = []
        for keyword in keywords:
            if any(game_term in keyword.lower() for game_term in ['game', 'gaming', 'play', 'console', 'pc']):
                game_elements.append(keyword)
        
        # إنشاء prompt محسن
        if game_elements:
            main_element = game_elements[0]
            prompt = f"Professional gaming illustration featuring {main_element}, modern digital art style, high quality 4K, vibrant colors, clean composition, suitable for gaming news article about '{title[:50]}'"
        else:
            prompt = f"Professional gaming-themed illustration, modern digital art style, high quality 4K, vibrant colors, clean composition, related to '{title[:50]}'"
        
        # إضافة معايير الجودة والأمان
        prompt += ", safe for work, family friendly, no violence, professional lighting, detailed artwork"
        
        return {
            'prompt': prompt,
            'category': 'high_quality_gaming',
            'priority': 1,
            'article_title': title
        }
    
    async def _generate_with_pollinations(self, prompt_data: Dict) -> Optional[Dict]:
        """إنشاء صورة باستخدام Pollinations.AI - الطريقة الأساسية الجديدة (مجاني 100%)"""
        try:
            import aiohttp
            import urllib.parse

            logger.info("🎨 إنشاء صورة باستخدام Pollinations.AI (مجاني)...")

            # تحسين الـ prompt للحصول على أفضل النتائج
            optimized_prompt = self._optimize_prompt_for_pollinations(prompt_data['prompt'])

            # ترميز الـ prompt للـ URL
            encoded_prompt = urllib.parse.quote(optimized_prompt)

            # إنشاء URL للصورة
            image_url = f"https://image.pollinations.ai/prompt/{encoded_prompt}"

            # إضافة معاملات إضافية لتحسين الجودة
            params = {
                'width': '1024',
                'height': '1024',
                'seed': '-1',  # عشوائي
                'model': 'flux',  # استخدام نموذج Flux المتقدم
                'enhance': 'true'  # تحسين الجودة
            }

            # إضافة المعاملات للـ URL
            param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            final_url = f"{image_url}?{param_string}"

            # التحقق من صحة الصورة
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(final_url) as response:
                    if response.status == 200:
                        # التحقق من أن المحتوى هو صورة
                        content_type = response.headers.get('content-type', '')
                        if 'image' in content_type:

                            # تحديث إحصائيات API
                            if 'pollinations' not in self.daily_stats['api_calls']:
                                self.daily_stats['api_calls']['pollinations'] = 0
                            self.daily_stats['api_calls']['pollinations'] += 1
                            self.daily_stats['api_calls']['total'] += 1

                            # تسجيل نجاح API
                            if 'api_attempts' not in self.daily_stats:
                                self.daily_stats['api_attempts'] = {}
                            if 'pollinations' not in self.daily_stats['api_attempts']:
                                self.daily_stats['api_attempts']['pollinations'] = {'success': 0, 'failure': 0}

                            self.daily_stats['api_attempts']['pollinations']['success'] += 1

                            logger.info("✅ تم إنشاء صورة عالية الجودة باستخدام Pollinations.AI")

                            return {
                                'url': final_url,
                                'description': prompt_data['prompt'][:100],
                                'source': 'Pollinations.AI',
                                'license': 'Open Source - Free Use',
                                'attribution': 'Generated by Pollinations.AI',
                                'width': 1024,
                                'height': 1024,
                                'format': 'PNG',
                                'generation_method': 'ai_generated_pollinations',
                                'api_used': 'pollinations',
                                'prompt_optimized': True,
                                'cache_eligible': True,
                                'free_service': True
                            }
                        else:
                            logger.warning(f"⚠️ Pollinations.AI أعاد محتوى غير صالح: {content_type}")
                            return None
                    else:
                        logger.warning(f"⚠️ خطأ في Pollinations.AI API: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صورة بـ Pollinations.AI: {e}")

            # تسجيل فشل API
            if 'api_attempts' not in self.daily_stats:
                self.daily_stats['api_attempts'] = {}
            if 'pollinations' not in self.daily_stats['api_attempts']:
                self.daily_stats['api_attempts']['pollinations'] = {'success': 0, 'failure': 0}

            self.daily_stats['api_attempts']['pollinations']['failure'] += 1
            return None

    def _optimize_prompt_for_pollinations(self, prompt: str) -> str:
        """تحسين الـ prompt خصيصاً لـ Pollinations.AI"""
        # إزالة الكلمات غير الضرورية وتحسين الوصف
        optimized = prompt.replace('safe for work', '').replace('family friendly', '')
        optimized = optimized.replace('no violence', '').replace('professional lighting', '')

        # إضافة كلمات مفتاحية لتحسين الجودة
        quality_keywords = "high quality, detailed, professional, 4k, masterpiece"

        # دمج الـ prompt المحسن
        final_prompt = f"{optimized.strip()}, {quality_keywords}"

        # تقليل الطول إذا كان طويلاً جداً
        if len(final_prompt) > 200:
            final_prompt = final_prompt[:200].rsplit(',', 1)[0]

        return final_prompt.strip()

    def _extract_game_name_from_article(self, article: Dict) -> Optional[str]:
        """استخراج اسم اللعبة من المقال - محسن"""
        try:
            import re

            title = article.get('title', '')
            content = article.get('content', '')
            keywords = article.get('keywords', [])

            # قائمة بأسماء الألعاب الشائعة للمطابقة السريعة
            common_games = [
                'Stardew Valley', 'Minecraft', 'Fortnite', 'Call of Duty', 'FIFA', 'PES',
                'League of Legends', 'Valorant', 'Overwatch', 'Cyberpunk 2077', 'The Witcher',
                'Grand Theft Auto', 'GTA', 'Red Dead Redemption', 'Assassins Creed',
                'Far Cry', 'Watch Dogs', 'Rainbow Six', 'Counter Strike', 'CS:GO', 'CS2',
                'Dota 2', 'World of Warcraft', 'Hearthstone', 'Diablo', 'StarCraft',
                'Apex Legends', 'PUBG', 'Fall Guys', 'Among Us', 'Rocket League',
                'Elden Ring', 'Dark Souls', 'Sekiro', 'Bloodborne', 'God of War',
                'Spider-Man', 'The Last of Us', 'Horizon', 'Ghost of Tsushima',
                'Final Fantasy', 'Resident Evil', 'Silent Hill', 'Metal Gear',
                'Tekken', 'Street Fighter', 'Mortal Kombat', 'Super Mario',
                'The Legend of Zelda', 'Pokemon', 'Animal Crossing', 'Splatoon'
            ]

            # البحث عن الألعاب الشائعة في النص
            full_text = f"{title} {content[:500]}".lower()
            for game in common_games:
                if game.lower() in full_text:
                    logger.info(f"🎯 تم العثور على لعبة شائعة: {game}")
                    return game

            # البحث في الكلمات المفتاحية أولاً
            for keyword in keywords:
                if isinstance(keyword, str) and len(keyword) > 3:
                    # تنظيف الكلمة المفتاحية
                    clean_keyword = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار|gaming|ألعاب|تحديث|إصدار)\b', '', keyword, flags=re.IGNORECASE).strip()
                    clean_keyword = re.sub(r'[^\w\s]', ' ', clean_keyword).strip()
                    clean_keyword = re.sub(r'\s+', ' ', clean_keyword).strip()

                    if len(clean_keyword) > 3 and not any(word in clean_keyword.lower() for word in ['player', 'console', 'تقييم', 'جديد', 'أفضل']):
                        logger.info(f"🎯 تم استخراج اسم اللعبة من الكلمات المفتاحية: {clean_keyword}")
                        return clean_keyword

            # البحث في العنوان - أنماط محسنة
            patterns = [
                r'"([^"]+)"',  # بين علامات اقتباس
                r'لعبة\s+([^\s\-\:]+(?:\s+[^\s\-\:]+)*)',  # بعد كلمة "لعبة"
                r'game\s+([^\s\-\:]+(?:\s+[^\s\-\:]+)*)',  # بعد كلمة "game"
                r'\b([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)\b',  # أسماء بأحرف كبيرة
                r'\b([a-zA-Z]+\s*[0-9]+(?:\s*[a-zA-Z]*)*)\b',  # أسماء مع أرقام
                r'في\s+([^\s\-\:]+(?:\s+[^\s\-\:]+)*)',  # بعد كلمة "في"
                r'من\s+([^\s\-\:]+(?:\s+[^\s\-\:]+)*)',  # بعد كلمة "من"
            ]

            for pattern in patterns:
                matches = re.findall(pattern, title, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, str):
                        clean_match = self._clean_extracted_game_name(match)
                        if clean_match and len(clean_match) > 3:
                            logger.info(f"🎯 تم استخراج اسم اللعبة من العنوان: {clean_match}")
                            return clean_match

            # البحث في المحتوى (أول 400 حرف)
            if content:
                content_start = content[:400]
                for pattern in patterns[:4]:  # الأنماط الأكثر دقة
                    matches = re.findall(pattern, content_start, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, str):
                            clean_match = self._clean_extracted_game_name(match)
                            if clean_match and len(clean_match) > 3:
                                logger.info(f"🎯 تم استخراج اسم اللعبة من المحتوى: {clean_match}")
                                return clean_match

            # محاولة أخيرة: البحث عن كلمات إنجليزية في العنوان
            english_words = re.findall(r'\b[A-Z][a-zA-Z]{3,}\b', title)
            for word in english_words:
                if word.lower() not in ['Review', 'Game', 'News', 'Update', 'Guide', 'Tips']:
                    logger.info(f"🎯 تم استخراج اسم اللعبة (كلمة إنجليزية): {word}")
                    return word

            logger.warning("⚠️ لم يتم العثور على اسم اللعبة في المقال")
            return None

        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج اسم اللعبة: {e}")
            return None

    def _clean_extracted_game_name(self, name: str) -> Optional[str]:
        """تنظيف اسم اللعبة المستخرج"""
        try:
            # إزالة الكلمات الشائعة
            clean_name = re.sub(r'\b(game|لعبة|review|مراجعة|news|أخبار|تُعد|من|أبرز|في|على|مع|هذه|تلك|الجديدة|الحديثة|المميزة)\b', '', name, flags=re.IGNORECASE)

            # إزالة الرموز والأرقام غير المرغوبة
            clean_name = re.sub(r'[^\w\s]', ' ', clean_name)
            clean_name = re.sub(r'\s+', ' ', clean_name).strip()

            # تجاهل الأسماء القصيرة جداً أو الشائعة
            if len(clean_name) <= 3 or clean_name.lower() in ['hand', 'wild', 'hunt', 'best', 'new', 'top', 'free']:
                return None

            return clean_name

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تنظيف اسم اللعبة: {e}")
            return None

    async def _generate_with_best_api(self, prompt_data: Dict) -> Optional[Dict]:
        """إنشاء صورة باستخدام أفضل API متاح - النظام الجديد أولاً ثم البدائل"""

        # محاولة النظام الجديد أولاً
        try:
            from modules.advanced_image_generator import advanced_image_generator

            logger.info("🎨 محاولة النظام المتقدم الجديد...")

            # إنشاء بيانات مقال للنظام الجديد
            article_data = {
                'title': prompt_data.get('title', ''),
                'content': prompt_data.get('content', ''),
                'category': prompt_data.get('category', 'gaming')
            }

            result = await advanced_image_generator.generate_image(prompt_data['prompt'], article_data)

            if result and result.get('url'):
                logger.info("✅ تم إنشاء صورة باستخدام النظام المتقدم الجديد")
                return result
            else:
                logger.warning("⚠️ النظام المتقدم الجديد لم يعيد نتيجة، التحول للنظام القديم...")

        except ImportError:
            logger.warning("⚠️ النظام المتقدم الجديد غير متوفر، استخدام النظام القديم...")
        except Exception as e:
            logger.warning(f"⚠️ خطأ في النظام المتقدم الجديد: {e}، التحول للنظام القديم...")

        # ترتيب APIs القديمة كنظام احتياطي
        api_priority = [
            ('pollinations', self._generate_with_pollinations),
            ('freepik', self.ai_generator._generate_with_freepik),
            ('fluxai', self.ai_generator._generate_with_fluxai)
        ]

        for api_name, api_method in api_priority:
            try:
                logger.info(f"🎨 محاولة إنشاء صورة باستخدام {api_name}...")

                result = await api_method(prompt_data)

                if result and isinstance(result, dict) and result.get('url'):
                    # إضافة معلومات إضافية
                    result.update({
                        'generation_method': 'smart_managed',
                        'api_used': api_name,
                        'prompt_optimized': True,
                        'cache_eligible': True
                    })

                    logger.info(f"✅ تم إنشاء صورة عالية الجودة باستخدام {api_name}")
                    return result
                else:
                    logger.warning(f"⚠️ {api_name} لم يعيد نتيجة صالحة")

            except Exception as e:
                logger.warning(f"⚠️ فشل {api_name}: {e}")
                continue

        # إذا فشلت جميع APIs، استخدم صورة احتياطية
        logger.warning("⚠️ فشل في إنشاء الصورة باستخدام جميع APIs، استخدام صورة احتياطية...")
        return await self._get_fallback_image(prompt_data)

    async def _get_fallback_image(self, prompt_data: Dict) -> Optional[Dict]:
        """الحصول على صورة احتياطية عالية الجودة"""
        try:
            # محاولة أخيرة باستخدام Pollinations.AI مع prompt مبسط
            try:
                logger.info("🔄 محاولة أخيرة باستخدام Pollinations.AI مع prompt مبسط...")
                simple_prompt = {
                    'prompt': 'gaming controller, modern setup, high quality',
                    'category': 'fallback'
                }
                pollinations_result = await self._generate_with_pollinations(simple_prompt)
                if pollinations_result:
                    pollinations_result['generation_method'] = 'pollinations_fallback'
                    logger.info("✅ تم الحصول على صورة احتياطية من Pollinations.AI")
                    return pollinations_result
            except Exception as e:
                logger.warning(f"⚠️ فشل في الحصول على صورة احتياطية من Pollinations.AI: {e}")

            # صور احتياطية عالية الجودة للألعاب (الخيار الأخير)
            fallback_images = [
                {
                    'url': 'https://images.unsplash.com/photo-**********-adc38448a05e?w=1024&h=1024&fit=crop',
                    'description': 'Professional gaming setup with RGB lighting',
                    'source': 'Unsplash',
                    'license': 'Unsplash License',
                    'attribution': 'Photo by Florian Olivo on Unsplash',
                    'width': 1024,
                    'height': 1024,
                    'format': 'JPEG',
                    'generation_method': 'fallback_high_quality'
                },
                {
                    'url': 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=1024&h=1024&fit=crop',
                    'description': 'Gaming controller and headset',
                    'source': 'Unsplash',
                    'license': 'Unsplash License',
                    'attribution': 'Photo by Jesper Aggergaard on Unsplash',
                    'width': 1024,
                    'height': 1024,
                    'format': 'JPEG',
                    'generation_method': 'fallback_high_quality'
                },
                {
                    'url': 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=1024&h=1024&fit=crop',
                    'description': 'Modern gaming workspace',
                    'source': 'Unsplash',
                    'license': 'Unsplash License',
                    'attribution': 'Photo by Sean Do on Unsplash',
                    'width': 1024,
                    'height': 1024,
                    'format': 'JPEG',
                    'generation_method': 'fallback_high_quality'
                }
            ]

            # اختيار صورة عشوائية
            import random
            selected_image = random.choice(fallback_images)

            # إضافة معلومات إضافية
            selected_image.update({
                'safe_for_adsense': True,
                'copyright_free': True,
                'fallback_image': True,
                'generation_timestamp': datetime.now().isoformat(),
                'api_used': 'fallback',
                'generation_method': 'smart_fallback'
            })

            logger.info("✅ تم استخدام صورة احتياطية عالية الجودة من Unsplash")
            return selected_image

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صورة احتياطية: {e}")
            return None
    
    def get_daily_stats(self) -> Dict:
        """الحصول على إحصائيات اليوم"""
        total_requests = self.daily_stats['cache_hits'] + self.daily_stats['cache_misses']

        return {
            **self.daily_stats,
            'remaining_quota': self.policy.max_daily_generations - self.daily_stats['images_generated'],
            'cache_hit_rate': (
                self.daily_stats['cache_hits'] / max(total_requests, 1)
            ) * 100,
            'manual_fallback_rate': (
                self.daily_stats.get('manual_fallbacks', 0) / max(self.daily_stats['images_generated'], 1)
            ) * 100,
            'licensed_images_rate': (
                self.daily_stats.get('licensed_images_used', 0) / max(self.daily_stats['images_generated'], 1)
            ) * 100,
            'game_analysis_success_rate': (
                self.daily_stats.get('game_analysis_success', 0) / max(self.daily_stats['articles_processed'], 1)
            ) * 100,
            'policy': {
                'max_daily_generations': self.policy.max_daily_generations,
                'max_images_per_article': self.policy.max_images_per_article,
                'min_quality_score': self.policy.min_article_quality_score
            },
            'image_sources': {
                'licensed_official': self.daily_stats.get('licensed_images_used', 0),
                'ai_generated': self.daily_stats['images_generated'] - self.daily_stats.get('licensed_images_used', 0) - self.daily_stats.get('manual_fallbacks', 0),
                'manual_fallback': self.daily_stats.get('manual_fallbacks', 0)
            }
        }
    
    def reset_daily_quota(self):
        """إعادة تعيين الحصة اليومية (للاختبار أو الصيانة)"""
        self.daily_stats['images_generated'] = 0
        self.daily_stats['api_calls'] = {'pollinations': 0, 'freepik': 0, 'fluxai': 0, 'total': 0}
        self._save_daily_stats()
        logger.info("🔄 تم إعادة تعيين الحصة اليومية للصور")

# إنشاء مثيل عام
smart_image_manager = SmartImageManager()
