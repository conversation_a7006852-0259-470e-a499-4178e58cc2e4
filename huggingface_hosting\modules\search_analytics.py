# نظام تحليلات البحث المتقدم
import time
import json
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import statistics

from .logger import logger
from .advanced_cache_system import advanced_cache

@dataclass
class SearchAnalytics:
    """تحليلات البحث"""
    query: str
    timestamp: float
    search_engine: str
    results_count: int
    response_time: float
    cache_hit: bool
    quality_score: float
    cost: float
    success: bool
    error_message: str = ""

@dataclass
class PerformanceMetrics:
    """مقاييس الأداء"""
    avg_response_time: float
    cache_hit_rate: float
    success_rate: float
    total_cost: float
    queries_per_hour: float
    top_queries: List[Tuple[str, int]]
    engine_performance: Dict[str, Dict]

class SearchAnalyticsManager:
    """مدير تحليلات البحث"""
    
    def __init__(self, db_path: str = "cache/search_analytics.db"):
        self.db_path = db_path
        
        # إعدادات التحليلات
        self.settings = {
            'retention_days': 90,  # الاحتفاظ بالبيانات لـ 90 يوم
            'aggregation_interval': 3600,  # تجميع البيانات كل ساعة
            'alert_thresholds': {
                'response_time': 10.0,  # ثواني
                'success_rate': 80.0,   # نسبة مئوية
                'cost_per_day': 5.0     # دولار
            }
        }
        
        # إحصائيات في الذاكرة
        self.current_session = {
            'start_time': time.time(),
            'searches': 0,
            'cache_hits': 0,
            'total_cost': 0.0,
            'engines_used': defaultdict(int),
            'response_times': []
        }
        
        # تهيئة قاعدة البيانات
        self._init_database()
        
        logger.info("📊 تم تهيئة مدير تحليلات البحث")
    
    def _init_database(self):
        """تهيئة قاعدة بيانات التحليلات"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                # جدول التحليلات التفصيلية
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS search_analytics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        query TEXT NOT NULL,
                        timestamp REAL NOT NULL,
                        search_engine TEXT NOT NULL,
                        results_count INTEGER NOT NULL,
                        response_time REAL NOT NULL,
                        cache_hit BOOLEAN NOT NULL,
                        quality_score REAL NOT NULL,
                        cost REAL NOT NULL,
                        success BOOLEAN NOT NULL,
                        error_message TEXT DEFAULT '',
                        date_hour TEXT NOT NULL
                    )
                ''')
                
                # جدول التحليلات المجمعة (كل ساعة)
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS hourly_analytics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date_hour TEXT UNIQUE NOT NULL,
                        total_searches INTEGER NOT NULL,
                        cache_hits INTEGER NOT NULL,
                        avg_response_time REAL NOT NULL,
                        avg_quality_score REAL NOT NULL,
                        total_cost REAL NOT NULL,
                        success_rate REAL NOT NULL,
                        top_engines TEXT NOT NULL,
                        top_queries TEXT NOT NULL
                    )
                ''')
                
                # جدول التنبيهات
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp REAL NOT NULL,
                        alert_type TEXT NOT NULL,
                        message TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        resolved BOOLEAN DEFAULT FALSE
                    )
                ''')
                
                # فهارس للأداء
                conn.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON search_analytics(timestamp)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_date_hour ON search_analytics(date_hour)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_search_engine ON search_analytics(search_engine)')
                conn.execute('CREATE INDEX IF NOT EXISTS idx_query ON search_analytics(query)')
                
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات التحليلات: {e}")
    
    def record_search(self, analytics: SearchAnalytics):
        """تسجيل عملية بحث"""
        try:
            # تحديث الإحصائيات في الذاكرة
            self.current_session['searches'] += 1
            if analytics.cache_hit:
                self.current_session['cache_hits'] += 1
            self.current_session['total_cost'] += analytics.cost
            self.current_session['engines_used'][analytics.search_engine] += 1
            self.current_session['response_times'].append(analytics.response_time)
            
            # تحديد الساعة للتجميع
            dt = datetime.fromtimestamp(analytics.timestamp)
            date_hour = dt.strftime('%Y-%m-%d %H:00:00')
            
            # حفظ في قاعدة البيانات
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO search_analytics 
                    (query, timestamp, search_engine, results_count, response_time, 
                     cache_hit, quality_score, cost, success, error_message, date_hour)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analytics.query, analytics.timestamp, analytics.search_engine,
                    analytics.results_count, analytics.response_time, analytics.cache_hit,
                    analytics.quality_score, analytics.cost, analytics.success,
                    analytics.error_message, date_hour
                ))
            
            # فحص التنبيهات
            self._check_alerts(analytics)
            
        except Exception as e:
            logger.error(f"❌ فشل في تسجيل تحليلات البحث: {e}")
    
    def _check_alerts(self, analytics: SearchAnalytics):
        """فحص التنبيهات"""
        try:
            alerts = []
            
            # تنبيه وقت الاستجابة
            if analytics.response_time > self.settings['alert_thresholds']['response_time']:
                alerts.append({
                    'type': 'slow_response',
                    'message': f"وقت استجابة بطيء: {analytics.response_time:.2f}s لـ {analytics.search_engine}",
                    'severity': 'warning'
                })
            
            # تنبيه فشل البحث
            if not analytics.success:
                alerts.append({
                    'type': 'search_failure',
                    'message': f"فشل في البحث: {analytics.error_message}",
                    'severity': 'error'
                })
            
            # تنبيه جودة منخفضة
            if analytics.quality_score < 30:
                alerts.append({
                    'type': 'low_quality',
                    'message': f"جودة نتائج منخفضة: {analytics.quality_score:.1f}% للاستعلام '{analytics.query}'",
                    'severity': 'info'
                })
            
            # حفظ التنبيهات
            if alerts:
                with sqlite3.connect(self.db_path) as conn:
                    for alert in alerts:
                        conn.execute('''
                            INSERT INTO alerts (timestamp, alert_type, message, severity)
                            VALUES (?, ?, ?, ?)
                        ''', (time.time(), alert['type'], alert['message'], alert['severity']))
                
        except Exception as e:
            logger.error(f"❌ فشل في فحص التنبيهات: {e}")
    
    def get_performance_metrics(self, hours: int = 24) -> PerformanceMetrics:
        """الحصول على مقاييس الأداء"""
        try:
            current_time = time.time()
            start_time = current_time - (hours * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                # الإحصائيات الأساسية
                cursor = conn.execute('''
                    SELECT 
                        COUNT(*) as total_searches,
                        SUM(CASE WHEN cache_hit = 1 THEN 1 ELSE 0 END) as cache_hits,
                        AVG(response_time) as avg_response_time,
                        SUM(cost) as total_cost,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_searches
                    FROM search_analytics 
                    WHERE timestamp >= ?
                ''', (start_time,))
                
                row = cursor.fetchone()
                if not row or row[0] == 0:
                    return PerformanceMetrics(0, 0, 0, 0, 0, [], {})
                
                total_searches, cache_hits, avg_response_time, total_cost, successful_searches = row
                
                # حساب المعدلات
                cache_hit_rate = (cache_hits / total_searches) * 100 if total_searches > 0 else 0
                success_rate = (successful_searches / total_searches) * 100 if total_searches > 0 else 0
                queries_per_hour = total_searches / hours
                
                # أهم الاستعلامات
                cursor = conn.execute('''
                    SELECT query, COUNT(*) as count
                    FROM search_analytics 
                    WHERE timestamp >= ?
                    GROUP BY query
                    ORDER BY count DESC
                    LIMIT 10
                ''', (start_time,))
                
                top_queries = cursor.fetchall()
                
                # أداء محركات البحث
                cursor = conn.execute('''
                    SELECT 
                        search_engine,
                        COUNT(*) as total,
                        AVG(response_time) as avg_time,
                        AVG(quality_score) as avg_quality,
                        SUM(cost) as total_cost,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
                    FROM search_analytics 
                    WHERE timestamp >= ?
                    GROUP BY search_engine
                ''', (start_time,))
                
                engine_performance = {}
                for row in cursor.fetchall():
                    engine, total, avg_time, avg_quality, engine_cost, successful = row
                    engine_performance[engine] = {
                        'total_searches': total,
                        'avg_response_time': round(avg_time, 3),
                        'avg_quality_score': round(avg_quality, 1),
                        'total_cost': round(engine_cost, 4),
                        'success_rate': round((successful / total) * 100, 1) if total > 0 else 0
                    }
                
                return PerformanceMetrics(
                    avg_response_time=round(avg_response_time, 3),
                    cache_hit_rate=round(cache_hit_rate, 1),
                    success_rate=round(success_rate, 1),
                    total_cost=round(total_cost, 4),
                    queries_per_hour=round(queries_per_hour, 1),
                    top_queries=top_queries,
                    engine_performance=engine_performance
                )
                
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على مقاييس الأداء: {e}")
            return PerformanceMetrics(0, 0, 0, 0, 0, [], {})
    
    def get_trend_analysis(self, days: int = 7) -> Dict:
        """تحليل الاتجاهات"""
        try:
            current_time = time.time()
            start_time = current_time - (days * 86400)
            
            with sqlite3.connect(self.db_path) as conn:
                # تحليل يومي
                cursor = conn.execute('''
                    SELECT 
                        DATE(datetime(timestamp, 'unixepoch')) as date,
                        COUNT(*) as searches,
                        AVG(response_time) as avg_response_time,
                        SUM(cost) as daily_cost,
                        AVG(quality_score) as avg_quality
                    FROM search_analytics 
                    WHERE timestamp >= ?
                    GROUP BY DATE(datetime(timestamp, 'unixepoch'))
                    ORDER BY date
                ''', (start_time,))
                
                daily_trends = []
                for row in cursor.fetchall():
                    date, searches, avg_time, cost, quality = row
                    daily_trends.append({
                        'date': date,
                        'searches': searches,
                        'avg_response_time': round(avg_time, 3),
                        'cost': round(cost, 4),
                        'avg_quality': round(quality, 1)
                    })
                
                # تحليل ساعي (آخر 24 ساعة)
                last_24h = current_time - 86400
                cursor = conn.execute('''
                    SELECT 
                        strftime('%H', datetime(timestamp, 'unixepoch')) as hour,
                        COUNT(*) as searches,
                        AVG(response_time) as avg_response_time
                    FROM search_analytics 
                    WHERE timestamp >= ?
                    GROUP BY strftime('%H', datetime(timestamp, 'unixepoch'))
                    ORDER BY hour
                ''', (last_24h,))
                
                hourly_trends = []
                for row in cursor.fetchall():
                    hour, searches, avg_time = row
                    hourly_trends.append({
                        'hour': int(hour),
                        'searches': searches,
                        'avg_response_time': round(avg_time, 3)
                    })
                
                return {
                    'daily_trends': daily_trends,
                    'hourly_trends': hourly_trends,
                    'analysis_period_days': days
                }
                
        except Exception as e:
            logger.error(f"❌ فشل في تحليل الاتجاهات: {e}")
            return {}
    
    def get_current_session_stats(self) -> Dict:
        """الحصول على إحصائيات الجلسة الحالية"""
        try:
            session_duration = time.time() - self.current_session['start_time']
            
            # حساب المعدلات
            cache_hit_rate = 0
            if self.current_session['searches'] > 0:
                cache_hit_rate = (self.current_session['cache_hits'] / self.current_session['searches']) * 100
            
            avg_response_time = 0
            if self.current_session['response_times']:
                avg_response_time = statistics.mean(self.current_session['response_times'])
            
            searches_per_minute = 0
            if session_duration > 0:
                searches_per_minute = (self.current_session['searches'] / session_duration) * 60
            
            return {
                'session_duration_minutes': round(session_duration / 60, 1),
                'total_searches': self.current_session['searches'],
                'cache_hits': self.current_session['cache_hits'],
                'cache_hit_rate': round(cache_hit_rate, 1),
                'total_cost': round(self.current_session['total_cost'], 4),
                'avg_response_time': round(avg_response_time, 3),
                'searches_per_minute': round(searches_per_minute, 2),
                'engines_used': dict(self.current_session['engines_used'])
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع إحصائيات الجلسة: {e}")
            return {}
    
    def get_alerts(self, hours: int = 24, severity: str = None) -> List[Dict]:
        """الحصول على التنبيهات"""
        try:
            current_time = time.time()
            start_time = current_time - (hours * 3600)
            
            query = '''
                SELECT timestamp, alert_type, message, severity, resolved
                FROM alerts 
                WHERE timestamp >= ?
            '''
            params = [start_time]
            
            if severity:
                query += ' AND severity = ?'
                params.append(severity)
            
            query += ' ORDER BY timestamp DESC'
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(query, params)
                
                alerts = []
                for row in cursor.fetchall():
                    timestamp, alert_type, message, alert_severity, resolved = row
                    alerts.append({
                        'timestamp': timestamp,
                        'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                        'type': alert_type,
                        'message': message,
                        'severity': alert_severity,
                        'resolved': bool(resolved)
                    })
                
                return alerts
                
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على التنبيهات: {e}")
            return []
    
    def cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_time = time.time() - (self.settings['retention_days'] * 86400)
            
            with sqlite3.connect(self.db_path) as conn:
                # حذف التحليلات القديمة
                cursor = conn.execute('DELETE FROM search_analytics WHERE timestamp < ?', (cutoff_time,))
                deleted_analytics = cursor.rowcount
                
                # حذف التنبيهات القديمة
                cursor = conn.execute('DELETE FROM alerts WHERE timestamp < ?', (cutoff_time,))
                deleted_alerts = cursor.rowcount
                
                if deleted_analytics > 0 or deleted_alerts > 0:
                    logger.info(f"🧹 تم حذف {deleted_analytics} تحليل و {deleted_alerts} تنبيه قديم")
                
        except Exception as e:
            logger.error(f"❌ فشل في تنظيف البيانات القديمة: {e}")

# إنشاء مثيل عام
search_analytics = SearchAnalyticsManager()
