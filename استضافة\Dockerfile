# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV GRADIO_SERVER_NAME="0.0.0.0"
ENV GRADIO_SERVER_PORT=7860

# تحديث النظام وتثبيت التبعيات الأساسية
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    libxml2-dev \
    libxslt1-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libopenblas-dev \
    gfortran \
    libhdf5-dev \
    libatlas-base-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير جذر
RUN useradd --create-home --shell /bin/bash app
USER app
WORKDIR /home/<USER>

# نسخ ملفات المتطلبات
COPY --chown=app:app requirements_huggingface.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir --user -r requirements_huggingface.txt

# نسخ ملفات التطبيق
COPY --chown=app:app . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p logs data cache images

# تعيين المسار
ENV PATH="/home/<USER>/.local/bin:$PATH"

# فتح المنفذ
EXPOSE 7860

# تشغيل التطبيق
CMD ["python", "app_huggingface.py"]
