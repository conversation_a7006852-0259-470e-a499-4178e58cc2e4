# معالج المحتوى الذكي المتكامل
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional
import json

from .logger import logger
from .intelligent_news_tracker import intelligent_news_tracker
from .enhanced_image_manager import enhanced_image_manager
from .enhanced_search_integration import enhanced_search
from .search_analytics import search_analytics, SearchAnalytics

class IntelligentContentProcessor:
    """معالج المحتوى الذكي المتكامل"""
    
    def __init__(self):
        self.processing_stats = {
            'articles_processed': 0,
            'important_stories_found': 0,
            'follow_up_searches': 0,
            'images_generated': 0,
            'total_processing_time': 0.0
        }
        
        # إعدادات المعالجة
        self.settings = {
            'enable_intelligent_tracking': True,
            'enable_enhanced_images': True,
            'max_articles_per_batch': 20,
            'processing_timeout': 300,  # 5 دقائق
            'quality_threshold': 60.0
        }
        
        logger.info("🧠 تم تهيئة معالج المحتوى الذكي المتكامل")
    
    async def process_articles_intelligently(self, articles: List[Dict]) -> List[Dict]:
        """معالجة المقالات بذكاء مع التتبع والتحسين"""
        try:
            start_time = time.time()
            logger.info(f"🧠 بدء المعالجة الذكية لـ {len(articles)} مقال...")
            
            if not articles:
                return []
            
            # تحديد المقالات للمعالجة (حد أقصى)
            articles_to_process = articles[:self.settings['max_articles_per_batch']]
            
            processed_articles = []
            
            # المرحلة 1: التتبع الذكي للأخبار
            if self.settings['enable_intelligent_tracking']:
                logger.info("📰 المرحلة 1: التتبع الذكي للأخبار...")
                tracked_articles = await intelligent_news_tracker.analyze_and_track_news(articles_to_process)
                
                # إحصائيات التتبع
                important_articles = [a for a in tracked_articles if a.get('importance_score', 0) >= 70]
                self.processing_stats['important_stories_found'] += len(important_articles)
                
                logger.info(f"📊 تم اكتشاف {len(important_articles)} مقال مهم من {len(tracked_articles)}")
            else:
                tracked_articles = articles_to_process
            
            # المرحلة 2: معالجة كل مقال
            for i, article in enumerate(tracked_articles):
                try:
                    logger.info(f"🔄 معالجة المقال {i+1}/{len(tracked_articles)}: {article.get('title', '')[:50]}...")
                    
                    # معالجة المقال
                    processed_article = await self._process_single_article(article)
                    
                    if processed_article:
                        processed_articles.append(processed_article)
                        self.processing_stats['articles_processed'] += 1
                    
                    # تأخير بين المقالات لتجنب الحمل الزائد
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في معالجة مقال: {e}")
                    # إضافة المقال الأصلي في حالة الفشل
                    processed_articles.append(article)
                    continue
            
            # المرحلة 3: التحليل النهائي والتحسين
            final_articles = await self._finalize_processing(processed_articles)
            
            # تحديث الإحصائيات
            processing_time = time.time() - start_time
            self.processing_stats['total_processing_time'] += processing_time
            
            # تسجيل التحليلات
            await self._record_processing_analytics(len(articles), len(final_articles), processing_time)
            
            logger.info(f"✅ اكتملت المعالجة الذكية: {len(final_articles)} مقال في {processing_time:.2f} ثانية")
            return final_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في المعالجة الذكية: {e}")
            return articles  # إرجاع المقالات الأصلية في حالة الفشل
    
    async def _process_single_article(self, article: Dict) -> Optional[Dict]:
        """معالجة مقال واحد"""
        try:
            processed_article = article.copy()
            
            # إضافة معلومات المعالجة
            processed_article['processing_info'] = {
                'processed_at': datetime.now().isoformat(),
                'processor_version': '2.0',
                'enhancements_applied': []
            }
            
            # تحسين المحتوى إذا كان المقال مهم
            importance_score = article.get('importance_score', 0)
            
            if importance_score >= 70:
                logger.info(f"⭐ مقال مهم - تطبيق تحسينات متقدمة...")
                
                # تحسين المحتوى
                enhanced_content = await self._enhance_article_content(article)
                if enhanced_content:
                    processed_article.update(enhanced_content)
                    processed_article['processing_info']['enhancements_applied'].append('content_enhancement')
            
            # توليد الصور (حد أقصى 3 صور)
            if self.settings['enable_enhanced_images']:
                image_result = await self._generate_article_images(processed_article)
                
                if image_result and image_result.get('images_generated', 0) > 0:
                    processed_article.update(image_result)
                    processed_article['processing_info']['enhancements_applied'].append('image_generation')
                    self.processing_stats['images_generated'] += image_result.get('images_generated', 0)
            
            # تحسين البيانات الوصفية
            processed_article = self._enhance_metadata(processed_article)
            processed_article['processing_info']['enhancements_applied'].append('metadata_enhancement')
            
            # فحص الجودة النهائية
            quality_score = self._calculate_final_quality_score(processed_article)
            processed_article['final_quality_score'] = quality_score
            
            if quality_score >= self.settings['quality_threshold']:
                return processed_article
            else:
                logger.warning(f"⚠️ جودة المقال منخفضة: {quality_score:.1f}")
                return processed_article  # إرجاع المقال حتى لو كانت الجودة منخفضة
            
        except Exception as e:
            logger.error(f"❌ فشل في معالجة مقال واحد: {e}")
            return article
    
    async def _enhance_article_content(self, article: Dict) -> Optional[Dict]:
        """تحسين محتوى المقال"""
        try:
            enhancements = {}
            
            # إضافة معلومات السياق إذا كانت متوفرة
            additional_info = article.get('additional_info', [])
            if additional_info:
                # دمج المعلومات الإضافية بشكل أفضل
                enhanced_content = self._merge_additional_context(article, additional_info)
                enhancements['content'] = enhanced_content
            
            # تحسين العنوان إذا لزم الأمر
            original_title = article.get('title', '')
            enhanced_title = self._enhance_title(original_title, article)
            if enhanced_title != original_title:
                enhancements['title'] = enhanced_title
            
            # إضافة علامات (tags) ذكية
            smart_tags = self._generate_smart_tags(article)
            if smart_tags:
                enhancements['tags'] = smart_tags
            
            return enhancements if enhancements else None
            
        except Exception as e:
            logger.debug(f"فشل في تحسين محتوى المقال: {e}")
            return None
    
    def _merge_additional_context(self, article: Dict, additional_info: List[Dict]) -> str:
        """دمج المعلومات الإضافية في السياق"""
        try:
            original_content = article.get('content', '')
            
            # إنشاء قسم منظم للمعلومات الإضافية
            context_section = "\n\n## السياق والمعلومات الإضافية:\n\n"
            
            for i, info in enumerate(additional_info[:3], 1):  # أقصى 3 معلومات
                context_section += f"### {i}. {info.get('title', 'معلومة إضافية')}\n"
                context_section += f"**المصدر:** {info.get('source', 'غير محدد')}\n"
                context_section += f"{info.get('summary', '')}\n\n"
            
            return original_content + context_section
            
        except Exception as e:
            logger.debug(f"فشل في دمج السياق الإضافي: {e}")
            return article.get('content', '')
    
    def _enhance_title(self, title: str, article: Dict) -> str:
        """تحسين العنوان"""
        try:
            # إضافة مؤشرات الأهمية للعناوين المهمة
            importance_score = article.get('importance_score', 0)
            
            if importance_score >= 90:
                if not any(indicator in title.lower() for indicator in ['breaking', 'urgent', 'exclusive']):
                    return f"🔥 {title}"
            elif importance_score >= 80:
                if not any(indicator in title.lower() for indicator in ['new', 'latest', 'update']):
                    return f"⭐ {title}"
            
            return title
            
        except Exception as e:
            logger.debug(f"فشل في تحسين العنوان: {e}")
            return title
    
    def _generate_smart_tags(self, article: Dict) -> List[str]:
        """توليد علامات ذكية للمقال"""
        try:
            import re
            
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            text = f"{title} {content}"
            
            tags = []
            
            # علامات نوع المحتوى
            content_types = {
                'news': ['news', 'announcement', 'report'],
                'review': ['review', 'rating', 'score'],
                'update': ['update', 'patch', 'version'],
                'release': ['release', 'launch', 'available'],
                'trailer': ['trailer', 'video', 'footage'],
                'leak': ['leak', 'rumor', 'speculation']
            }
            
            for tag, keywords in content_types.items():
                if any(keyword in text for keyword in keywords):
                    tags.append(tag)
            
            # علامات المنصات
            platforms = ['pc', 'xbox', 'playstation', 'nintendo', 'mobile', 'steam']
            for platform in platforms:
                if platform in text:
                    tags.append(platform)
            
            # علامات أنواع الألعاب
            genres = ['rpg', 'fps', 'strategy', 'racing', 'sports', 'puzzle', 'adventure', 'action']
            for genre in genres:
                if genre in text:
                    tags.append(genre)
            
            # إزالة التكرار وترتيب
            unique_tags = list(set(tags))
            return unique_tags[:8]  # أقصى 8 علامات
            
        except Exception as e:
            logger.debug(f"فشل في توليد العلامات الذكية: {e}")
            return []
    
    async def _generate_article_images(self, article: Dict) -> Optional[Dict]:
        """توليد صور للمقال (حد أقصى 3 صور)"""
        try:
            # استخدام مدير الصور المحسن
            image_result = await enhanced_image_manager.generate_images_for_article(article)
            
            if image_result and image_result.get('images_generated', 0) > 0:
                logger.info(f"🎨 تم توليد {image_result['images_generated']} صورة للمقال")
                return image_result
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في توليد صور المقال: {e}")
            return None
    
    def _enhance_metadata(self, article: Dict) -> Dict:
        """تحسين البيانات الوصفية"""
        try:
            enhanced_article = article.copy()
            
            # إضافة معلومات التصنيف
            enhanced_article['category'] = self._determine_article_category(article)
            
            # إضافة مستوى الأولوية
            enhanced_article['priority_level'] = self._determine_priority_level(article)
            
            # إضافة تقدير وقت القراءة
            enhanced_article['estimated_read_time'] = self._calculate_read_time(article)
            
            # إضافة نقاط SEO
            enhanced_article['seo_score'] = self._calculate_seo_score(article)
            
            return enhanced_article
            
        except Exception as e:
            logger.debug(f"فشل في تحسين البيانات الوصفية: {e}")
            return article
    
    def _determine_article_category(self, article: Dict) -> str:
        """تحديد فئة المقال"""
        try:
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            text = f"{title} {content}"
            
            categories = {
                'breaking_news': ['breaking', 'urgent', 'just in'],
                'reviews': ['review', 'rating', 'score', 'verdict'],
                'updates': ['update', 'patch', 'version', 'changelog'],
                'releases': ['release', 'launch', 'available', 'coming'],
                'trailers': ['trailer', 'video', 'footage', 'gameplay'],
                'industry': ['industry', 'business', 'company', 'financial'],
                'esports': ['esports', 'tournament', 'championship', 'competitive']
            }
            
            for category, keywords in categories.items():
                if any(keyword in text for keyword in keywords):
                    return category
            
            return 'general'
            
        except Exception as e:
            logger.debug(f"فشل في تحديد فئة المقال: {e}")
            return 'general'
    
    def _determine_priority_level(self, article: Dict) -> str:
        """تحديد مستوى الأولوية"""
        importance_score = article.get('importance_score', 0)
        
        if importance_score >= 90:
            return 'critical'
        elif importance_score >= 80:
            return 'high'
        elif importance_score >= 60:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_read_time(self, article: Dict) -> int:
        """حساب تقدير وقت القراءة (بالدقائق)"""
        try:
            content = article.get('content', '')
            word_count = len(content.split())
            
            # متوسط 200 كلمة في الدقيقة
            read_time = max(1, round(word_count / 200))
            
            return read_time
            
        except Exception as e:
            logger.debug(f"فشل في حساب وقت القراءة: {e}")
            return 1
    
    def _calculate_seo_score(self, article: Dict) -> float:
        """حساب نقاط SEO"""
        try:
            score = 0.0
            
            title = article.get('title', '')
            content = article.get('content', '')
            
            # طول العنوان المناسب - استخدام الحدود الجديدة
            from config.settings import SEOConfig
            if SEOConfig.TITLE_LENGTH_MIN <= len(title) <= SEOConfig.TITLE_LENGTH_MAX:
                score += 20
            
            # طول المحتوى المناسب
            if len(content) >= 300:
                score += 20
            
            # وجود كلمات مفتاحية
            if article.get('tags'):
                score += 15
            
            # وجود صور
            if article.get('image_urls'):
                score += 15
            
            # جودة المحتوى
            quality_score = article.get('final_quality_score', 0)
            score += (quality_score / 100) * 30
            
            return min(100.0, score)
            
        except Exception as e:
            logger.debug(f"فشل في حساب نقاط SEO: {e}")
            return 50.0
    
    def _calculate_final_quality_score(self, article: Dict) -> float:
        """حساب نقاط الجودة النهائية"""
        try:
            score = 0.0
            
            # نقاط المحتوى الأساسية
            base_quality = article.get('quality_score', 0)
            score += base_quality * 0.4
            
            # نقاط الأهمية
            importance = article.get('importance_score', 0)
            score += importance * 0.3
            
            # نقاط التحسينات
            enhancements = article.get('processing_info', {}).get('enhancements_applied', [])
            score += len(enhancements) * 5
            
            # نقاط الصور
            if article.get('images_generated', 0) > 0:
                score += 10
            
            # نقاط البيانات الوصفية
            if article.get('tags'):
                score += 5
            
            return min(100.0, score)
            
        except Exception as e:
            logger.debug(f"فشل في حساب الجودة النهائية: {e}")
            return 50.0
    
    async def _finalize_processing(self, articles: List[Dict]) -> List[Dict]:
        """الانتهاء من المعالجة والتحسين النهائي"""
        try:
            # ترتيب المقالات حسب الأولوية والجودة
            sorted_articles = sorted(
                articles,
                key=lambda x: (
                    x.get('importance_score', 0) * 0.6 + 
                    x.get('final_quality_score', 0) * 0.4
                ),
                reverse=True
            )
            
            # إضافة ترقيم الأولوية
            for i, article in enumerate(sorted_articles):
                article['display_order'] = i + 1
                article['processing_completed'] = True
            
            return sorted_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في الانتهاء من المعالجة: {e}")
            return articles
    
    async def _record_processing_analytics(self, input_count: int, output_count: int, processing_time: float):
        """تسجيل تحليلات المعالجة"""
        try:
            analytics = SearchAnalytics(
                query="content_processing",
                timestamp=time.time(),
                search_engine="intelligent_processor",
                results_count=output_count,
                response_time=processing_time,
                cache_hit=False,
                quality_score=85.0,
                cost=0.0,
                success=output_count > 0
            )
            
            search_analytics.record_search(analytics)
            
        except Exception as e:
            logger.debug(f"فشل في تسجيل تحليلات المعالجة: {e}")
    
    def get_processing_stats(self) -> Dict:
        """الحصول على إحصائيات المعالجة"""
        try:
            # إحصائيات التتبع
            tracking_stats = intelligent_news_tracker.get_tracking_stats()
            
            # إحصائيات الصور
            image_stats = enhanced_image_manager.get_daily_stats()
            
            return {
                'processing_stats': self.processing_stats,
                'tracking_stats': tracking_stats,
                'image_stats': image_stats,
                'settings': self.settings
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع إحصائيات المعالجة: {e}")
            return {}

# إنشاء مثيل عام
intelligent_content_processor = IntelligentContentProcessor()
