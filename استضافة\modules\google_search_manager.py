#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير مفاتيح Google Search API المتقدم
"""

import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from .logger import logger
from .api_key_manager import ApiKeyManager

class GoogleSearchManager:
    """مدير متقدم لمفاتيح Google Search API مع ميزات إضافية"""
    
    def __init__(self, api_keys: List[str], search_engine_id: str):
        """
        تهيئة مدير Google Search
        :param api_keys: قائمة مفاتيح Google Search API
        :param search_engine_id: معرف محرك البحث المخصص
        """
        if not api_keys:
            raise ValueError("يجب توفير مفاتيح API على الأقل")
        
        self.search_engine_id = search_engine_id
        self.api_manager = ApiKeyManager(
            api_keys=api_keys,
            service_name="Google Search",
            auto_recovery_minutes=30,  # إعادة تفعيل سريعة
            load_balancing=True
        )
        
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        self.daily_quota_per_key = 100  # الحد اليومي لكل مفتاح
        self.search_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'quota_exceeded_count': 0,
            'last_reset': datetime.now()
        }
        
        logger.info(f"🔍 تم تهيئة مدير Google Search مع {len(api_keys)} مفتاح")
    
    async def search(self, query: str, num_results: int = 10, **kwargs) -> List[Dict]:
        """
        البحث باستخدام Google Custom Search API
        :param query: استعلام البحث
        :param num_results: عدد النتائج المطلوبة
        :param kwargs: معاملات إضافية للبحث
        :return: قائمة النتائج
        """
        try:
            self.search_stats['total_searches'] += 1
            
            # إعداد معاملات البحث
            params = {
                'key': self.api_manager.get_key(),
                'cx': self.search_engine_id,
                'q': query,
                'num': min(num_results, 10),  # Google limit
                **kwargs
            }
            
            # إضافة معاملات افتراضية للألعاب
            if 'dateRestrict' not in params:
                params['dateRestrict'] = 'w1'  # الأسبوع الماضي
            if 'sort' not in params:
                params['sort'] = 'date'
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.base_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._process_search_results(data)
                        
                        self.search_stats['successful_searches'] += 1
                        logger.info(f"🔍 Google Search نجح: {len(results)} نتيجة لـ '{query}'")
                        
                        return results
                    
                    elif response.status == 403:
                        logger.warning(f"⚠️ خطأ 403 - تم تجاوز الحصة أو مفتاح غير صالح")
                        return await self._handle_quota_exceeded(query, num_results, **kwargs)
                    
                    elif response.status == 429:
                        logger.warning(f"⚠️ خطأ 429 - تم تجاوز حد الطلبات")
                        return await self._handle_rate_limit(query, num_results, **kwargs)
                    
                    else:
                        logger.error(f"❌ خطأ غير متوقع في Google Search: {response.status}")
                        self.search_stats['failed_searches'] += 1
                        return []
        
        except Exception as e:
            logger.error(f"❌ خطأ في Google Search: {e}")
            self.search_stats['failed_searches'] += 1
            return []
    
    async def _handle_quota_exceeded(self, query: str, num_results: int, **kwargs) -> List[Dict]:
        """معالجة تجاوز الحصة"""
        try:
            self.search_stats['quota_exceeded_count'] += 1
            
            # تبديل المفتاح
            self.api_manager.rotate_key()
            logger.info("🔄 تم تبديل مفتاح Google Search API")
            
            # إعادة المحاولة مرة واحدة
            if self.api_manager.get_available_keys_count() > 0:
                return await self.search(query, num_results, **kwargs)
            else:
                logger.critical("🚨 تم استنفاد جميع مفاتيح Google Search API")
                return []
                
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة تجاوز الحصة: {e}")
            return []
    
    async def _handle_rate_limit(self, query: str, num_results: int, **kwargs) -> List[Dict]:
        """معالجة تجاوز حد الطلبات"""
        try:
            # تبديل المفتاح وعدم إعادة المحاولة فوراً
            self.api_manager.rotate_key()
            logger.info("🔄 تم تبديل مفتاح Google Search API بسبب تجاوز حد الطلبات")
            
            # تأخير قصير
            await asyncio.sleep(2)
            
            return []  # لا نعيد المحاولة فوراً في حالة 429
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة تجاوز حد الطلبات: {e}")
            return []
    
    def _process_search_results(self, data: Dict) -> List[Dict]:
        """معالجة نتائج البحث"""
        results = []
        
        if not data.get('items'):
            return results
        
        for item in data['items']:
            try:
                result = {
                    'title': item.get('title', ''),
                    'link': item.get('link', ''),
                    'snippet': item.get('snippet', ''),
                    'displayLink': item.get('displayLink', ''),
                    'formattedUrl': item.get('formattedUrl', ''),
                    'source': 'Google Search',
                    'search_time': datetime.now(),
                    'relevance_score': self._calculate_relevance(item)
                }
                
                # إضافة معلومات إضافية إذا متوفرة
                if 'pagemap' in item:
                    pagemap = item['pagemap']
                    if 'metatags' in pagemap and pagemap['metatags']:
                        metatag = pagemap['metatags'][0]
                        result['meta_description'] = metatag.get('description', '')
                        result['meta_keywords'] = metatag.get('keywords', '')
                
                results.append(result)
                
            except Exception as e:
                logger.warning(f"⚠️ خطأ في معالجة نتيجة بحث: {e}")
                continue
        
        return results
    
    def _calculate_relevance(self, item: Dict) -> float:
        """حساب درجة الصلة للنتيجة"""
        score = 50.0  # نقطة البداية
        
        title = item.get('title', '').lower()
        snippet = item.get('snippet', '').lower()
        
        # كلمات مفتاحية للألعاب
        gaming_keywords = [
            'game', 'gaming', 'video game', 'esports', 'console',
            'pc gaming', 'mobile game', 'indie game', 'aaa game',
            'gameplay', 'review', 'trailer', 'release', 'update'
        ]
        
        # زيادة النقاط للكلمات المفتاحية في العنوان
        for keyword in gaming_keywords:
            if keyword in title:
                score += 10
            if keyword in snippet:
                score += 5
        
        # تقليل النقاط للمحتوى غير المرغوب
        unwanted_keywords = ['casino', 'gambling', 'adult', 'porn']
        for keyword in unwanted_keywords:
            if keyword in title or keyword in snippet:
                score -= 20
        
        return min(max(score, 0), 100)  # بين 0 و 100
    
    def get_search_statistics(self) -> Dict:
        """الحصول على إحصائيات البحث"""
        api_stats = self.api_manager.get_usage_stats()
        
        return {
            'search_stats': self.search_stats,
            'api_key_stats': api_stats,
            'performance_metrics': {
                'success_rate': (
                    self.search_stats['successful_searches'] / 
                    max(self.search_stats['total_searches'], 1)
                ) * 100,
                'average_quota_usage': (
                    self.search_stats['total_searches'] / 
                    max(len(self.api_manager.keys), 1)
                ),
                'quota_exceeded_rate': (
                    self.search_stats['quota_exceeded_count'] / 
                    max(self.search_stats['total_searches'], 1)
                ) * 100
            }
        }
    
    def reset_statistics(self):
        """إعادة تعيين الإحصائيات"""
        self.search_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'quota_exceeded_count': 0,
            'last_reset': datetime.now()
        }
        logger.info("📊 تم إعادة تعيين إحصائيات Google Search")
    
    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة"""
        return len(self.api_manager.keys) - len(self.api_manager.blacklisted_keys)
    
    def reset_key_failures(self, key: str = None):
        """إعادة تعيين فشل المفاتيح"""
        self.api_manager.reset_key_failures(key)
    
    async def test_all_keys(self) -> Dict:
        """اختبار جميع المفاتيح"""
        test_results = {}
        test_query = "gaming news test"
        
        for i, key in enumerate(self.api_manager.keys):
            try:
                params = {
                    'key': key,
                    'cx': self.search_engine_id,
                    'q': test_query,
                    'num': 1
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.base_url, params=params) as response:
                        masked_key = f"{key[:8]}...{key[-8:]}"
                        
                        if response.status == 200:
                            test_results[masked_key] = {
                                'status': 'working',
                                'response_code': 200,
                                'message': 'المفتاح يعمل بشكل طبيعي'
                            }
                        else:
                            test_results[masked_key] = {
                                'status': 'error',
                                'response_code': response.status,
                                'message': f'خطأ HTTP {response.status}'
                            }
                
                # تأخير بين الاختبارات
                await asyncio.sleep(1)
                
            except Exception as e:
                masked_key = f"{key[:8]}...{key[-8:]}"
                test_results[masked_key] = {
                    'status': 'error',
                    'response_code': 0,
                    'message': f'خطأ: {str(e)}'
                }
        
        return test_results

# إنشاء مثيل عام (سيتم تهيئته في settings.py)
google_search_manager = None
