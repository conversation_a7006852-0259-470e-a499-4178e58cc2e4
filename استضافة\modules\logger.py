# نظام التسجيل المتقدم للوكيل البرمجي
import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
import sys

class MinecraftBotLogger:
    """نظام تسجيل احترافي للوكيل البرمجي"""
    
    def __init__(self, log_file: str = "logs/bot.log", log_level: str = "INFO"):
        self.log_file = log_file
        self.log_level = getattr(logging, log_level.upper())
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self):
        """إعداد نظام التسجيل"""
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # إنشاء المسجل الرئيسي
        self.logger = logging.getLogger("minecraft_bot")
        self.logger.setLevel(self.log_level)
        
        # منع التكرار في حالة الاستدعاء المتعدد
        if self.logger.handlers:
            return
        
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # معالج الملفات مع التدوير التلقائي
        file_handler = RotatingFileHandler(
            self.log_file,
            maxBytes=10*1024*1024,  # 10 ميجابايت
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)

        # معالج الكونسول
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)

        # إضافة المعالجات
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str, extra_data: dict = None):
        """تسجيل رسالة معلومات"""
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.info(message)
    
    def warning(self, message: str, extra_data: dict = None):
        """تسجيل رسالة تحذير"""
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.warning(message)
    
    def error(self, message: str, error: Exception = None, extra_data: dict = None):
        """تسجيل رسالة خطأ"""
        if error:
            message = f"{message} | نوع الخطأ: {type(error).__name__} | التفاصيل: {str(error)}"
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.error(message)
    
    def critical(self, message: str, error: Exception = None, extra_data: dict = None):
        """تسجيل رسالة خطأ حرج"""
        if error:
            message = f"{message} | نوع الخطأ: {type(error).__name__} | التفاصيل: {str(error)}"
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.critical(message)
    
    def debug(self, message: str, extra_data: dict = None):
        """تسجيل رسالة تصحيح الأخطاء"""
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.debug(message)
    
    def log_api_call(self, api_name: str, endpoint: str, status: str, response_time: float = None):
        """تسجيل استدعاءات API"""
        message = f"استدعاء API: {api_name} | النقطة: {endpoint} | الحالة: {status}"
        if response_time:
            message += f" | وقت الاستجابة: {response_time:.2f}ث"
        self.info(message)
    
    def log_content_processing(self, content_type: str, source: str, action: str, status: str):
        """تسجيل معالجة المحتوى"""
        message = f"معالجة المحتوى: {content_type} | المصدر: {source} | الإجراء: {action} | الحالة: {status}"
        self.info(message)
    
    def log_publishing(self, platform: str, content_title: str, status: str, article_url: str = None):
        """تسجيل عمليات النشر"""
        message = f"النشر: {platform} | العنوان: {content_title} | الحالة: {status}"
        if article_url:
            message += f" | الرابط: {article_url}"
        self.info(message)
    
    def log_bot_status(self, status: str, uptime: str = None, processed_articles: int = None):
        """تسجيل حالة البوت"""
        message = f"حالة البوت: {status}"
        if uptime:
            message += f" | مدة التشغيل: {uptime}"
        if processed_articles is not None:
            message += f" | المقالات المعالجة: {processed_articles}"
        self.info(message)
    
    def start_session_log(self):
        """بدء جلسة تسجيل جديدة"""
        self.info("=" * 80)
        self.info("🚀 بدء تشغيل وكيل أخبار ماين كرافت")
        self.info(f"⏰ الوقت الحالي: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info("=" * 80)
    
    def end_session_log(self):
        """إنهاء جلسة التسجيل"""
        self.info("=" * 80)
        self.info("🛑 إيقاف وكيل أخبار ماين كرافت")
        self.info(f"⏰ الوقت الحالي: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info("=" * 80)

# إنشاء مثيل عام للمسجل
logger = MinecraftBotLogger()
