# نظام تحليل صور الألعاب المحسن باستخدام Gemini 2.5 Pro
import asyncio
import aiohttp
import json
import re
import base64
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import hashlib
import os

from modules.logger import logger
from config.settings import google_api_manager

class GameImageAnalyzer:
    """محلل صور الألعاب المحسن باستخدام Gemini 2.5 Pro لفهم الشكل البصري وإنشاء صور مشابهة"""

    def __init__(self):
        self.cache_dir = "cache/game_images"
        self.analysis_cache_file = "cache/game_image_analysis.json"

        # إعدادات Gemini 2.5 Pro
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"

        # إنشاء المجلدات
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs("cache", exist_ok=True)

        # تحميل التخزين المؤقت
        self.analysis_cache = self._load_analysis_cache()

        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_analyses': 0,
            'gemini_analyses': 0,
            'fallback_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'cache_hits': 0,
            'daily_usage': 0,
            'last_reset': datetime.now().date()
        }
        
        # قوالب تحليل الألعاب حسب النوع
        self.game_style_templates = {
            'action': {
                'visual_style': 'dynamic action scenes with explosions and combat',
                'color_palette': 'dark tones with bright highlights, red and orange accents',
                'composition': 'diagonal compositions with movement and energy',
                'characters': 'heroic poses, weapons, armor details'
            },
            'rpg': {
                'visual_style': 'fantasy landscapes with magical elements',
                'color_palette': 'rich blues, purples, golds, mystical lighting',
                'composition': 'epic wide shots with detailed environments',
                'characters': 'detailed character designs, magical effects'
            },
            'racing': {
                'visual_style': 'sleek vehicles and racing environments',
                'color_palette': 'metallic colors, bright neons, speed blur effects',
                'composition': 'dynamic angles showing speed and motion',
                'characters': 'focus on vehicles rather than people'
            },
            'sports': {
                'visual_style': 'athletic action and stadium environments',
                'color_palette': 'team colors, grass greens, bright lighting',
                'composition': 'action shots capturing peak moments',
                'characters': 'athletes in motion, sports equipment'
            },
            'strategy': {
                'visual_style': 'tactical overview with detailed units',
                'color_palette': 'earth tones, military colors, strategic blues',
                'composition': 'top-down or isometric views',
                'characters': 'armies, buildings, strategic elements'
            },
            'horror': {
                'visual_style': 'dark atmospheric scenes with tension',
                'color_palette': 'dark grays, blood reds, eerie lighting',
                'composition': 'claustrophobic or ominous wide shots',
                'characters': 'mysterious figures, monsters, shadows'
            },
            'puzzle': {
                'visual_style': 'clean geometric designs with bright colors',
                'color_palette': 'bright primary colors, clean whites',
                'composition': 'organized layouts with clear patterns',
                'characters': 'abstract shapes, minimal character design'
            }
        }
        
        # قوالب تحليل Gemini المتقدمة
        self.gemini_analysis_prompt = """
        Analyze this gaming article to extract visual style information for creating similar images.

        Provide analysis in this EXACT JSON format:
        {
            "game_name": "Extracted game name or 'Unknown'",
            "genre": "Game genre (action, rpg, racing, sports, strategy, horror, puzzle, etc.)",
            "visual_style": "Detailed description of visual style and art direction",
            "color_palette": "Main colors and color scheme description",
            "composition": "Layout and composition style description",
            "characters": "Character design and visual elements description",
            "mood": "Overall mood and atmosphere",
            "art_direction": "Specific art direction notes",
            "creation_prompt": "Detailed prompt for creating similar gaming image",
            "confidence_score": 0.0-1.0
        }

        Article Title: {title}
        Article Content: {content}
        Keywords: {keywords}
        """

        logger.info(f"🎮 تم تهيئة محلل صور الألعاب المحسن - Gemini: {'مفعل' if self.enabled else 'معطل'}")
    
    def _load_analysis_cache(self) -> Dict:
        """تحميل التخزين المؤقت لتحليل الصور"""
        try:
            if os.path.exists(self.analysis_cache_file):
                with open(self.analysis_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل تخزين تحليل الصور: {e}")
        
        return {}
    
    def _save_analysis_cache(self):
        """حفظ التخزين المؤقت لتحليل الصور"""
        try:
            with open(self.analysis_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ فشل في حفظ تخزين تحليل الصور: {e}")
    
    async def analyze_game_for_image_generation(self, article: Dict) -> Dict:
        """تحليل اللعبة من المقال لإنشاء صورة مناسبة باستخدام Gemini 2.5 Pro"""
        try:
            self.usage_stats['total_analyses'] += 1
            self.usage_stats['daily_usage'] += 1

            # إنشاء مفتاح تخزين مؤقت
            article_text = f"{article.get('title', '')} {article.get('content', '')[:500]}"
            cache_key = hashlib.md5(article_text.encode()).hexdigest()

            # فحص التخزين المؤقت أولاً
            if cache_key in self.analysis_cache:
                self.usage_stats['cache_hits'] += 1
                logger.info(f"📦 استخدام تحليل مخزن مؤقتاً")
                return self.analysis_cache[cache_key]

            # محاولة التحليل باستخدام Gemini 2.5 Pro أولاً
            if self.enabled:
                gemini_result = await self._analyze_with_gemini(article)
                if gemini_result and gemini_result.get('confidence_score', 0) > 0.6:
                    self.usage_stats['gemini_analyses'] += 1
                    self.usage_stats['successful_analyses'] += 1

                    # حفظ في التخزين المؤقت
                    self.analysis_cache[cache_key] = gemini_result
                    self._save_analysis_cache()

                    logger.info(f"✅ تم تحليل اللعبة بنجاح باستخدام Gemini: {gemini_result.get('game_name', 'Unknown')}")
                    return gemini_result
                else:
                    logger.warning("⚠️ تحليل Gemini لم ينجح، التحول للطريقة التقليدية")

            # الطريقة التقليدية كبديل
            fallback_result = await self._analyze_with_fallback_method(article)
            self.usage_stats['fallback_analyses'] += 1

            if fallback_result:
                self.usage_stats['successful_analyses'] += 1
                # حفظ في التخزين المؤقت
                self.analysis_cache[cache_key] = fallback_result
                self._save_analysis_cache()

                logger.info(f"✅ تم تحليل اللعبة بالطريقة التقليدية: {fallback_result.get('game_name', 'Unknown')}")
                return fallback_result
            else:
                self.usage_stats['failed_analyses'] += 1
                logger.warning("⚠️ فشل في جميع طرق التحليل، استخدام النمط العام")
                return self._get_generic_gaming_style()

        except Exception as e:
            self.usage_stats['failed_analyses'] += 1
            logger.error(f"❌ فشل في تحليل اللعبة: {e}")
            return self._get_generic_gaming_style()

    async def _analyze_with_gemini(self, article: Dict) -> Optional[Dict]:
        """تحليل المقال باستخدام Gemini 2.5 Pro"""
        try:
            logger.info("🤖 بدء التحليل باستخدام Gemini 2.5 Pro...")

            # إعداد النص للتحليل
            title = article.get('title', '')
            content = article.get('content', '')[:2000]  # تحديد الطول لتجنب تجاوز الحدود
            keywords = ', '.join(article.get('keywords', []))

            # إنشاء prompt مخصص
            analysis_prompt = self.gemini_analysis_prompt.format(
                title=title,
                content=content,
                keywords=keywords
            )

            # إرسال الطلب إلى Gemini
            url = f"{self.base_url}/models/{self.model_name}:generateContent"

            headers = {
                'Content-Type': 'application/json'
            }

            params = {
                'key': google_api_manager.get_key()
            }

            payload = {
                "contents": [{
                    "parts": [{
                        "text": analysis_prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.3,  # دقة عالية للتحليل
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": 2048,
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=60) as response:

                    if response.status == 200:
                        data = await response.json()

                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])

                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text'].strip()

                                # معالجة الاستجابة
                                return self._process_gemini_response(response_text, article)

                    else:
                        logger.warning(f"⚠️ خطأ في Gemini API: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ فشل في التحليل باستخدام Gemini: {e}")
            return None

    def _process_gemini_response(self, response_text: str, article: Dict) -> Optional[Dict]:
        """معالجة استجابة Gemini وتحويلها إلى تنسيق مناسب"""
        try:
            logger.info(f"🔍 معالجة استجابة Gemini: {response_text[:200]}...")

            # تنظيف النص أولاً
            cleaned_text = response_text.strip()

            # محاولة استخراج JSON من الاستجابة
            json_start = cleaned_text.find('{')
            json_end = cleaned_text.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_text = cleaned_text[json_start:json_end]

                # تنظيف JSON من الأخطاء الشائعة
                json_text = json_text.replace('\n', ' ').replace('\r', ' ')
                json_text = ' '.join(json_text.split())  # إزالة المسافات الزائدة

                logger.info(f"🔍 محاولة تحليل JSON: {json_text[:100]}...")

                analysis_data = json.loads(json_text)

                # تحويل إلى التنسيق المطلوب
                result = {
                    'game_name': analysis_data.get('game_name', 'Unknown'),
                    'visual_style': {
                        'visual_style': analysis_data.get('visual_style', 'modern gaming artwork'),
                        'color_palette': analysis_data.get('color_palette', 'vibrant gaming colors'),
                        'composition': analysis_data.get('composition', 'dynamic composition'),
                        'characters': analysis_data.get('characters', 'gaming elements'),
                        'genre': analysis_data.get('genre', 'action'),
                        'mood': analysis_data.get('mood', 'exciting'),
                        'art_direction': analysis_data.get('art_direction', 'professional'),
                        'confidence': analysis_data.get('confidence_score', 0.8)
                    },
                    'enhanced_prompt': analysis_data.get('creation_prompt', ''),
                    'analysis_date': datetime.now().isoformat(),
                    'confidence_score': analysis_data.get('confidence_score', 0.8),
                    'analysis_method': 'gemini_2.5_pro'
                }

                return result

            else:
                # إذا لم يكن JSON، حاول استخراج المعلومات من النص
                logger.warning("⚠️ لم يتم العثور على JSON صالح، محاولة استخراج بديلة")
                return self._extract_from_text_response(response_text, article)

        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ خطأ في تحليل JSON من Gemini: {e}")
            logger.info(f"🔍 النص الذي فشل في التحليل: {json_text[:200] if 'json_text' in locals() else response_text[:200]}")
            return self._extract_from_text_response(response_text, article)
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة Gemini: {e}")
            logger.info(f"🔍 النص الأصلي: {response_text[:200]}")
            return self._extract_from_text_response(response_text, article)

    def _extract_from_text_response(self, text: str, article: Dict) -> Optional[Dict]:
        """استخراج المعلومات من النص العادي كبديل"""
        try:
            logger.info("🔍 استخراج المعلومات من النص العادي...")

            # استخراج اسم اللعبة من المقال أولاً
            game_name = self._extract_game_name(article) or 'Unknown Game'

            # تحليل النوع من النص والمقال
            genre = 'action'
            article_text = f"{article.get('title', '')} {article.get('content', '')}".lower()

            # تحديد النوع بناءً على الكلمات المفتاحية
            genre_keywords = {
                'action': ['action', 'fight', 'combat', 'battle', 'shooter', 'gun', 'weapon', 'call of duty', 'fps'],
                'rpg': ['rpg', 'adventure', 'magic', 'fantasy', 'dragon', 'wizard', 'zelda', 'witcher'],
                'racing': ['racing', 'car', 'speed', 'drive', 'motor', 'formula'],
                'sports': ['sports', 'football', 'soccer', 'basketball', 'tennis', 'fifa'],
                'strategy': ['strategy', 'empire', 'civilization', 'tactical'],
                'horror': ['horror', 'zombie', 'dead', 'evil', 'dark', 'resident evil'],
                'puzzle': ['puzzle', 'brain', 'logic', 'match']
            }

            for g, keywords in genre_keywords.items():
                if any(keyword in article_text for keyword in keywords):
                    genre = g
                    break

            # إنشاء وصف بصري بناءً على النوع
            visual_descriptions = {
                'action': 'dynamic action scenes with intense combat and dramatic lighting',
                'rpg': 'fantasy landscapes with magical elements and rich detailed environments',
                'racing': 'sleek vehicles with speed effects and dynamic motion blur',
                'sports': 'athletic action in stadium environments with vibrant team colors',
                'strategy': 'tactical overview with detailed units and strategic elements',
                'horror': 'dark atmospheric scenes with eerie lighting and tension',
                'puzzle': 'clean geometric designs with bright organized layouts'
            }

            visual_style = visual_descriptions.get(genre, 'modern gaming artwork with dynamic elements')

            # إنشاء prompt محسن
            creation_prompt = f"Professional gaming artwork for {game_name}, {visual_style}, high quality 4K, vibrant colors, detailed composition, gaming aesthetic, no text overlay"

            # تحديد لوحة الألوان بناءً على النوع
            color_palettes = {
                'action': 'dark tones with bright highlights, red and orange accents',
                'rpg': 'rich blues, purples, golds, mystical lighting',
                'racing': 'metallic colors, bright neons, speed blur effects',
                'sports': 'team colors, grass greens, bright stadium lighting',
                'strategy': 'earth tones, military colors, strategic blues',
                'horror': 'dark grays, blood reds, eerie lighting',
                'puzzle': 'bright primary colors, clean whites'
            }

            color_palette = color_palettes.get(genre, 'vibrant gaming colors with blue and orange accents')

            result = {
                'game_name': game_name,
                'visual_style': {
                    'visual_style': visual_style,
                    'color_palette': color_palette,
                    'composition': 'dynamic gaming composition with professional layout',
                    'characters': 'detailed gaming characters and elements',
                    'genre': genre,
                    'mood': 'exciting and engaging',
                    'art_direction': 'professional gaming artwork',
                    'confidence': 0.8  # ثقة أعلى للطريقة المحسنة
                },
                'enhanced_prompt': creation_prompt,
                'analysis_date': datetime.now().isoformat(),
                'confidence_score': 0.8,  # ثقة أعلى
                'analysis_method': 'enhanced_text_extraction'
            }

            logger.info(f"✅ تم استخراج المعلومات بنجاح: {game_name} ({genre})")
            return result

        except Exception as e:
            logger.error(f"❌ فشل في الاستخراج البديل: {e}")
            return None

    async def _analyze_with_fallback_method(self, article: Dict) -> Optional[Dict]:
        """التحليل بالطريقة التقليدية كبديل"""
        try:
            game_name = self._extract_game_name(article)

            if not game_name:
                game_name = "Gaming Content"

            # البحث عن معلومات اللعبة
            game_info = await self._search_game_information(game_name)

            # تحليل النوع والأسلوب البصري
            visual_analysis = self._analyze_game_visual_style(game_info, article)

            # إنشاء prompt محسن للصورة
            enhanced_prompt = self._create_enhanced_image_prompt(visual_analysis, game_name, article)

            return {
                'game_name': game_name,
                'visual_style': visual_analysis,
                'enhanced_prompt': enhanced_prompt,
                'analysis_date': datetime.now().isoformat(),
                'confidence_score': visual_analysis.get('confidence', 0.7),
                'analysis_method': 'traditional_fallback'
            }

        except Exception as e:
            logger.error(f"❌ فشل في التحليل التقليدي: {e}")
            return None

    def _extract_game_name(self, article: Dict) -> Optional[str]:
        """استخراج اسم اللعبة من المقال"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            
            # البحث عن أنماط أسماء الألعاب
            game_patterns = [
                r'لعبة\s+([^،\s]+(?:\s+[^،\s]+)*)',  # لعبة [اسم]
                r'game\s+([A-Za-z0-9\s]+?)(?:\s|$|[،.])',  # game [name]
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:game|لعبة)',  # [Name] game
                r'"([^"]+)"',  # أي شيء بين علامات اقتباس
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})\s+(?:released|announced|update)',  # أسماء قبل كلمات مفتاحية
            ]
            
            text_to_search = f"{title} {content}"
            
            for pattern in game_patterns:
                matches = re.findall(pattern, text_to_search, re.IGNORECASE)
                if matches:
                    # أخذ أطول اسم وجد
                    game_name = max(matches, key=len).strip()
                    if len(game_name) > 2 and len(game_name) < 50:
                        return game_name
            
            # إذا لم نجد شيء، نحاول استخراج من العنوان مباشرة
            title_words = title.split()
            if len(title_words) >= 2:
                # أخذ أول كلمتين كاسم محتمل للعبة
                potential_name = ' '.join(title_words[:2])
                if not any(word in potential_name.lower() for word in ['news', 'أخبار', 'update', 'تحديث']):
                    return potential_name
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في استخراج اسم اللعبة: {e}")
            return None
    
    async def _search_game_information(self, game_name: str) -> Dict:
        """البحث عن معلومات اللعبة من مصادر مختلفة"""
        try:
            # محاولة البحث في مصادر مختلفة
            game_info = {}
            
            # 1. البحث في قاعدة بيانات الألعاب المحلية (إذا كانت متوفرة)
            local_info = self._search_local_game_database(game_name)
            if local_info:
                game_info.update(local_info)
            
            # 2. تحليل النوع من الاسم والكلمات المفتاحية
            genre_analysis = self._analyze_genre_from_name(game_name)
            game_info.update(genre_analysis)
            
            return game_info
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في البحث عن معلومات اللعبة: {e}")
            return {}
    
    def _search_local_game_database(self, game_name: str) -> Dict:
        """البحث في قاعدة بيانات محلية للألعاب المشهورة"""
        # قاعدة بيانات مبسطة للألعاب المشهورة
        famous_games = {
            'mortal kombat': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'call of duty': {'genre': 'action', 'style': 'fps', 'theme': 'military'},
            'fifa': {'genre': 'sports', 'style': 'football', 'theme': 'soccer'},
            'minecraft': {'genre': 'sandbox', 'style': 'blocky', 'theme': 'creative'},
            'fortnite': {'genre': 'action', 'style': 'battle_royale', 'theme': 'survival'},
            'league of legends': {'genre': 'strategy', 'style': 'moba', 'theme': 'fantasy'},
            'world of warcraft': {'genre': 'rpg', 'style': 'mmorpg', 'theme': 'fantasy'},
            'grand theft auto': {'genre': 'action', 'style': 'open_world', 'theme': 'crime'},
            'the witcher': {'genre': 'rpg', 'style': 'fantasy', 'theme': 'medieval'},
            'cyberpunk': {'genre': 'rpg', 'style': 'futuristic', 'theme': 'cyberpunk'},
            'assassins creed': {'genre': 'action', 'style': 'stealth', 'theme': 'historical'},
            'resident evil': {'genre': 'horror', 'style': 'survival', 'theme': 'zombie'},
            'super mario': {'genre': 'platform', 'style': 'cartoon', 'theme': 'family'},
            'zelda': {'genre': 'rpg', 'style': 'adventure', 'theme': 'fantasy'},
            'pokemon': {'genre': 'rpg', 'style': 'cartoon', 'theme': 'creatures'},
            'street fighter': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'tekken': {'genre': 'action', 'style': 'fighting', 'theme': 'martial_arts'},
            'overwatch': {'genre': 'action', 'style': 'fps', 'theme': 'futuristic'},
            'apex legends': {'genre': 'action', 'style': 'battle_royale', 'theme': 'futuristic'},
            'valorant': {'genre': 'action', 'style': 'fps', 'theme': 'tactical'}
        }
        
        game_name_lower = game_name.lower()
        
        for known_game, info in famous_games.items():
            if known_game in game_name_lower or any(word in game_name_lower for word in known_game.split()):
                logger.info(f"🎮 تم العثور على اللعبة في قاعدة البيانات: {known_game}")
                return info
        
        return {}
    
    def _analyze_genre_from_name(self, game_name: str) -> Dict:
        """تحليل نوع اللعبة من الاسم والكلمات المفتاحية"""
        name_lower = game_name.lower()
        
        # كلمات مفتاحية لتحديد النوع
        genre_keywords = {
            'action': ['war', 'fight', 'combat', 'battle', 'shooter', 'gun', 'weapon', 'حرب', 'قتال', 'معركة'],
            'rpg': ['quest', 'adventure', 'magic', 'fantasy', 'dragon', 'wizard', 'مغامرة', 'سحر', 'خيال'],
            'racing': ['racing', 'car', 'speed', 'drive', 'motor', 'سباق', 'سيارة', 'سرعة'],
            'sports': ['football', 'soccer', 'basketball', 'tennis', 'sport', 'كرة', 'رياضة'],
            'strategy': ['strategy', 'empire', 'civilization', 'war', 'tactical', 'استراتيجية', 'امبراطورية'],
            'horror': ['horror', 'zombie', 'dead', 'evil', 'dark', 'رعب', 'زومبي', 'شر'],
            'puzzle': ['puzzle', 'brain', 'logic', 'match', 'لغز', 'منطق', 'ذكاء']
        }
        
        detected_genre = 'action'  # افتراضي
        confidence = 0.3
        
        for genre, keywords in genre_keywords.items():
            if any(keyword in name_lower for keyword in keywords):
                detected_genre = genre
                confidence = 0.8
                break
        
        return {
            'genre': detected_genre,
            'confidence': confidence,
            'detection_method': 'keyword_analysis'
        }
    
    def _analyze_game_visual_style(self, game_info: Dict, article: Dict) -> Dict:
        """تحليل الأسلوب البصري للعبة"""
        genre = game_info.get('genre', 'action')
        style_template = self.game_style_templates.get(genre, self.game_style_templates['action'])
        
        # تخصيص الأسلوب بناءً على معلومات إضافية
        customized_style = style_template.copy()
        
        # إضافة تفاصيل من المقال
        article_text = f"{article.get('title', '')} {article.get('content', '')}".lower()
        
        # تحسين الوصف بناءً على محتوى المقال
        if 'new' in article_text or 'جديد' in article_text:
            customized_style['composition'] += ', showcasing new features and updates'
        
        if 'character' in article_text or 'شخصية' in article_text:
            customized_style['characters'] += ', detailed character showcase'
        
        if 'trailer' in article_text or 'إعلان' in article_text:
            customized_style['visual_style'] += ', cinematic trailer style'
        
        customized_style['confidence'] = game_info.get('confidence', 0.7)
        customized_style['genre'] = genre
        
        return customized_style
    
    def _create_enhanced_image_prompt(self, visual_analysis: Dict, game_name: str, article: Dict) -> str:
        """إنشاء prompt محسن للصورة بناءً على التحليل"""
        try:
            # الأساس من التحليل البصري
            base_style = visual_analysis.get('visual_style', 'gaming artwork')
            color_palette = visual_analysis.get('color_palette', 'vibrant gaming colors')
            composition = visual_analysis.get('composition', 'dynamic gaming composition')
            characters = visual_analysis.get('characters', 'gaming characters')
            
            # إنشاء prompt شامل
            prompt_parts = [
                f"Professional gaming artwork for {game_name}",
                base_style,
                f"featuring {characters}",
                f"with {color_palette}",
                f"using {composition}",
                "high quality, detailed, 4K resolution",
                "gaming logo style, official game art aesthetic",
                "no text overlay, clean composition"
            ]
            
            # إضافة تفاصيل من العنوان
            title = article.get('title', '')
            if 'update' in title.lower() or 'تحديث' in title:
                prompt_parts.append("showing new content and updates")
            
            if 'release' in title.lower() or 'إصدار' in title:
                prompt_parts.append("launch artwork style")
            
            enhanced_prompt = ", ".join(prompt_parts)
            
            # تنظيف وتحسين الـ prompt
            enhanced_prompt = re.sub(r'\s+', ' ', enhanced_prompt).strip()
            
            logger.info(f"🎨 تم إنشاء prompt محسن: {enhanced_prompt[:100]}...")
            return enhanced_prompt
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء prompt محسن: {e}")
            return f"Professional gaming artwork for {game_name}, high quality, detailed"
    
    def _get_generic_gaming_style(self) -> Dict:
        """الحصول على أسلوب عام للألعاب عند فشل التحليل"""
        return {
            'game_name': 'Generic Game',
            'visual_style': {
                'visual_style': 'modern gaming artwork with dynamic elements',
                'color_palette': 'vibrant gaming colors with blue and orange accents',
                'composition': 'centered composition with gaming elements',
                'characters': 'gaming icons and symbols',
                'genre': 'action',
                'confidence': 0.5
            },
            'enhanced_prompt': 'Professional gaming artwork, modern style, vibrant colors, high quality, detailed, 4K resolution, gaming aesthetic',
            'analysis_date': datetime.now().isoformat(),
            'confidence_score': 0.5,
            'analysis_method': 'generic_fallback'
        }

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""

        total_analyses = self.usage_stats['total_analyses']
        success_rate = (self.usage_stats['successful_analyses'] / total_analyses * 100) if total_analyses > 0 else 0
        gemini_rate = (self.usage_stats['gemini_analyses'] / total_analyses * 100) if total_analyses > 0 else 0
        cache_hit_rate = (self.usage_stats['cache_hits'] / total_analyses * 100) if total_analyses > 0 else 0

        return {
            'enabled': self.enabled,
            'model': self.model_name,
            'total_analyses': total_analyses,
            'successful_analyses': self.usage_stats['successful_analyses'],
            'failed_analyses': self.usage_stats['failed_analyses'],
            'success_rate': round(success_rate, 2),
            'gemini_analyses': self.usage_stats['gemini_analyses'],
            'fallback_analyses': self.usage_stats['fallback_analyses'],
            'gemini_usage_rate': round(gemini_rate, 2),
            'cache_hits': self.usage_stats['cache_hits'],
            'cache_hit_rate': round(cache_hit_rate, 2),
            'daily_usage': self.usage_stats['daily_usage'],
            'last_reset': self.usage_stats['last_reset'].isoformat() if hasattr(self.usage_stats['last_reset'], 'isoformat') else str(self.usage_stats['last_reset']),
            'cache_size': len(self.analysis_cache)
        }

    async def analyze_image_for_style_extraction(self, image_path: str) -> Optional[Dict]:
        """تحليل صورة موجودة لاستخراج النمط البصري باستخدام Gemini 2.5 Pro"""

        if not self.enabled:
            logger.warning("⚠️ نظام Gemini غير مفعل")
            return None

        if not os.path.exists(image_path):
            logger.error(f"❌ الصورة غير موجودة: {image_path}")
            return None

        try:
            logger.info(f"🎨 بدء تحليل الصورة لاستخراج النمط: {os.path.basename(image_path)}")

            # تحويل الصورة إلى base64
            image_data = await self._encode_image_to_base64(image_path)
            if not image_data:
                raise Exception("فشل في قراءة الصورة")

            # إعداد prompt لتحليل الصورة
            image_analysis_prompt = """
            Analyze this gaming image to extract its visual style for creating similar images.

            Provide analysis in this EXACT JSON format:
            {
                "game_name": "Game name if identifiable or 'Unknown'",
                "visual_style": "Detailed description of the visual style",
                "color_palette": "Main colors and color scheme",
                "art_style": "Art style (realistic, cartoon, pixel art, etc.)",
                "composition": "Layout and composition description",
                "lighting": "Lighting style and mood",
                "characters": "Character or element descriptions",
                "ui_elements": "UI elements if visible",
                "genre_indicators": "Visual clues about game genre",
                "creation_prompt": "Detailed prompt for creating similar image",
                "confidence_score": 0.0-1.0
            }
            """

            # إرسال الطلب إلى Gemini
            url = f"{self.base_url}/models/{self.model_name}:generateContent"

            headers = {
                'Content-Type': 'application/json'
            }

            params = {
                'key': google_api_manager.get_key()
            }

            # تحديد نوع الصورة
            image_format = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'

            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": image_analysis_prompt
                        },
                        {
                            "inlineData": {
                                "mimeType": image_format,
                                "data": image_data
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.3,
                    "topK": 32,
                    "topP": 0.8,
                    "maxOutputTokens": 2048,
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=60) as response:

                    if response.status == 200:
                        data = await response.json()

                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])

                            if parts and 'text' in parts[0]:
                                response_text = parts[0]['text'].strip()

                                # معالجة الاستجابة
                                return self._process_image_analysis_response(response_text, image_path)

                    else:
                        logger.warning(f"⚠️ خطأ في Gemini API: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ فشل في تحليل الصورة: {e}")
            return None

    async def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """تحويل الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                encoded_data = base64.b64encode(image_data).decode('utf-8')
                return encoded_data
        except Exception as e:
            logger.error(f"❌ فشل في تحويل الصورة إلى base64: {e}")
            return None

    def _process_image_analysis_response(self, response_text: str, image_path: str) -> Optional[Dict]:
        """معالجة استجابة تحليل الصورة"""
        try:
            # محاولة استخراج JSON من الاستجابة
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                analysis_data = json.loads(json_text)

                result = {
                    'source_image': image_path,
                    'game_name': analysis_data.get('game_name', 'Unknown'),
                    'visual_style': analysis_data.get('visual_style', 'Unknown'),
                    'color_palette': analysis_data.get('color_palette', 'Unknown'),
                    'art_style': analysis_data.get('art_style', 'Unknown'),
                    'composition': analysis_data.get('composition', 'Unknown'),
                    'lighting': analysis_data.get('lighting', 'Unknown'),
                    'characters': analysis_data.get('characters', 'Unknown'),
                    'ui_elements': analysis_data.get('ui_elements', 'Unknown'),
                    'genre_indicators': analysis_data.get('genre_indicators', 'Unknown'),
                    'creation_prompt': analysis_data.get('creation_prompt', ''),
                    'confidence_score': analysis_data.get('confidence_score', 0.5),
                    'analysis_timestamp': datetime.now().isoformat(),
                    'analysis_method': 'gemini_image_analysis'
                }

                logger.info(f"✅ تم تحليل الصورة بنجاح: {analysis_data.get('game_name', 'Unknown')}")
                return result

            else:
                logger.warning("⚠️ لم يتم العثور على JSON صالح في استجابة تحليل الصورة")
                return None

        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ خطأ في تحليل JSON من تحليل الصورة: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة تحليل الصورة: {e}")
            return None

# إنشاء مثيل عام
game_image_analyzer = GameImageAnalyzer()
