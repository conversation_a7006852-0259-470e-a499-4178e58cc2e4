# إعدادات الصوت المحسنة لتجنب مشاكل رفع الملفات

class AudioConfig:
    """إعدادات تحسين الصوت لـ Whisper API"""
    
    # حدود الحجم
    MAX_FILE_SIZE_MB = 25  # الحد الأقصى لحجم الملف
    OPTIMAL_FILE_SIZE_MB = 10  # الحجم الأمثل
    CHUNK_SIZE_MB = 5  # حجم الجزء عند التقسيم
    
    # إعدادات الجودة
    AUDIO_QUALITY_SETTINGS = {
        'high_quality': {
            'codec': 'mp3',
            'bitrate': '192k',
            'sample_rate': '44100',
            'channels': '2'
        },
        'medium_quality': {
            'codec': 'mp3',
            'bitrate': '128k',
            'sample_rate': '22050',
            'channels': '1'
        },
        'low_quality': {
            'codec': 'mp3',
            'bitrate': '64k',
            'sample_rate': '16000',
            'channels': '1'
        }
    }
    
    # إعدادات yt-dlp المحسنة
    YT_DLP_OPTS_BASE = {
        'quiet': True,
        'no_warnings': True,
        'extract_flat': False,
        'format': 'bestaudio[filesize<15M]/bestaudio/best[filesize<15M]',
    }
    
    # إعدادات الضغط التدريجي
    COMPRESSION_LEVELS = [
        {'max_size_mb': 8, 'quality': 'medium_quality'},
        {'max_size_mb': 12, 'quality': 'medium_quality'},
        {'max_size_mb': 15, 'quality': 'high_quality'},
    ]
    
    # نماذج Whisper مرتبة حسب الأولوية (الأصغر أولاً للملفات الكبيرة)
    WHISPER_MODELS_PRIORITY = [
        'whisper-tiny',
        'whisper-small', 
        'whisper-base',
        'whisper-medium',
        'whisper-large-v3'
    ]
    
    # إعدادات المحاولات
    MAX_RETRIES = 3
    RETRY_DELAY_SECONDS = 2
    TIMEOUT_SECONDS = 90
    
    @classmethod
    def get_quality_for_size(cls, size_mb: float) -> str:
        """تحديد جودة الصوت المناسبة حسب الحجم"""
        if size_mb > 20:
            return 'low_quality'
        elif size_mb > 10:
            return 'medium_quality'
        else:
            return 'high_quality'
    
    @classmethod
    def get_model_for_size(cls, size_mb: float) -> str:
        """تحديد نموذج Whisper المناسب حسب الحجم"""
        if size_mb > 20:
            return 'whisper-tiny'
        elif size_mb > 15:
            return 'whisper-small'
        elif size_mb > 10:
            return 'whisper-base'
        elif size_mb > 5:
            return 'whisper-medium'
        else:
            return 'whisper-large-v3'
    
    @classmethod
    def should_split_file(cls, size_mb: float) -> bool:
        """تحديد ما إذا كان يجب تقسيم الملف"""
        return size_mb > cls.MAX_FILE_SIZE_MB
    
    @classmethod
    def should_compress_file(cls, size_mb: float) -> bool:
        """تحديد ما إذا كان يجب ضغط الملف"""
        return size_mb > cls.OPTIMAL_FILE_SIZE_MB

# إعدادات خاصة بالتشخيص
class AudioDiagnostics:
    """أدوات تشخيص مشاكل الصوت"""
    
    @staticmethod
    def analyze_file_size(audio_data: bytes) -> dict:
        """تحليل حجم الملف وإعطاء توصيات"""
        size_bytes = len(audio_data)
        size_mb = size_bytes / 1024 / 1024
        
        analysis = {
            'size_bytes': size_bytes,
            'size_mb': round(size_mb, 2),
            'status': 'unknown',
            'recommendations': []
        }
        
        if size_mb <= AudioConfig.OPTIMAL_FILE_SIZE_MB:
            analysis['status'] = 'optimal'
            analysis['recommendations'].append('الحجم مثالي - لا حاجة لتعديل')
        elif size_mb <= AudioConfig.MAX_FILE_SIZE_MB:
            analysis['status'] = 'acceptable'
            analysis['recommendations'].append('الحجم مقبول - قد يحتاج ضغط خفيف')
        else:
            analysis['status'] = 'too_large'
            analysis['recommendations'].extend([
                'الملف كبير جداً - يحتاج ضغط أو تقسيم',
                f'يُنصح بتقليل الحجم إلى أقل من {AudioConfig.MAX_FILE_SIZE_MB}MB'
            ])
        
        return analysis
    
    @staticmethod
    def suggest_upload_strategy(size_mb: float) -> dict:
        """اقتراح استراتيجية الرفع المناسبة"""
        strategy = {
            'method': 'direct',
            'model': AudioConfig.get_model_for_size(size_mb),
            'quality': AudioConfig.get_quality_for_size(size_mb),
            'steps': []
        }
        
        if AudioConfig.should_split_file(size_mb):
            strategy['method'] = 'split'
            strategy['steps'] = [
                'تقسيم الملف إلى أجزاء صغيرة',
                'رفع كل جزء منفصلاً',
                'دمج النصوص المستخرجة'
            ]
        elif AudioConfig.should_compress_file(size_mb):
            strategy['method'] = 'compress'
            strategy['steps'] = [
                'ضغط الملف لتقليل الحجم',
                'رفع الملف المضغوط',
                'استخدام نموذج مناسب للحجم'
            ]
        else:
            strategy['steps'] = [
                'رفع مباشر بدون تعديل',
                'استخدام أفضل نموذج متاح'
            ]
        
        return strategy

# إعدادات معالجة الأخطاء
class AudioErrorHandling:
    """معالجة أخطاء الصوت"""
    
    ERROR_MESSAGES = {
        'file_too_large': 'الملف كبير جداً',
        'upload_failed': 'فشل في رفع الملف',
        'invalid_format': 'تنسيق الملف غير صحيح',
        'network_error': 'خطأ في الشبكة',
        'api_error': 'خطأ في API'
    }
    
    RECOVERY_STRATEGIES = {
        'file_too_large': ['compress', 'split', 'reduce_quality'],
        'upload_failed': ['retry', 'alternative_method', 'compress'],
        'invalid_format': ['convert_format', 'retry'],
        'network_error': ['retry', 'wait_and_retry'],
        'api_error': ['alternative_api', 'retry', 'reduce_model']
    }
    
    @classmethod
    def get_recovery_plan(cls, error_type: str) -> list:
        """الحصول على خطة الاستعادة من الخطأ"""
        return cls.RECOVERY_STRATEGIES.get(error_type, ['retry'])
