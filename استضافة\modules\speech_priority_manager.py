# نظام الأولوية والتبديل التلقائي لخدمات تحويل النص إلى صوت
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from modules.logger import logger

class ServiceStatus(Enum):
    """حالة الخدمة"""
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"
    QUOTA_EXCEEDED = "quota_exceeded"

@dataclass
class ServiceHealth:
    """صحة الخدمة"""
    service_name: str
    status: ServiceStatus
    success_rate: float  # معدل النجاح (0-1)
    avg_response_time: float  # متوسط وقت الاستجابة بالثواني
    last_success: Optional[datetime] = None
    last_error: Optional[datetime] = None
    error_count: int = 0
    success_count: int = 0
    total_requests: int = 0
    consecutive_failures: int = 0
    last_error_message: str = ""

@dataclass
class UsageQuota:
    """حصة الاستخدام"""
    service_name: str
    monthly_limit: int  # بالدقائق
    monthly_usage: int  # بالدقائق
    daily_limit: int = 0  # بالدقائق (اختياري)
    daily_usage: int = 0  # بالدقائق
    hourly_limit: int = 0  # بالطلبات (اختياري)
    hourly_usage: int = 0  # بالطلبات
    reset_date: Optional[datetime] = None

class SpeechPriorityManager:
    """مدير الأولوية والتبديل التلقائي لخدمات تحويل النص إلى صوت"""
    
    def __init__(self):
        self.health_data: Dict[str, ServiceHealth] = {}
        self.usage_quotas: Dict[str, UsageQuota] = {}
        self.priority_rules: Dict[str, int] = {}
        self.fallback_chain: List[str] = []
        self.blacklist_duration = 300  # 5 دقائق للخدمات المعطلة مؤقتاً
        self.data_file = "cache/speech_priority_data.json"
        
        self._load_data()
        self._initialize_default_priorities()
        
    def _initialize_default_priorities(self):
        """تهيئة الأولويات الافتراضية"""
        default_priorities = {
            "assemblyai": 1,      # أعلى أولوية - 416 ساعة مجانية
            "speechmatics": 2,    # 480 دقيقة شهرياً
            "ibm_watson": 3,      # 500 دقيقة شهرياً
            "azure_speech": 4,    # 300 دقيقة شهرياً
            "google_cloud": 5,    # 60 دقيقة شهرياً
            "witai": 6,           # مجاني بلا حدود لكن جودة أقل
            "whisper": 99         # البديل الاحتياطي الأخير
        }
        
        # تحديث الأولويات فقط إذا لم تكن موجودة
        for service, priority in default_priorities.items():
            if service not in self.priority_rules:
                self.priority_rules[service] = priority
                
        # إنشاء سلسلة البدائل
        self.fallback_chain = sorted(self.priority_rules.keys(), key=lambda x: self.priority_rules[x])
        
    def _load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # تحميل بيانات الصحة
                for service_name, health_data in data.get("health", {}).items():
                    # تحويل التواريخ من string إلى datetime
                    if health_data.get("last_success"):
                        health_data["last_success"] = datetime.fromisoformat(health_data["last_success"])
                    if health_data.get("last_error"):
                        health_data["last_error"] = datetime.fromisoformat(health_data["last_error"])
                        
                    health_data["status"] = ServiceStatus(health_data["status"])
                    self.health_data[service_name] = ServiceHealth(**health_data)
                    
                # تحميل بيانات الاستخدام
                for service_name, quota_data in data.get("quotas", {}).items():
                    if quota_data.get("reset_date"):
                        quota_data["reset_date"] = datetime.fromisoformat(quota_data["reset_date"])
                    self.usage_quotas[service_name] = UsageQuota(**quota_data)
                    
                # تحميل قواعد الأولوية
                self.priority_rules.update(data.get("priorities", {}))
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل بيانات الأولوية: {e}")
            
    def _save_data(self):
        """حفظ البيانات"""
        try:
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            
            # تحويل البيانات للحفظ
            health_data = {}
            for service_name, health in self.health_data.items():
                health_dict = asdict(health)
                # تحويل datetime إلى string
                if health_dict["last_success"]:
                    health_dict["last_success"] = health_dict["last_success"].isoformat()
                if health_dict["last_error"]:
                    health_dict["last_error"] = health_dict["last_error"].isoformat()
                health_dict["status"] = health_dict["status"].value
                health_data[service_name] = health_dict
                
            quota_data = {}
            for service_name, quota in self.usage_quotas.items():
                quota_dict = asdict(quota)
                if quota_dict["reset_date"]:
                    quota_dict["reset_date"] = quota_dict["reset_date"].isoformat()
                quota_data[service_name] = quota_dict
                
            data = {
                "health": health_data,
                "quotas": quota_data,
                "priorities": self.priority_rules,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ بيانات الأولوية: {e}")
            
    def initialize_service(self, service_name: str, monthly_limit: int, 
                          daily_limit: int = 0, hourly_limit: int = 0):
        """تهيئة خدمة جديدة"""
        if service_name not in self.health_data:
            self.health_data[service_name] = ServiceHealth(
                service_name=service_name,
                status=ServiceStatus.ACTIVE,
                success_rate=1.0,
                avg_response_time=0.0
            )
            
        if service_name not in self.usage_quotas:
            self.usage_quotas[service_name] = UsageQuota(
                service_name=service_name,
                monthly_limit=monthly_limit,
                monthly_usage=0,
                daily_limit=daily_limit,
                daily_usage=0,
                hourly_limit=hourly_limit,
                hourly_usage=0,
                reset_date=datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            )
            
        self._save_data()
        
    def record_request_result(self, service_name: str, success: bool, 
                            response_time: float, duration_minutes: float = 0,
                            error_message: str = ""):
        """تسجيل نتيجة طلب"""
        now = datetime.now()
        
        # تحديث بيانات الصحة
        if service_name not in self.health_data:
            self.initialize_service(service_name, 999999)  # حد افتراضي عالي
            
        health = self.health_data[service_name]
        health.total_requests += 1
        
        if success:
            health.success_count += 1
            health.last_success = now
            health.consecutive_failures = 0
            health.status = ServiceStatus.ACTIVE
        else:
            health.error_count += 1
            health.last_error = now
            health.consecutive_failures += 1
            health.last_error_message = error_message
            
            # تحديد حالة الخدمة بناءً على نوع الخطأ
            if "rate limit" in error_message.lower() or "quota" in error_message.lower():
                health.status = ServiceStatus.RATE_LIMITED
            elif health.consecutive_failures >= 3:
                health.status = ServiceStatus.ERROR
                
        # حساب معدل النجاح ومتوسط وقت الاستجابة
        health.success_rate = health.success_count / health.total_requests
        
        # تحديث متوسط وقت الاستجابة (متوسط متحرك)
        if health.avg_response_time == 0:
            health.avg_response_time = response_time
        else:
            health.avg_response_time = (health.avg_response_time * 0.8) + (response_time * 0.2)
            
        # تحديث استخدام الحصة
        if duration_minutes > 0:
            self._update_usage(service_name, duration_minutes)
            
        self._save_data()
        
    def _update_usage(self, service_name: str, duration_minutes: float):
        """تحديث استخدام الحصة"""
        if service_name not in self.usage_quotas:
            return
            
        quota = self.usage_quotas[service_name]
        now = datetime.now()
        
        # فحص إعادة تعيين الحصة الشهرية
        if quota.reset_date and now >= quota.reset_date + timedelta(days=30):
            quota.monthly_usage = 0
            quota.daily_usage = 0
            quota.hourly_usage = 0
            quota.reset_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
        # فحص إعادة تعيين الحصة اليومية
        if quota.reset_date and now.date() > quota.reset_date.date():
            quota.daily_usage = 0
            quota.hourly_usage = 0
            
        # فحص إعادة تعيين الحصة الساعية
        if quota.reset_date and now.hour != quota.reset_date.hour:
            quota.hourly_usage = 0
            
        # تحديث الاستخدام
        quota.monthly_usage += int(duration_minutes)
        quota.daily_usage += int(duration_minutes)
        quota.hourly_usage += 1  # عدد الطلبات
        
        # فحص تجاوز الحدود
        health = self.health_data[service_name]
        if quota.monthly_usage >= quota.monthly_limit:
            health.status = ServiceStatus.QUOTA_EXCEEDED
        elif quota.daily_limit > 0 and quota.daily_usage >= quota.daily_limit:
            health.status = ServiceStatus.QUOTA_EXCEEDED
        elif quota.hourly_limit > 0 and quota.hourly_usage >= quota.hourly_limit:
            health.status = ServiceStatus.RATE_LIMITED
            
    def get_best_service(self, duration_minutes: float = 1.0) -> Optional[str]:
        """الحصول على أفضل خدمة متاحة"""
        available_services = []
        
        for service_name in self.fallback_chain:
            if self._is_service_available(service_name, duration_minutes):
                health = self.health_data.get(service_name)
                if health:
                    # حساب نقاط الجودة
                    quality_score = self._calculate_quality_score(health, service_name)
                    available_services.append((service_name, quality_score))
                    
        if not available_services:
            logger.warning("⚠️ لا توجد خدمات متاحة")
            return None
            
        # ترتيب حسب نقاط الجودة
        available_services.sort(key=lambda x: x[1], reverse=True)
        best_service = available_services[0][0]
        
        logger.info(f"🎯 أفضل خدمة متاحة: {best_service}")
        return best_service
        
    def _is_service_available(self, service_name: str, duration_minutes: float) -> bool:
        """فحص ما إذا كانت الخدمة متاحة"""
        # فحص الصحة العامة
        health = self.health_data.get(service_name)
        if not health or health.status in [ServiceStatus.DISABLED, ServiceStatus.ERROR]:
            return False
            
        # فحص الحصة
        quota = self.usage_quotas.get(service_name)
        if quota:
            if quota.monthly_usage + duration_minutes > quota.monthly_limit:
                return False
            if quota.daily_limit > 0 and quota.daily_usage + duration_minutes > quota.daily_limit:
                return False
            if quota.hourly_limit > 0 and quota.hourly_usage >= quota.hourly_limit:
                return False
                
        # فحص الفشل المتتالي
        if health.consecutive_failures >= 3:
            # فحص ما إذا كان الوقت كافي للمحاولة مرة أخرى
            if health.last_error:
                time_since_error = (datetime.now() - health.last_error).total_seconds()
                if time_since_error < self.blacklist_duration:
                    return False
                    
        return True
        
    def _calculate_quality_score(self, health: ServiceHealth, service_name: str) -> float:
        """حساب نقاط جودة الخدمة"""
        score = 0.0
        
        # الأولوية الأساسية (40%)
        priority = self.priority_rules.get(service_name, 99)
        priority_score = max(0, (10 - priority) / 10) * 0.4
        score += priority_score
        
        # معدل النجاح (30%)
        success_score = health.success_rate * 0.3
        score += success_score
        
        # سرعة الاستجابة (20%)
        if health.avg_response_time > 0:
            # كلما قل الوقت، كلما زادت النقاط
            speed_score = max(0, (60 - health.avg_response_time) / 60) * 0.2
            score += speed_score
        else:
            score += 0.2  # نقاط كاملة للخدمات الجديدة
            
        # الاستقرار (10%)
        stability_score = max(0, (5 - health.consecutive_failures) / 5) * 0.1
        score += stability_score
        
        return score
        
    def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمات"""
        stats = {
            "total_services": len(self.health_data),
            "active_services": 0,
            "services": {}
        }
        
        for service_name, health in self.health_data.items():
            quota = self.usage_quotas.get(service_name, UsageQuota(service_name, 0, 0))
            
            service_stats = {
                "status": health.status.value,
                "success_rate": round(health.success_rate * 100, 2),
                "avg_response_time": round(health.avg_response_time, 2),
                "total_requests": health.total_requests,
                "consecutive_failures": health.consecutive_failures,
                "monthly_usage": quota.monthly_usage,
                "monthly_limit": quota.monthly_limit,
                "usage_percentage": round((quota.monthly_usage / quota.monthly_limit) * 100, 2) if quota.monthly_limit > 0 else 0,
                "priority": self.priority_rules.get(service_name, 99),
                "quality_score": round(self._calculate_quality_score(health, service_name), 3)
            }
            
            if health.status == ServiceStatus.ACTIVE:
                stats["active_services"] += 1
                
            stats["services"][service_name] = service_stats
            
        return stats
        
    def reset_service_health(self, service_name: str):
        """إعادة تعيين صحة خدمة معينة"""
        if service_name in self.health_data:
            health = self.health_data[service_name]
            health.status = ServiceStatus.ACTIVE
            health.consecutive_failures = 0
            health.error_count = 0
            health.last_error = None
            health.last_error_message = ""
            self._save_data()
            logger.info(f"🔄 تم إعادة تعيين صحة خدمة {service_name}")


# إنشاء مثيل عام
speech_priority_manager = SpeechPriorityManager()
