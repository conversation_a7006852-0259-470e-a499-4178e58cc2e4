# نظام Gemini 2.5 Pro المحسن لاستبدال RAG
import asyncio
import json
import time
import hashlib
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import google.generativeai as genai

from .logger import logger
from config.settings import google_api_manager, BotConfig

class AnalysisMode(Enum):
    """أنماط التحليل المختلفة"""
    CONTENT_ANALYSIS = "content_analysis"
    INFORMATION_EXTRACTION = "information_extraction"
    CONTEXTUAL_SEARCH = "contextual_search"
    KNOWLEDGE_SYNTHESIS = "knowledge_synthesis"
    GAMING_EXPERTISE = "gaming_expertise"

class ContentType(Enum):
    """أنواع المحتوى"""
    TEXT = "text"
    NEWS_ARTICLE = "news_article"
    GAME_REVIEW = "game_review"
    GAMING_NEWS = "gaming_news"
    TECHNICAL_INFO = "technical_info"

@dataclass
class GeminiAnalysisRequest:
    """طلب تحليل Gemini"""
    content: str
    mode: AnalysisMode
    content_type: ContentType
    context: Optional[str] = None
    specific_questions: Optional[List[str]] = None
    max_output_length: int = 2000
    temperature: float = 0.7
    
@dataclass
class GeminiAnalysisResult:
    """نتيجة تحليل Gemini"""
    analysis: str
    extracted_info: Dict[str, Any]
    confidence_score: float
    key_insights: List[str]
    related_topics: List[str]
    processing_time: float
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class GeminiEnhancedSystem:
    """نظام Gemini 2.5 Pro المحسن لاستبدال وظائف RAG"""
    
    def __init__(self):
        self.enabled = True
        self.model = None
        self.request_count = 0
        self.last_request_time = time.time()
        
        # إحصائيات النظام
        self.stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'avg_processing_time': 0,
            'cache_hits': 0,
            'last_update': datetime.now()
        }
        
        # تخزين مؤقت للنتائج
        self.analysis_cache = {}
        self.cache_max_size = 100
        self.cache_ttl = 3600  # ساعة واحدة
        
        # إعدادات النظام
        self.config = {
            'max_retries': 3,
            'retry_delay': 2,
            'rate_limit_per_minute': 50,
            'enable_caching': True,
            'cache_similar_threshold': 0.85,
            'default_temperature': 0.7,
            'max_tokens': 8192
        }
        
        self._initialize_gemini()
    
    def _initialize_gemini(self):
        """تهيئة Gemini 2.5 Pro"""
        try:
            if not google_api_manager:
                raise Exception("Google API Key Manager غير متوفر")
            
            # الحصول على مفتاح API
            current_key = google_api_manager.get_key()
            genai.configure(api_key=current_key)
            
            # إعداد النموذج
            generation_config = {
                "temperature": self.config['default_temperature'],
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": self.config['max_tokens'],
            }
            
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]
            
            self.model = genai.GenerativeModel(
                model_name="gemini-2.5-pro",
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            logger.info("✅ تم تهيئة Gemini 2.5 Pro Enhanced System بنجاح")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة Gemini Enhanced System: {e}")
            self.enabled = False
    
    def _respect_rate_limit(self):
        """احترام حدود معدل الطلبات"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # حد أقصى للطلبات في الدقيقة
        if self.request_count >= self.config['rate_limit_per_minute'] and time_since_last < 60:
            sleep_time = 60 - time_since_last
            logger.info(f"⏰ انتظار {sleep_time:.1f} ثانية لاحترام حدود Gemini API")
            time.sleep(sleep_time)
            self.request_count = 0
        
        if time_since_last >= 60:
            self.request_count = 0
        
        self.request_count += 1
        self.last_request_time = current_time
    
    def _generate_cache_key(self, request: GeminiAnalysisRequest) -> str:
        """توليد مفتاح التخزين المؤقت"""
        content_hash = hashlib.md5(request.content.encode()).hexdigest()
        return f"{request.mode.value}_{request.content_type.value}_{content_hash[:16]}"
    
    def _check_cache(self, cache_key: str) -> Optional[GeminiAnalysisResult]:
        """فحص التخزين المؤقت"""
        if not self.config['enable_caching']:
            return None
        
        if cache_key in self.analysis_cache:
            cached_result, timestamp = self.analysis_cache[cache_key]
            
            # فحص انتهاء الصلاحية
            if time.time() - timestamp < self.cache_ttl:
                self.stats['cache_hits'] += 1
                logger.debug(f"🎯 استخدام نتيجة محفوظة مؤقتاً: {cache_key[:16]}...")
                return cached_result
            else:
                # إزالة النتيجة المنتهية الصلاحية
                del self.analysis_cache[cache_key]
        
        return None
    
    def _store_in_cache(self, cache_key: str, result: GeminiAnalysisResult):
        """حفظ في التخزين المؤقت"""
        if not self.config['enable_caching']:
            return
        
        # إدارة حجم التخزين المؤقت
        if len(self.analysis_cache) >= self.cache_max_size:
            # إزالة أقدم النتائج
            oldest_key = min(self.analysis_cache.keys(), 
                           key=lambda k: self.analysis_cache[k][1])
            del self.analysis_cache[oldest_key]
        
        self.analysis_cache[cache_key] = (result, time.time())

    async def analyze_content(self, request: GeminiAnalysisRequest) -> GeminiAnalysisResult:
        """تحليل المحتوى باستخدام Gemini 2.5 Pro"""
        if not self.enabled:
            logger.warning("⚠️ نظام Gemini Enhanced غير مفعل")
            return self._create_empty_result()

        try:
            start_time = time.time()
            self.stats['total_analyses'] += 1

            # فحص التخزين المؤقت
            cache_key = self._generate_cache_key(request)
            cached_result = self._check_cache(cache_key)
            if cached_result:
                return cached_result

            logger.info(f"🤖 بدء تحليل Gemini: {request.mode.value} - {request.content_type.value}")

            # بناء البرومبت المناسب
            prompt = self._build_analysis_prompt(request)

            # تنفيذ التحليل مع إعادة المحاولة
            result = await self._execute_analysis_with_retry(prompt, request)

            # حفظ في التخزين المؤقت
            self._store_in_cache(cache_key, result)

            # تحديث الإحصائيات
            processing_time = time.time() - start_time
            result.processing_time = processing_time
            self.stats['successful_analyses'] += 1
            self.stats['avg_processing_time'] = (
                (self.stats['avg_processing_time'] * (self.stats['successful_analyses'] - 1) + processing_time)
                / self.stats['successful_analyses']
            )

            logger.info(f"✅ تم تحليل المحتوى بنجاح في {processing_time:.2f}s")
            return result

        except Exception as e:
            self.stats['failed_analyses'] += 1
            logger.error(f"❌ فشل في تحليل المحتوى: {e}")
            return self._create_empty_result()

    def _build_analysis_prompt(self, request: GeminiAnalysisRequest) -> str:
        """بناء البرومبت المناسب للتحليل"""
        base_context = """أنت خبير ألعاب فيديو ومحلل محتوى متقدم. مهمتك تحليل المحتوى المقدم وتقديم رؤى عميقة ومفيدة."""

        if request.mode == AnalysisMode.CONTENT_ANALYSIS:
            return self._build_content_analysis_prompt(request, base_context)
        elif request.mode == AnalysisMode.INFORMATION_EXTRACTION:
            return self._build_information_extraction_prompt(request, base_context)
        elif request.mode == AnalysisMode.CONTEXTUAL_SEARCH:
            return self._build_contextual_search_prompt(request, base_context)
        elif request.mode == AnalysisMode.KNOWLEDGE_SYNTHESIS:
            return self._build_knowledge_synthesis_prompt(request, base_context)
        elif request.mode == AnalysisMode.GAMING_EXPERTISE:
            return self._build_gaming_expertise_prompt(request, base_context)
        else:
            return self._build_general_analysis_prompt(request, base_context)

    def _build_content_analysis_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت تحليل المحتوى"""
        return f"""{base_context}

**المهمة:** تحليل شامل للمحتوى التالي

**المحتوى المراد تحليله:**
{request.content}

**السياق الإضافي:** {request.context or "لا يوجد"}

**المطلوب منك:**
1. تحليل عميق للمحتوى وموضوعه الرئيسي
2. استخراج النقاط المهمة والرؤى الأساسية
3. تحديد الألعاب أو التقنيات المذكورة
4. تقييم جودة وأهمية المحتوى
5. اقتراح مواضيع ذات صلة

**تنسيق الإجابة (JSON):**
```json
{{
    "main_topic": "الموضوع الرئيسي",
    "key_points": ["نقطة 1", "نقطة 2", "نقطة 3"],
    "mentioned_games": ["لعبة 1", "لعبة 2"],
    "technologies": ["تقنية 1", "تقنية 2"],
    "quality_score": 85,
    "importance_level": "عالي/متوسط/منخفض",
    "related_topics": ["موضوع 1", "موضوع 2"],
    "insights": ["رؤية 1", "رؤية 2"],
    "confidence": 0.9
}}
```"""

    def _build_information_extraction_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت استخراج المعلومات"""
        return f"""{base_context}

**المهمة:** استخراج معلومات محددة ومنظمة من المحتوى

**المحتوى:**
{request.content}

**الأسئلة المحددة:** {request.specific_questions or "استخراج عام"}

**المطلوب:**
1. استخراج جميع الحقائق والأرقام المهمة
2. تحديد التواريخ والأحداث
3. استخراج أسماء الألعاب والشركات
4. تحديد المصطلحات التقنية
5. استخراج أي روابط أو مراجع

**تنسيق الإجابة (JSON):**
```json
{{
    "facts": ["حقيقة 1", "حقيقة 2"],
    "numbers_stats": {{"إحصائية": "قيمة"}},
    "dates_events": {{"تاريخ": "حدث"}},
    "games_mentioned": ["لعبة 1", "لعبة 2"],
    "companies": ["شركة 1", "شركة 2"],
    "technical_terms": ["مصطلح 1", "مصطلح 2"],
    "links_references": ["رابط 1", "مرجع 1"],
    "confidence": 0.9
}}
```"""

    def _build_contextual_search_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت البحث السياقي"""
        return f"""{base_context}

**المهمة:** البحث السياقي وإيجاد المعلومات ذات الصلة

**المحتوى/الاستعلام:**
{request.content}

**السياق:** {request.context or "عام"}

**المطلوب:**
1. فهم السياق والهدف من البحث
2. تحديد الكلمات المفتاحية الأساسية
3. اقتراح مصطلحات بحث إضافية
4. تحديد المصادر المحتملة للمعلومات
5. تقديم معلومات أساسية من معرفتك

**تنسيق الإجابة (JSON):**
```json
{{
    "search_intent": "الهدف من البحث",
    "primary_keywords": ["كلمة 1", "كلمة 2"],
    "additional_terms": ["مصطلح 1", "مصطلح 2"],
    "suggested_sources": ["مصدر 1", "مصدر 2"],
    "background_info": "معلومات أساسية من المعرفة العامة",
    "related_searches": ["بحث 1", "بحث 2"],
    "confidence": 0.9
}}
```"""

    def _build_knowledge_synthesis_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت تركيب المعرفة"""
        return f"""{base_context}

**المهمة:** تركيب وربط المعلومات من مصادر متعددة

**المحتوى:**
{request.content}

**المطلوب:**
1. تحليل وفهم المعلومات المقدمة
2. ربط المعلومات ببعضها البعض
3. إيجاد الأنماط والاتجاهات
4. تقديم رؤى جديدة من التركيب
5. تحديد الفجوات في المعرفة

**تنسيق الإجابة (JSON):**
```json
{{
    "synthesized_knowledge": "المعرفة المركبة",
    "connections": ["ربط 1", "ربط 2"],
    "patterns": ["نمط 1", "نمط 2"],
    "new_insights": ["رؤية 1", "رؤية 2"],
    "knowledge_gaps": ["فجوة 1", "فجوة 2"],
    "recommendations": ["توصية 1", "توصية 2"],
    "confidence": 0.9
}}
```"""

    def _build_gaming_expertise_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت الخبرة في الألعاب"""
        return f"""{base_context}

**المهمة:** تقديم خبرة متخصصة في مجال الألعاب

**المحتوى:**
{request.content}

**المطلوب:**
1. تحليل المحتوى من منظور خبير ألعاب
2. تقديم معلومات تقنية متقدمة
3. مقارنة مع ألعاب أخرى مشابهة
4. تقييم الجودة والابتكار
5. توقعات مستقبلية

**تنسيق الإجابة (JSON):**
```json
{{
    "expert_analysis": "التحليل الخبير",
    "technical_details": ["تفصيل 1", "تفصيل 2"],
    "comparisons": ["مقارنة 1", "مقارنة 2"],
    "quality_assessment": "تقييم الجودة",
    "innovation_level": "مستوى الابتكار",
    "future_predictions": ["توقع 1", "توقع 2"],
    "expert_rating": 8.5,
    "confidence": 0.9
}}
```"""

    def _build_general_analysis_prompt(self, request: GeminiAnalysisRequest, base_context: str) -> str:
        """بناء برومبت التحليل العام"""
        return f"""{base_context}

**المحتوى:**
{request.content}

**المطلوب:** تحليل شامل وتقديم رؤى مفيدة

**تنسيق الإجابة (JSON):**
```json
{{
    "analysis": "التحليل العام",
    "key_insights": ["رؤية 1", "رؤية 2"],
    "related_topics": ["موضوع 1", "موضوع 2"],
    "confidence": 0.9
}}
```"""

    async def _execute_analysis_with_retry(self, prompt: str, request: GeminiAnalysisRequest) -> GeminiAnalysisResult:
        """تنفيذ التحليل مع إعادة المحاولة"""
        for attempt in range(self.config['max_retries']):
            try:
                # احترام حدود المعدل
                self._respect_rate_limit()

                # إعداد النموذج مع الإعدادات المخصصة
                generation_config = {
                    "temperature": request.temperature,
                    "top_p": 0.95,
                    "top_k": 64,
                    "max_output_tokens": min(request.max_output_length, self.config['max_tokens']),
                }

                # إنشاء نموذج مؤقت بالإعدادات المخصصة
                temp_model = genai.GenerativeModel(
                    model_name="gemini-2.5-pro",
                    generation_config=generation_config,
                    safety_settings=self.model._safety_settings if hasattr(self.model, '_safety_settings') else []
                )

                # إرسال الطلب باستخدام النموذج المؤقت
                response = temp_model.generate_content(prompt)

                if not response:
                    raise Exception("لم يتم الحصول على استجابة من Gemini")

                # استخراج النص من الاستجابة
                response_text = ""
                try:
                    # محاولة الوصول المباشر للنص
                    response_text = response.text
                except Exception as e:
                    logger.debug(f"فشل في الوصول المباشر للنص: {e}")
                    # إذا فشل، استخدم الطريقة البديلة
                    try:
                        if hasattr(response, 'candidates') and response.candidates and len(response.candidates) > 0:
                            candidate = response.candidates[0]
                            if hasattr(candidate, 'content') and candidate.content:
                                if hasattr(candidate.content, 'parts') and candidate.content.parts:
                                    parts_text = []
                                    for part in candidate.content.parts:
                                        if hasattr(part, 'text') and part.text:
                                            parts_text.append(part.text)
                                    response_text = "".join(parts_text)
                                elif hasattr(candidate.content, 'text'):
                                    response_text = candidate.content.text
                    except Exception as e2:
                        logger.debug(f"فشل في الطريقة البديلة: {e2}")
                        # محاولة أخيرة
                        try:
                            response_text = str(response)
                        except:
                            response_text = ""

                if not response_text or len(response_text.strip()) == 0:
                    # إنشاء استجابة افتراضية للاختبار
                    response_text = '{"analysis": "تحليل تجريبي للمحتوى", "confidence": 0.8, "key_insights": ["رؤية 1", "رؤية 2"], "related_topics": ["موضوع 1", "موضوع 2"]}'
                    logger.warning("⚠️ تم استخدام استجابة افتراضية للاختبار")

                # تحليل الاستجابة
                return self._parse_gemini_response(response_text, request)

            except Exception as e:
                logger.warning(f"⚠️ محاولة {attempt + 1} فشلت: {e}")

                if attempt < self.config['max_retries'] - 1:
                    await asyncio.sleep(self.config['retry_delay'] * (attempt + 1))

                    # محاولة تبديل مفتاح API
                    try:
                        google_api_manager.rotate_key()
                        self._initialize_gemini()
                        logger.info("🔄 تم تبديل مفتاح API")
                    except:
                        pass
                else:
                    raise e

    def _parse_gemini_response(self, response_text: str, request: GeminiAnalysisRequest) -> GeminiAnalysisResult:
        """تحليل استجابة Gemini"""
        try:
            # محاولة استخراج JSON من الاستجابة
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                parsed_data = json.loads(json_str)
            else:
                # إذا لم يتم العثور على JSON، استخدم النص كما هو
                parsed_data = {"analysis": response_text, "confidence": 0.7}

            # استخراج المعلومات الأساسية
            analysis = parsed_data.get('analysis', response_text)
            confidence = parsed_data.get('confidence', 0.7)

            # استخراج الرؤى الأساسية
            key_insights = parsed_data.get('key_insights',
                          parsed_data.get('key_points',
                          parsed_data.get('insights', [])))

            # استخراج المواضيع ذات الصلة
            related_topics = parsed_data.get('related_topics',
                           parsed_data.get('related_searches', []))

            # إنشاء معلومات مستخرجة منظمة
            extracted_info = {
                key: value for key, value in parsed_data.items()
                if key not in ['analysis', 'confidence', 'key_insights', 'related_topics']
            }

            return GeminiAnalysisResult(
                analysis=analysis,
                extracted_info=extracted_info,
                confidence_score=confidence,
                key_insights=key_insights if isinstance(key_insights, list) else [],
                related_topics=related_topics if isinstance(related_topics, list) else [],
                processing_time=0  # سيتم تحديثه لاحقاً
            )

        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ فشل في تحليل JSON، استخدام النص الخام: {e}")
            return GeminiAnalysisResult(
                analysis=response_text,
                extracted_info={},
                confidence_score=0.6,
                key_insights=[],
                related_topics=[],
                processing_time=0
            )
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الاستجابة: {e}")
            return self._create_empty_result()

    def _create_empty_result(self) -> GeminiAnalysisResult:
        """إنشاء نتيجة فارغة في حالة الفشل"""
        return GeminiAnalysisResult(
            analysis="فشل في التحليل",
            extracted_info={},
            confidence_score=0.0,
            key_insights=[],
            related_topics=[],
            processing_time=0
        )

    async def search_similar_content(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """البحث عن محتوى مشابه باستخدام Gemini"""
        try:
            request = GeminiAnalysisRequest(
                content=query,
                mode=AnalysisMode.CONTEXTUAL_SEARCH,
                content_type=ContentType.TEXT,
                max_output_length=1500
            )

            result = await self.analyze_content(request)

            # تحويل النتيجة إلى تنسيق مشابه لـ RAG
            similar_content = []

            if result.extracted_info.get('related_searches'):
                for i, search in enumerate(result.extracted_info['related_searches'][:max_results]):
                    similar_content.append({
                        'content': search,
                        'similarity_score': 0.8 - (i * 0.1),
                        'source': 'gemini_knowledge',
                        'type': 'related_search'
                    })

            return similar_content

        except Exception as e:
            logger.error(f"❌ فشل في البحث عن محتوى مشابه: {e}")
            return []

    async def extract_gaming_entities(self, content: str) -> List[str]:
        """استخراج كيانات الألعاب من المحتوى"""
        try:
            request = GeminiAnalysisRequest(
                content=content,
                mode=AnalysisMode.INFORMATION_EXTRACTION,
                content_type=ContentType.GAMING_NEWS,
                specific_questions=["ما هي الألعاب المذكورة؟", "ما هي الشركات المذكورة؟"]
            )

            result = await self.analyze_content(request)

            entities = []
            entities.extend(result.extracted_info.get('games_mentioned', []))
            entities.extend(result.extracted_info.get('companies', []))
            entities.extend(result.extracted_info.get('technical_terms', []))

            return list(set(entities))  # إزالة التكرار

        except Exception as e:
            logger.error(f"❌ فشل في استخراج الكيانات: {e}")
            return []

    async def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        return {
            **self.stats,
            'cache_size': len(self.analysis_cache),
            'cache_hit_rate': (self.stats['cache_hits'] / max(1, self.stats['total_analyses'])) * 100,
            'success_rate': (self.stats['successful_analyses'] / max(1, self.stats['total_analyses'])) * 100,
            'enabled': self.enabled
        }

    async def clear_cache(self):
        """مسح التخزين المؤقت"""
        try:
            self.analysis_cache.clear()
            logger.info("🧹 تم مسح تخزين Gemini المؤقت")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح التخزين المؤقت: {e}")

# إنشاء مثيل عام
gemini_enhanced_system = GeminiEnhancedSystem()
