# -*- coding: utf-8 -*-
"""
مولد الصور المتقدم باستخدام APIs حديثة
يدعم OpenArt، Leap AI، DeepAI، و Replicate
"""

import asyncio
import aiohttp
import json
import time
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import traceback
import urllib.parse

# إضافة المسار للوحدات
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.logger import logger
from config.new_image_apis_config import new_image_apis_config, APIConfig

class AdvancedImageGenerator:
    """مولد الصور المتقدم باستخدام APIs حديثة"""
    
    def __init__(self):
        self.config = new_image_apis_config
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'api_usage': {
                'openart': {'requests': 0, 'successes': 0, 'failures': 0},
                'leap_ai': {'requests': 0, 'successes': 0, 'failures': 0},
                'deepai': {'requests': 0, 'successes': 0, 'failures': 0},
                'replicate': {'requests': 0, 'successes': 0, 'failures': 0}
            },
            'daily_usage': {
                'date': datetime.now().date(),
                'requests_today': 0
            }
        }
        
        # تخزين مؤقت للصور
        self.image_cache = {}
        self.cache_file = "cache/advanced_image_cache.json"
        self._load_cache()
        
        logger.info("🎨 تم تهيئة مولد الصور المتقدم مع APIs حديثة")
    
    def _load_cache(self):
        """تحميل التخزين المؤقت للصور"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.image_cache = json.load(f)
                logger.info(f"📦 تم تحميل {len(self.image_cache)} صورة من التخزين المؤقت")
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل التخزين المؤقت: {e}")
            self.image_cache = {}
    
    def _save_cache(self):
        """حفظ التخزين المؤقت للصور"""
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.image_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ التخزين المؤقت: {e}")
    
    def _generate_cache_key(self, prompt: str, api_name: str) -> str:
        """إنشاء مفتاح للتخزين المؤقت"""
        combined = f"{prompt}_{api_name}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """فحص صحة التخزين المؤقت"""
        try:
            created_at = datetime.fromisoformat(cache_entry['created_at'])
            cache_duration = timedelta(hours=self.config.general_settings['cache_duration_hours'])
            return datetime.now() - created_at < cache_duration
        except:
            return False
    
    async def generate_image(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام أفضل API متاح"""
        try:
            self.usage_stats['total_requests'] += 1
            self.usage_stats['daily_usage']['requests_today'] += 1
            
            # تحسين الـ prompt
            category = 'gaming'
            if article_data:
                if 'review' in article_data.get('title', '').lower():
                    category = 'review'
                elif 'news' in article_data.get('title', '').lower():
                    category = 'news'
            
            enhanced_prompt = self.config.enhance_prompt(prompt, category)
            logger.info(f"🎨 بدء إنشاء صورة: {enhanced_prompt[:50]}...")
            
            # فحص التخزين المؤقت
            if self.config.general_settings['enable_caching']:
                cached_result = await self._get_cached_image(enhanced_prompt)
                if cached_result:
                    logger.info("📦 تم استخدام صورة من التخزين المؤقت")
                    return cached_result
            
            # محاولة APIs حسب الأولوية
            enabled_apis = self.config.get_enabled_apis()
            
            for api_config in enabled_apis:
                try:
                    logger.info(f"🔄 محاولة {api_config.name}...")
                    
                    result = await self._generate_with_api(api_config.name.lower().replace(' ', '_'), enhanced_prompt, article_data)
                    
                    if result:
                        # حفظ في التخزين المؤقت
                        if self.config.general_settings['enable_caching']:
                            await self._cache_image(enhanced_prompt, api_config.name.lower().replace(' ', '_'), result)
                        
                        self.usage_stats['successful_requests'] += 1
                        self.usage_stats['api_usage'][api_config.name.lower().replace(' ', '_')]['successes'] += 1
                        
                        logger.info(f"✅ تم إنشاء صورة بنجاح باستخدام {api_config.name}")
                        return result
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل {api_config.name}: {e}")
                    self.usage_stats['api_usage'][api_config.name.lower().replace(' ', '_')]['failures'] += 1
                    continue
            
            # إذا فشلت جميع APIs الجديدة، استخدم النظام الاحتياطي
            logger.warning("⚠️ فشل في جميع APIs الجديدة، التحول للنظام الاحتياطي...")
            return await self._fallback_to_existing_system(enhanced_prompt, article_data)
            
        except Exception as e:
            logger.error(f"❌ خطأ عام في إنشاء الصورة: {e}")
            self.usage_stats['failed_requests'] += 1
            return None
    
    async def _get_cached_image(self, prompt: str) -> Optional[Dict]:
        """البحث عن صورة في التخزين المؤقت"""
        try:
            for api_name in self.config.apis.keys():
                cache_key = self._generate_cache_key(prompt, api_name)
                if cache_key in self.image_cache:
                    cache_entry = self.image_cache[cache_key]
                    if self._is_cache_valid(cache_entry):
                        return cache_entry['result']
            return None
        except Exception as e:
            logger.warning(f"⚠️ خطأ في البحث في التخزين المؤقت: {e}")
            return None
    
    async def _cache_image(self, prompt: str, api_name: str, result: Dict):
        """حفظ صورة في التخزين المؤقت"""
        try:
            cache_key = self._generate_cache_key(prompt, api_name)
            self.image_cache[cache_key] = {
                'result': result,
                'created_at': datetime.now().isoformat(),
                'prompt': prompt,
                'api_used': api_name
            }
            self._save_cache()
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ التخزين المؤقت: {e}")
    
    async def _generate_with_api(self, api_name: str, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام API محدد"""
        try:
            self.usage_stats['api_usage'][api_name]['requests'] += 1
            
            if api_name == 'openart':
                return await self._generate_with_openart(prompt, article_data)
            elif api_name == 'leap_ai':
                return await self._generate_with_leap_ai(prompt, article_data)
            elif api_name == 'deepai':
                return await self._generate_with_deepai(prompt, article_data)
            elif api_name == 'replicate':
                return await self._generate_with_replicate(prompt, article_data)
            else:
                logger.warning(f"⚠️ API غير مدعوم: {api_name}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في {api_name}: {e}")
            return None
    
    async def _generate_with_openart(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام OpenArt API"""
        try:
            api_config = self.config.get_api_config('openart')
            if not api_config or not api_config.enabled:
                return None
            
            headers = self.config.get_api_headers('openart')
            settings = self.config.api_specific_settings['openart']
            
            payload = {
                'prompt': prompt,
                'model': settings['model'],
                'style': settings['style'],
                'aspect_ratio': settings['aspect_ratio'],
                'guidance_scale': settings['guidance_scale'],
                'num_inference_steps': settings['num_inference_steps'],
                'width': self.config.general_settings['default_width'],
                'height': self.config.general_settings['default_height']
            }
            
            timeout = aiohttp.ClientTimeout(total=api_config.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{api_config.base_url}/generate",
                    headers=headers,
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get('image_url') or result.get('url'):
                            return {
                                'url': result.get('image_url') or result.get('url'),
                                'description': prompt[:100],
                                'source': 'OpenArt',
                                'license': 'AI Generated - Commercial Use',
                                'attribution': 'Generated by OpenArt',
                                'width': self.config.general_settings['default_width'],
                                'height': self.config.general_settings['default_height'],
                                'format': 'PNG',
                                'generation_method': 'openart_api',
                                'api_used': 'openart'
                            }
                    else:
                        logger.warning(f"⚠️ OpenArt API خطأ: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ خطأ في OpenArt API: {e}")
            return None

    async def _generate_with_leap_ai(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام Leap AI API"""
        try:
            api_config = self.config.get_api_config('leap_ai')
            if not api_config or not api_config.enabled:
                return None

            headers = self.config.get_api_headers('leap_ai')
            settings = self.config.api_specific_settings['leap_ai']

            payload = {
                'prompt': prompt,
                'model': settings['model'],
                'style': settings['style'],
                'guidance_scale': settings['guidance_scale'],
                'num_inference_steps': settings['num_inference_steps'],
                'scheduler': settings['scheduler'],
                'width': self.config.general_settings['default_width'],
                'height': self.config.general_settings['default_height']
            }

            timeout = aiohttp.ClientTimeout(total=api_config.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{api_config.base_url}/images/generate",
                    headers=headers,
                    json=payload
                ) as response:

                    if response.status == 200:
                        result = await response.json()

                        if result.get('images') and len(result['images']) > 0:
                            image_data = result['images'][0]
                            return {
                                'url': image_data.get('url'),
                                'description': prompt[:100],
                                'source': 'Leap AI',
                                'license': 'AI Generated - Commercial Use',
                                'attribution': 'Generated by Leap AI',
                                'width': self.config.general_settings['default_width'],
                                'height': self.config.general_settings['default_height'],
                                'format': 'PNG',
                                'generation_method': 'leap_ai_api',
                                'api_used': 'leap_ai'
                            }
                    else:
                        logger.warning(f"⚠️ Leap AI API خطأ: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Leap AI API: {e}")
            return None

    async def _generate_with_deepai(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام DeepAI API"""
        try:
            api_config = self.config.get_api_config('deepai')
            if not api_config or not api_config.enabled:
                return None

            headers = self.config.get_api_headers('deepai')
            settings = self.config.api_specific_settings['deepai']

            # DeepAI يستخدم form data بدلاً من JSON
            data = {
                'text': prompt,
                'grid_size': settings['grid_size'],
                'width': str(settings['width']),
                'height': str(settings['height']),
                'hd': 'true' if settings['style'] == 'hd' else 'false'
            }

            timeout = aiohttp.ClientTimeout(total=api_config.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{api_config.base_url}/text2img",
                    headers=headers,
                    data=data
                ) as response:

                    if response.status == 200:
                        result = await response.json()

                        if result.get('output_url'):
                            return {
                                'url': result['output_url'],
                                'description': prompt[:100],
                                'source': 'DeepAI',
                                'license': 'AI Generated - Free Use',
                                'attribution': 'Generated by DeepAI',
                                'width': settings['width'],
                                'height': settings['height'],
                                'format': 'PNG',
                                'generation_method': 'deepai_api',
                                'api_used': 'deepai'
                            }
                    else:
                        logger.warning(f"⚠️ DeepAI API خطأ: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في DeepAI API: {e}")
            return None

    async def _generate_with_replicate(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """إنشاء صورة باستخدام Replicate API"""
        try:
            api_config = self.config.get_api_config('replicate')
            if not api_config or not api_config.enabled:
                return None

            headers = self.config.get_api_headers('replicate')
            settings = self.config.api_specific_settings['replicate']

            # إنشاء prediction
            payload = {
                'version': settings['model'].split(':')[1],
                'input': {
                    'prompt': prompt,
                    'guidance_scale': settings['guidance_scale'],
                    'num_inference_steps': settings['num_inference_steps'],
                    'scheduler': settings['scheduler'],
                    'width': self.config.general_settings['default_width'],
                    'height': self.config.general_settings['default_height']
                }
            }

            timeout = aiohttp.ClientTimeout(total=api_config.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # إنشاء prediction
                async with session.post(
                    f"{api_config.base_url}/predictions",
                    headers=headers,
                    json=payload
                ) as response:

                    if response.status == 201:
                        prediction = await response.json()
                        prediction_id = prediction.get('id')

                        if prediction_id:
                            # انتظار اكتمال المعالجة
                            return await self._wait_for_replicate_result(session, headers, api_config, prediction_id, prompt)
                    else:
                        logger.warning(f"⚠️ Replicate API خطأ: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Replicate API: {e}")
            return None

    async def _wait_for_replicate_result(self, session, headers, api_config, prediction_id, prompt):
        """انتظار نتيجة Replicate API"""
        try:
            max_attempts = 30  # 30 محاولة (حوالي 5 دقائق)

            for attempt in range(max_attempts):
                await asyncio.sleep(10)  # انتظار 10 ثواني

                async with session.get(
                    f"{api_config.base_url}/predictions/{prediction_id}",
                    headers=headers
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        status = result.get('status')

                        if status == 'succeeded':
                            output = result.get('output')
                            if output and len(output) > 0:
                                return {
                                    'url': output[0] if isinstance(output, list) else output,
                                    'description': prompt[:100],
                                    'source': 'Replicate',
                                    'license': 'AI Generated - Commercial Use',
                                    'attribution': 'Generated by Replicate',
                                    'width': self.config.general_settings['default_width'],
                                    'height': self.config.general_settings['default_height'],
                                    'format': 'PNG',
                                    'generation_method': 'replicate_api',
                                    'api_used': 'replicate'
                                }
                        elif status == 'failed':
                            logger.warning("⚠️ Replicate: فشل في إنشاء الصورة")
                            return None
                        elif status in ['starting', 'processing']:
                            logger.info(f"🔄 Replicate: المعالجة جارية... ({attempt + 1}/{max_attempts})")
                            continue

            logger.warning("⚠️ Replicate: انتهت مهلة الانتظار")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في انتظار نتيجة Replicate: {e}")
            return None

    async def _fallback_to_existing_system(self, prompt: str, article_data: Dict = None) -> Optional[Dict]:
        """العودة للنظام الاحتياطي الموجود"""
        try:
            logger.info("🔄 استخدام النظام الاحتياطي الموجود...")

            # محاولة استيراد النظام الموجود
            try:
                from modules.smart_image_manager import smart_image_manager

                # إنشاء بيانات مقال مبسطة
                article_for_fallback = {
                    'title': article_data.get('title', '') if article_data else '',
                    'content': article_data.get('content', '') if article_data else '',
                    'image_prompts': [prompt]
                }

                result = await smart_image_manager.generate_smart_image_for_article(article_for_fallback)

                if result:
                    # إضافة معلومات إضافية
                    result.update({
                        'fallback_system': True,
                        'advanced_api_failed': True
                    })
                    logger.info("✅ تم إنشاء صورة باستخدام النظام الاحتياطي")
                    return result

            except ImportError:
                logger.warning("⚠️ النظام الاحتياطي غير متوفر")

            # إذا فشل النظام الاحتياطي، استخدم صورة placeholder
            return await self._get_placeholder_image(prompt)

        except Exception as e:
            logger.error(f"❌ خطأ في النظام الاحتياطي: {e}")
            return await self._get_placeholder_image(prompt)

    async def _get_placeholder_image(self, prompt: str) -> Dict:
        """الحصول على صورة placeholder كحل أخير"""
        try:
            # إنشاء صورة placeholder بناءً على الـ prompt
            encoded_prompt = urllib.parse.quote(prompt[:50])
            placeholder_url = f"https://via.placeholder.com/1024x1024/2c3e50/ecf0f1?text={encoded_prompt}"

            return {
                'url': placeholder_url,
                'description': prompt[:100],
                'source': 'Placeholder',
                'license': 'Free Use',
                'attribution': 'Placeholder Image',
                'width': 1024,
                'height': 1024,
                'format': 'PNG',
                'generation_method': 'placeholder',
                'api_used': 'placeholder',
                'fallback_image': True
            }

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء صورة placeholder: {e}")
            return None

    async def test_all_apis(self) -> Dict:
        """اختبار جميع APIs الجديدة"""
        test_results = {
            'overall_status': False,
            'apis': {},
            'test_timestamp': datetime.now().isoformat()
        }

        test_prompt = "A simple gaming controller, digital art style, high quality"

        for api_name, api_config in self.config.apis.items():
            logger.info(f"🧪 اختبار {api_config.name}...")

            test_results['apis'][api_name] = {
                'available': False,
                'enabled': api_config.enabled,
                'has_api_key': bool(api_config.api_key),
                'error': None,
                'response_time': None
            }

            if not api_config.enabled or not api_config.api_key:
                test_results['apis'][api_name]['error'] = 'API غير مفعل أو مفتاح API مفقود'
                continue

            try:
                start_time = time.time()
                result = await self._generate_with_api(api_name, test_prompt)
                response_time = time.time() - start_time

                test_results['apis'][api_name]['response_time'] = response_time

                if result and result.get('url'):
                    test_results['apis'][api_name]['available'] = True
                    logger.info(f"✅ {api_config.name} يعمل بنجاح ({response_time:.2f}s)")
                else:
                    test_results['apis'][api_name]['error'] = 'لم يتم إرجاع صورة صالحة'
                    logger.warning(f"⚠️ {api_config.name} لا يعيد صورة صالحة")

            except Exception as e:
                test_results['apis'][api_name]['error'] = str(e)
                logger.error(f"❌ خطأ في اختبار {api_config.name}: {e}")

        # تحديد الحالة العامة
        test_results['overall_status'] = any(
            api_result['available'] for api_result in test_results['apis'].values()
        )

        return test_results

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            'total_requests': self.usage_stats['total_requests'],
            'successful_requests': self.usage_stats['successful_requests'],
            'failed_requests': self.usage_stats['failed_requests'],
            'success_rate': (
                (self.usage_stats['successful_requests'] / self.usage_stats['total_requests'] * 100)
                if self.usage_stats['total_requests'] > 0 else 0
            ),
            'api_usage': self.usage_stats['api_usage'],
            'daily_usage': self.usage_stats['daily_usage'],
            'cache_size': len(self.image_cache)
        }

    def clear_cache(self):
        """مسح التخزين المؤقت"""
        try:
            self.image_cache = {}
            self._save_cache()
            logger.info("🗑️ تم مسح التخزين المؤقت للصور")
        except Exception as e:
            logger.error(f"❌ خطأ في مسح التخزين المؤقت: {e}")

# إنشاء كائن المولد المتقدم
advanced_image_generator = AdvancedImageGenerator()
