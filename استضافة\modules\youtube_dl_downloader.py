# واجهة youtube-dl و yt-dlp لتحميل الصوت
import asyncio
import subprocess
import os
import json
import time
import tempfile
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import re

from modules.logger import logger
from config.settings import BotConfig

@dataclass
class YoutubeDLResult:
    """نتيجة تحميل youtube-dl"""
    success: bool
    file_path: Optional[str]
    audio_data: Optional[bytes]
    file_size: int
    duration_seconds: float
    format: str
    quality: str
    tool_used: str  # youtube-dl أو yt-dlp
    processing_time: float
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None

class YoutubeDLDownloader:
    """واجهة youtube-dl و yt-dlp لتحميل الصوت"""
    
    def __init__(self):
        self.youtube_dl_path = BotConfig.YOUTUBE_DL_PATH
        self.ffmpeg_path = BotConfig.FFMPEG_PATH
        self.output_dir = BotConfig.YOUTUBE_DL_OUTPUT_DIR
        self.temp_dir = "temp/youtube_dl"
        
        # إنشاء المجلدات
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # فحص توفر الأدوات
        self.youtube_dl_available = self._check_tool_availability("youtube-dl")
        self.yt_dlp_available = self._check_tool_availability("yt-dlp")
        self.ffmpeg_available = self._check_tool_availability(self.ffmpeg_path)
        
        logger.info(f"🟠 تم تهيئة YouTube-DL Downloader")
        logger.info(f"   - youtube-dl: {'✅' if self.youtube_dl_available else '❌'}")
        logger.info(f"   - yt-dlp: {'✅' if self.yt_dlp_available else '❌'}")
        logger.info(f"   - ffmpeg: {'✅' if self.ffmpeg_available else '❌'}")
        
    def _check_tool_availability(self, tool_name: str) -> bool:
        """فحص توفر أداة معينة"""
        try:
            result = subprocess.run(
                [tool_name, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False
            
    def is_available(self) -> bool:
        """فحص ما إذا كانت الخدمة متاحة"""
        return (self.youtube_dl_available or self.yt_dlp_available) and self.ffmpeg_available
        
    def get_preferred_tool(self) -> Optional[str]:
        """الحصول على الأداة المفضلة"""
        if self.yt_dlp_available:
            return "yt-dlp"
        elif self.youtube_dl_available:
            return "youtube-dl"
        else:
            return None
            
    async def download_audio(self, video_url: str, video_id: str = "",
                           quality: str = "medium", format: str = "mp3") -> YoutubeDLResult:
        """تحميل الصوت باستخدام youtube-dl أو yt-dlp"""
        
        start_time = time.time()
        
        if not self.is_available():
            return YoutubeDLResult(
                success=False, file_path=None, audio_data=None,
                file_size=0, duration_seconds=0.0, format=format,
                quality=quality, tool_used="none", processing_time=0.0,
                error_message="youtube-dl/yt-dlp أو ffmpeg غير متوفر"
            )
            
        tool = self.get_preferred_tool()
        if not tool:
            return YoutubeDLResult(
                success=False, file_path=None, audio_data=None,
                file_size=0, duration_seconds=0.0, format=format,
                quality=quality, tool_used="none", processing_time=0.0,
                error_message="لا توجد أدوات تحميل متاحة"
            )
            
        try:
            # إنشاء مجلد مؤقت للتحميل
            with tempfile.TemporaryDirectory(dir=self.temp_dir) as temp_download_dir:
                
                # إعداد معاملات التحميل
                download_options = self._prepare_download_options(
                    video_url, video_id, quality, format, temp_download_dir
                )
                
                # تنفيذ التحميل
                result = await self._execute_download(
                    tool, download_options, temp_download_dir, video_id, format, quality
                )
                
                result.processing_time = time.time() - start_time
                result.tool_used = tool
                
                return result
                
        except Exception as e:
            logger.error(f"❌ خطأ في YouTube-DL downloader: {e}")
            return YoutubeDLResult(
                success=False, file_path=None, audio_data=None,
                file_size=0, duration_seconds=0.0, format=format,
                quality=quality, tool_used=tool, processing_time=time.time() - start_time,
                error_message=f"خطأ في {tool}: {str(e)}"
            )
            
    def _prepare_download_options(self, video_url: str, video_id: str,
                                quality: str, format: str, output_dir: str) -> List[str]:
        """إعداد معاملات التحميل"""
        
        # تحديد جودة الصوت
        quality_settings = BotConfig.AUDIO_QUALITY_SETTINGS.get(quality, 
                                                               BotConfig.AUDIO_QUALITY_SETTINGS["medium"])
        
        # اسم الملف
        filename = f"{video_id or '%(id)s'}.%(ext)s"
        output_template = os.path.join(output_dir, filename)
        
        options = [
            "--extract-audio",
            "--audio-format", format,
            "--audio-quality", quality_settings["audio_quality"],
            "--output", output_template,
            "--no-playlist",
            "--ignore-errors",
            "--no-warnings"
        ]
        
        # إضافة ffmpeg إذا كان متوفراً
        if self.ffmpeg_available and self.ffmpeg_path != "ffmpeg":
            options.extend(["--ffmpeg-location", self.ffmpeg_path])
            
        # إضافة معاملات إضافية للجودة
        if quality == "high":
            options.extend([
                "--format", "bestaudio/best",
                "--prefer-ffmpeg"
            ])
        elif quality == "low":
            options.extend([
                "--format", "worstaudio/worst"
            ])
        else:  # medium
            options.extend([
                "--format", "bestaudio[abr<=128]/best[abr<=128]"
            ])
            
        options.append(video_url)
        
        return options
        
    async def _execute_download(self, tool: str, options: List[str], 
                              temp_dir: str, video_id: str, format: str, quality: str) -> YoutubeDLResult:
        """تنفيذ عملية التحميل"""
        
        try:
            # تنفيذ الأمر
            process = await asyncio.create_subprocess_exec(
                tool, *options,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=temp_dir
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=600)
            
            if process.returncode == 0:
                # البحث عن الملف المحمل
                downloaded_file = self._find_downloaded_file(temp_dir, video_id, format)
                
                if downloaded_file and os.path.exists(downloaded_file):
                    # قراءة الملف
                    with open(downloaded_file, 'rb') as f:
                        audio_data = f.read()
                    
                    # نسخ الملف إلى المجلد النهائي
                    final_path = os.path.join(self.output_dir, os.path.basename(downloaded_file))
                    shutil.copy2(downloaded_file, final_path)
                    
                    # الحصول على معلومات الملف
                    file_size = len(audio_data)
                    duration = self._get_audio_duration(downloaded_file)
                    metadata = self._extract_metadata(stdout.decode('utf-8', errors='ignore'))
                    
                    return YoutubeDLResult(
                        success=True, file_path=final_path, audio_data=audio_data,
                        file_size=file_size, duration_seconds=duration,
                        format=format, quality=quality, tool_used=tool,
                        processing_time=0.0, metadata=metadata
                    )
                else:
                    return YoutubeDLResult(
                        success=False, file_path=None, audio_data=None,
                        file_size=0, duration_seconds=0.0, format=format,
                        quality=quality, tool_used=tool, processing_time=0.0,
                        error_message="لم يتم العثور على الملف المحمل"
                    )
            else:
                error_msg = stderr.decode('utf-8', errors='ignore')
                return YoutubeDLResult(
                    success=False, file_path=None, audio_data=None,
                    file_size=0, duration_seconds=0.0, format=format,
                    quality=quality, tool_used=tool, processing_time=0.0,
                    error_message=f"فشل التحميل: {error_msg}"
                )
                
        except asyncio.TimeoutError:
            return YoutubeDLResult(
                success=False, file_path=None, audio_data=None,
                file_size=0, duration_seconds=0.0, format=format,
                quality=quality, tool_used=tool, processing_time=0.0,
                error_message="انتهت مهلة التحميل"
            )
        except Exception as e:
            return YoutubeDLResult(
                success=False, file_path=None, audio_data=None,
                file_size=0, duration_seconds=0.0, format=format,
                quality=quality, tool_used=tool, processing_time=0.0,
                error_message=f"خطأ في التنفيذ: {str(e)}"
            )
            
    def _find_downloaded_file(self, directory: str, video_id: str, format: str) -> Optional[str]:
        """البحث عن الملف المحمل"""
        try:
            # البحث عن ملفات بالصيغة المطلوبة
            for file in os.listdir(directory):
                if file.endswith(f".{format}"):
                    if video_id and video_id in file:
                        return os.path.join(directory, file)
                    elif not video_id:  # إذا لم يكن هناك video_id محدد
                        return os.path.join(directory, file)
                        
            # البحث عن أي ملف صوتي
            audio_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.aac']
            for file in os.listdir(directory):
                if any(file.lower().endswith(ext) for ext in audio_extensions):
                    return os.path.join(directory, file)
                    
        except Exception as e:
            logger.warning(f"⚠️ خطأ في البحث عن الملف: {e}")
            
        return None
        
    def _get_audio_duration(self, file_path: str) -> float:
        """الحصول على مدة الملف الصوتي"""
        try:
            if not self.ffmpeg_available:
                return 0.0
                
            result = subprocess.run([
                self.ffmpeg_path, "-i", file_path, "-f", "null", "-"
            ], capture_output=True, text=True, timeout=30)
            
            # البحث عن مدة الملف في الإخراج
            duration_match = re.search(r"Duration: (\d+):(\d+):(\d+\.\d+)", result.stderr)
            if duration_match:
                hours = int(duration_match.group(1))
                minutes = int(duration_match.group(2))
                seconds = float(duration_match.group(3))
                return hours * 3600 + minutes * 60 + seconds
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في الحصول على مدة الملف: {e}")
            
        return 0.0
        
    def _extract_metadata(self, output: str) -> Dict[str, Any]:
        """استخراج معلومات إضافية من إخراج الأداة"""
        metadata = {}
        
        try:
            # البحث عن معلومات مفيدة في الإخراج
            lines = output.split('\n')
            for line in lines:
                if 'title:' in line.lower():
                    metadata['title'] = line.split(':', 1)[1].strip()
                elif 'duration:' in line.lower():
                    metadata['duration_str'] = line.split(':', 1)[1].strip()
                elif 'uploader:' in line.lower():
                    metadata['uploader'] = line.split(':', 1)[1].strip()
                    
        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج المعلومات: {e}")
            
        return metadata
        
    def get_tool_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الأدوات"""
        return {
            "youtube_dl_available": self.youtube_dl_available,
            "yt_dlp_available": self.yt_dlp_available,
            "ffmpeg_available": self.ffmpeg_available,
            "preferred_tool": self.get_preferred_tool(),
            "is_available": self.is_available(),
            "output_directory": self.output_dir,
            "supported_formats": ["mp3", "wav", "m4a", "ogg", "aac"],
            "supported_qualities": ["high", "medium", "low"]
        }


# إنشاء مثيل عام
youtube_dl_downloader = YoutubeDLDownloader()
