# مدير النماذج الاحتياطية للبحث العميق
import asyncio
import aiohttp
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass
import logging

from .logger import logger
from config.settings import BotConfig

class AIModelType(Enum):
    """أنواع نماذج الذكاء الاصطناعي المتاحة"""
    GEMINI_2_5_PRO = "gemini_2_5_pro"      # الأولوية الأولى - Gemini 2.5 Pro
    DEEPSEEK_R1 = "deepseek_r1"            # الأولوية الثانية - تفكير عميق + بحث
    GROQ_API = "groq_api"                  # الأولوية الثالثة - سرعة عالية
    GEMINI_2_5_PRO_BACKUP = "gemini_2_5_pro_backup"  # Gemini 2.5 Pro احتياطي

@dataclass
class AIModelConfig:
    """تكوين نموذج الذكاء الاصطناعي"""
    name: str
    api_endpoint: str
    api_key: str
    max_requests_per_day: int
    max_requests_per_minute: int
    supports_web_search: bool
    cost_per_request: float
    quality_score: int  # من 1-10
    speed_score: int    # من 1-10
    
@dataclass
class SearchRequest:
    """طلب بحث للنماذج الاحتياطية"""
    query: str
    max_results: int = 10
    search_depth: str = "standard"  # standard, deep, comprehensive
    include_web_search: bool = True
    language: str = "ar"
    
class FallbackAIManager:
    """مدير النماذج الاحتياطية للبحث العميق"""
    
    def __init__(self):
        self.models = self._initialize_models()
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'models_used': {},
            'daily_usage': {},
            'last_reset': datetime.now().date()
        }
        self.model_status = {}
        self._initialize_model_status()
        
    def _initialize_models(self) -> Dict[AIModelType, AIModelConfig]:
        """تهيئة تكوينات النماذج"""
        return {
            AIModelType.GEMINI_2_5_PRO: AIModelConfig(
                name="Gemini 2.5 Pro",
                api_endpoint="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent",
                api_key=getattr(BotConfig, 'GEMINI_API_KEY', ''),
                max_requests_per_day=500,
                max_requests_per_minute=15,
                supports_web_search=True,
                cost_per_request=0.0,
                quality_score=10,
                speed_score=8
            ),
            AIModelType.DEEPSEEK_R1: AIModelConfig(
                name="DeepSeek R1",
                api_endpoint="https://api.deepseek.com/v1/chat/completions",
                api_key=getattr(BotConfig, 'DEEPSEEK_API_KEY', ''),
                max_requests_per_day=1000,
                max_requests_per_minute=20,
                supports_web_search=True,
                cost_per_request=0.0,
                quality_score=10,
                speed_score=7
            ),
            AIModelType.GROQ_API: AIModelConfig(
                name="Groq API",
                api_endpoint="https://api.groq.com/openai/v1/chat/completions",
                api_key=getattr(BotConfig, 'GROQ_API_KEY', ''),
                max_requests_per_day=2000,
                max_requests_per_minute=30,
                supports_web_search=True,
                cost_per_request=0.0,
                quality_score=8,
                speed_score=10
            ),
            AIModelType.GEMINI_2_5_PRO_BACKUP: AIModelConfig(
                name="Gemini 2.5 Pro Backup",
                api_endpoint="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent",
                api_key=getattr(BotConfig, 'GEMINI_API_KEY', ''),
                max_requests_per_day=1500,
                max_requests_per_minute=15,
                supports_web_search=True,
                cost_per_request=0.0,
                quality_score=10,
                speed_score=9
            )
        }
    
    def _initialize_model_status(self):
        """تهيئة حالة النماذج"""
        for model_type in AIModelType:
            self.model_status[model_type] = {
                'available': True,
                'last_used': None,
                'consecutive_failures': 0,
                'daily_requests': 0,
                'minute_requests': 0,
                'last_minute_reset': datetime.now(),
                'blacklisted_until': None
            }
    
    async def search_with_fallback(self, request: SearchRequest) -> Optional[Dict]:
        """البحث مع النماذج الاحتياطية"""
        self._reset_daily_stats_if_needed()
        
        # ترتيب النماذج حسب الأولوية والتوفر
        available_models = self._get_available_models_by_priority()
        
        if not available_models:
            logger.error("❌ لا توجد نماذج متاحة للبحث")
            logger.info("🔄 محاولة إعادة تعيين النماذج...")
            self._reset_failed_models()
            available_models = self._get_available_models_by_priority()

            if not available_models:
                logger.error("❌ لا توجد نماذج متاحة حتى بعد إعادة التعيين")
                return None
        
        logger.info(f"🔍 بدء البحث الاحتياطي مع {len(available_models)} نموذج متاح")
        
        for model_type in available_models:
            try:
                logger.info(f"🤖 محاولة البحث باستخدام {self.models[model_type].name}")
                
                result = await self._search_with_model(model_type, request)
                
                if result and result.get('success'):
                    self._record_successful_request(model_type)
                    logger.info(f"✅ نجح البحث باستخدام {self.models[model_type].name}")
                    return result
                else:
                    self._record_failed_request(model_type)
                    logger.warning(f"⚠️ فشل البحث باستخدام {self.models[model_type].name}")
                    
            except Exception as e:
                self._record_failed_request(model_type)
                logger.error(f"❌ خطأ في البحث باستخدام {self.models[model_type].name}: {e}")
                
                # إذا كان الخطأ متعلق بالحدود، قم بتعطيل النموذج مؤقتاً
                if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                    self._temporarily_blacklist_model(model_type, hours=1)
                
                continue
        
        logger.error("❌ فشلت جميع النماذج الاحتياطية في البحث")
        return None
    
    def _get_available_models_by_priority(self) -> List[AIModelType]:
        """الحصول على النماذج المتاحة مرتبة حسب الأولوية"""
        available = []

        # ترتيب النماذج: Gemini 2.5 Pro أولاً، ثم البدائل، ثم Gemini 2.5 Pro الاحتياطي
        for model_type in [AIModelType.GEMINI_2_5_PRO, AIModelType.DEEPSEEK_R1,
                          AIModelType.GROQ_API, AIModelType.GEMINI_2_5_PRO_BACKUP]:
            if self._is_model_available(model_type):
                available.append(model_type)

        return available

    def _reset_failed_models(self):
        """إعادة تعيين النماذج الفاشلة للسماح بإعادة المحاولة"""
        for model_type in AIModelType:
            status = self.model_status[model_type]
            if status['consecutive_failures'] >= 3:
                logger.info(f"🔄 إعادة تعيين فشل {self.models[model_type].name}")
                status['consecutive_failures'] = 0
                status['blacklisted_until'] = None

    def _is_model_available(self, model_type: AIModelType) -> bool:
        """فحص توفر النموذج"""
        status = self.model_status[model_type]
        config = self.models[model_type]

        # فحص القائمة السوداء
        if status['blacklisted_until'] and datetime.now() < status['blacklisted_until']:
            logger.debug(f"❌ {config.name} في القائمة السوداء حتى {status['blacklisted_until']}")
            return False

        # فحص الحدود اليومية
        if status['daily_requests'] >= config.max_requests_per_day:
            logger.debug(f"❌ {config.name} تجاوز الحد اليومي ({status['daily_requests']}/{config.max_requests_per_day})")
            return False

        # فحص الحدود الدقيقية
        if self._check_minute_limit(model_type):
            logger.debug(f"❌ {config.name} تجاوز الحد الدقيقي")
            return False

        # فحص الفشل المتتالي مع إعادة تعيين بعد فترة
        if status['consecutive_failures'] >= 3:
            # إعادة تعيين الفشل المتتالي بعد 30 دقيقة
            if status['last_used'] and (datetime.now() - status['last_used']).total_seconds() > 1800:
                logger.info(f"🔄 إعادة تعيين فشل {config.name} بعد 30 دقيقة")
                status['consecutive_failures'] = 0
            else:
                logger.debug(f"❌ {config.name} فشل متتالي ({status['consecutive_failures']} مرات)")
                return False

        # فحص وجود المفتاح
        if not config.api_key:
            logger.debug(f"❌ {config.name} لا يوجد مفتاح API")
            return False

        logger.debug(f"✅ {config.name} متاح للاستخدام")
        return True
    
    def _check_minute_limit(self, model_type: AIModelType) -> bool:
        """فحص حد الطلبات في الدقيقة"""
        status = self.model_status[model_type]
        config = self.models[model_type]
        
        now = datetime.now()
        if (now - status['last_minute_reset']).total_seconds() >= 60:
            status['minute_requests'] = 0
            status['last_minute_reset'] = now
        
        return status['minute_requests'] >= config.max_requests_per_minute
    
    def _reset_daily_stats_if_needed(self):
        """إعادة تعيين الإحصائيات اليومية إذا لزم الأمر"""
        today = datetime.now().date()
        if self.usage_stats['last_reset'] != today:
            self.usage_stats['last_reset'] = today
            self.usage_stats['daily_usage'] = {}
            
            # إعادة تعيين العدادات اليومية للنماذج
            for model_type in AIModelType:
                self.model_status[model_type]['daily_requests'] = 0
                self.model_status[model_type]['consecutive_failures'] = 0
                
            logger.info("🔄 تم إعادة تعيين الإحصائيات اليومية")
    
    def _temporarily_blacklist_model(self, model_type: AIModelType, hours: int = 1):
        """تعطيل النموذج مؤقتاً"""
        blacklist_until = datetime.now() + timedelta(hours=hours)
        self.model_status[model_type]['blacklisted_until'] = blacklist_until
        logger.warning(f"⚠️ تم تعطيل {self.models[model_type].name} مؤقتاً حتى {blacklist_until}")
    
    def _record_successful_request(self, model_type: AIModelType):
        """تسجيل طلب ناجح"""
        self.usage_stats['total_requests'] += 1
        self.usage_stats['successful_requests'] += 1
        self.usage_stats['models_used'][model_type.value] = self.usage_stats['models_used'].get(model_type.value, 0) + 1
        
        status = self.model_status[model_type]
        status['last_used'] = datetime.now()
        status['consecutive_failures'] = 0
        status['daily_requests'] += 1
        status['minute_requests'] += 1
    
    def _record_failed_request(self, model_type: AIModelType):
        """تسجيل طلب فاشل"""
        self.usage_stats['total_requests'] += 1
        self.usage_stats['failed_requests'] += 1
        
        status = self.model_status[model_type]
        status['consecutive_failures'] += 1
        status['daily_requests'] += 1
        status['minute_requests'] += 1
    
    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            **self.usage_stats,
            'model_status': {
                model_type.value: {
                    'available': self._is_model_available(model_type),
                    'daily_requests': self.model_status[model_type]['daily_requests'],
                    'consecutive_failures': self.model_status[model_type]['consecutive_failures'],
                    'last_used': self.model_status[model_type]['last_used']
                }
                for model_type in AIModelType
            }
        }

    async def _search_with_model(self, model_type: AIModelType, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام نموذج محدد"""
        config = self.models[model_type]

        if model_type == AIModelType.GEMINI_2_5_PRO:
            return await self._search_with_gemini_2_5_pro(config, request)
        elif model_type == AIModelType.DEEPSEEK_R1:
            return await self._search_with_deepseek_r1(config, request)
        elif model_type == AIModelType.GROQ_API:
            return await self._search_with_groq(config, request)
        elif model_type == AIModelType.GEMINI_2_5_PRO_BACKUP:
            return await self._search_with_gemini_2_5_pro_backup(config, request)

        return None

    def _validate_response_data(self, data: Dict, model_name: str) -> bool:
        """التحقق من صحة بيانات الاستجابة"""
        try:
            if not isinstance(data, dict):
                logger.warning(f"⚠️ {model_name}: الاستجابة ليست dictionary")
                return False

            if not data:
                logger.warning(f"⚠️ {model_name}: الاستجابة فارغة")
                return False

            # فحص وجود رسائل خطأ
            if 'error' in data:
                error_info = data['error']
                logger.error(f"❌ {model_name}: خطأ في الاستجابة - {error_info}")
                return False

            # فحص وجود المرشحين
            if 'candidates' not in data:
                logger.warning(f"⚠️ {model_name}: لا توجد مرشحين في الاستجابة")
                return False

            candidates = data['candidates']
            if not candidates or len(candidates) == 0:
                logger.warning(f"⚠️ {model_name}: قائمة المرشحين فارغة")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من صحة الاستجابة لـ {model_name}: {e}")
            return False

    async def _search_with_gemini_2_5_pro(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Gemini 2.5 Pro"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': config.api_key
            }

            # إنشاء prompt للتحليل العميق
            search_prompt = f"""
            أنت خبير في أخبار الألعاب. قدم تحليلاً شاملاً ومفصلاً حول: {request.query}

            يرجى تقديم:
            1. ملخص شامل للموضوع
            2. أحدث التطورات والأخبار المتاحة
            3. مصادر موثوقة ومراجع
            4. تحليل عميق للمعلومات

            استخدم أحدث المعلومات المتاحة لديك وقدم تحليلاً مفيداً وشاملاً.
            """

            payload = {
                "contents": [{
                    "parts": [{"text": search_prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 4096,
                },
                "systemInstruction": {
                    "parts": [{"text": "أجب مباشرة بدون تفكير مطول. قدم إجابة مفيدة ومختصرة."}]
                }
            }

            # إضافة timeout محسن
            timeout = aiohttp.ClientTimeout(total=60, connect=10)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        # التحقق من صحة الاستجابة
                        if not self._validate_response_data(data, "Gemini 2.5 Pro"):
                            return None

                        # تسجيل مفصل لفهم بنية الاستجابة
                        logger.debug(f"🔍 بنية استجابة Gemini 2.5 Pro: {list(data.keys())}")

                        # محاولة استخراج المحتوى بطرق مختلفة ومحسنة
                        content = None

                        # الطريقة الأولى: البنية المعتادة
                        if 'candidates' in data and data['candidates']:
                            candidate = data['candidates'][0]
                            logger.debug(f"🔍 بنية المرشح: {list(candidate.keys())}")

                            # فحص سبب الانتهاء
                            finish_reason = candidate.get('finishReason', '')
                            if finish_reason == 'MAX_TOKENS':
                                logger.warning("⚠️ Gemini وصل للحد الأقصى من الرموز - قد يكون المحتوى مقطوع")

                            # فحص البنية الكاملة للمرشح
                            if 'content' in candidate:
                                content_obj = candidate['content']
                                logger.debug(f"🔍 بنية المحتوى: {list(content_obj.keys()) if isinstance(content_obj, dict) else type(content_obj)}")

                                if isinstance(content_obj, dict) and 'parts' in content_obj:
                                    parts = content_obj['parts']
                                    if parts and len(parts) > 0:
                                        for part in parts:
                                            if isinstance(part, dict) and 'text' in part:
                                                content = part['text']
                                                logger.info("✅ تم استخراج المحتوى بالطريقة المعتادة")
                                                break
                                elif isinstance(content_obj, str):
                                    content = content_obj
                                    logger.info("✅ تم استخراج المحتوى كنص مباشر")
                                elif isinstance(content_obj, dict) and 'role' in content_obj:
                                    # حالة خاصة: المحتوى يحتوي على role فقط بدون parts
                                    logger.warning("⚠️ المحتوى يحتوي على role فقط - قد يكون هناك مشكلة في التوليد")
                                    # محاولة البحث عن نص في مكان آخر
                                    if 'text' in content_obj:
                                        content = content_obj['text']
                                        logger.info("✅ تم استخراج المحتوى من role object")

                            # الطريقة الثانية: محاولة استخراج مباشر من المرشح
                            if not content and 'text' in candidate:
                                content = candidate['text']
                                logger.info("✅ تم استخراج المحتوى مباشرة من المرشح")

                            # الطريقة الثالثة: البحث العميق في جميع المفاتيح
                            if not content:
                                def extract_text_recursively(obj, path=""):
                                    """استخراج النص بشكل تكراري من البنية المعقدة"""
                                    if isinstance(obj, str) and len(obj.strip()) > 50:
                                        return obj.strip()
                                    elif isinstance(obj, dict):
                                        for key, value in obj.items():
                                            if key == 'text' and isinstance(value, str) and len(value.strip()) > 20:
                                                return value.strip()
                                        # البحث التكراري في القيم
                                        for key, value in obj.items():
                                            result = extract_text_recursively(value, f"{path}.{key}")
                                            if result:
                                                return result
                                    elif isinstance(obj, list):
                                        for i, item in enumerate(obj):
                                            result = extract_text_recursively(item, f"{path}[{i}]")
                                            if result:
                                                return result
                                    return None

                                content = extract_text_recursively(candidate, "candidate")
                                if content:
                                    logger.info("✅ تم استخراج المحتوى بالبحث العميق")

                        # الطريقة الرابعة: البحث في الجذر
                        if not content and 'text' in data:
                            content = data['text']
                            logger.info("✅ تم استخراج المحتوى من الجذر")

                        # الطريقة الخامسة: البحث العميق في جميع مفاتيح الجذر
                        if not content:
                            def extract_text_from_root(obj, path="root"):
                                """استخراج النص من الجذر بشكل تكراري"""
                                if isinstance(obj, str) and len(obj.strip()) > 50:
                                    return obj.strip()
                                elif isinstance(obj, dict):
                                    # أولوية للمفاتيح المحتملة
                                    priority_keys = ['text', 'content', 'message', 'response', 'output', 'result']
                                    for key in priority_keys:
                                        if key in obj and isinstance(obj[key], str) and len(obj[key].strip()) > 20:
                                            return obj[key].strip()
                                    # البحث في باقي المفاتيح
                                    for key, value in obj.items():
                                        if key not in priority_keys:
                                            result = extract_text_from_root(value, f"{path}.{key}")
                                            if result:
                                                return result
                                elif isinstance(obj, list):
                                    for i, item in enumerate(obj):
                                        result = extract_text_from_root(item, f"{path}[{i}]")
                                        if result:
                                            return result
                                return None

                            content = extract_text_from_root(data)
                            if content:
                                logger.info("✅ تم استخراج المحتوى بالبحث العميق في الجذر")

                        # إذا لم نجد محتوى، جرب طرق إضافية
                        if not content:
                            logger.warning("⚠️ لم يتم العثور على محتوى بالطرق العادية، جاري المحاولة بطرق إضافية...")

                            # فحص إذا كانت المشكلة بسبب MAX_TOKENS
                            if 'candidates' in data and data['candidates']:
                                candidate = data['candidates'][0]
                                finish_reason = candidate.get('finishReason', '')

                                if finish_reason == 'MAX_TOKENS':
                                    logger.info("🔄 إعادة المحاولة بـ prompt أقصر بسبب MAX_TOKENS...")

                                    # إنشاء prompt أقصر
                                    short_prompt = f"أجب بإيجاز عن: {request.query}"

                                    short_payload = {
                                        "contents": [{
                                            "parts": [{"text": short_prompt}]
                                        }],
                                        "generationConfig": {
                                            "temperature": 0.5,
                                            "maxOutputTokens": 1024,
                                        }
                                    }

                                    try:
                                        async with session.post(config.api_endpoint, headers=headers, json=short_payload) as retry_response:
                                            if retry_response.status == 200:
                                                retry_data = await retry_response.json()
                                                if self._validate_response_data(retry_data, "Gemini 2.5 Pro (Retry)"):
                                                    # استخراج المحتوى من المحاولة الثانية
                                                    retry_candidates = retry_data.get('candidates', [])
                                                    if retry_candidates:
                                                        retry_candidate = retry_candidates[0]
                                                        retry_content_obj = retry_candidate.get('content', {})
                                                        if isinstance(retry_content_obj, dict) and 'parts' in retry_content_obj:
                                                            retry_parts = retry_content_obj['parts']
                                                            if retry_parts and len(retry_parts) > 0:
                                                                for part in retry_parts:
                                                                    if isinstance(part, dict) and 'text' in part:
                                                                        content = part['text']
                                                                        logger.info("✅ نجحت المحاولة الثانية بـ prompt أقصر")
                                                                        break
                                    except Exception as retry_error:
                                        logger.warning(f"⚠️ فشلت المحاولة الثانية: {retry_error}")

                            # إذا لم تنجح المحاولة الثانية، جرب البحث العام
                            if not content:
                                # طريقة إضافية: البحث عن أي نص طويل في الاستجابة
                                def find_any_meaningful_text(obj, min_length=30):
                                    """البحث عن أي نص ذو معنى في الاستجابة"""
                                    if isinstance(obj, str):
                                        text = obj.strip()
                                        if len(text) >= min_length and not any(error_word in text.lower() for error_word in ['error', 'خطأ', 'فشل']):
                                            return text
                                    elif isinstance(obj, dict):
                                        for value in obj.values():
                                            result = find_any_meaningful_text(value, min_length)
                                            if result:
                                                return result
                                    elif isinstance(obj, list):
                                        for item in obj:
                                            result = find_any_meaningful_text(item, min_length)
                                            if result:
                                                return result
                                    return None

                                content = find_any_meaningful_text(data)
                                if content:
                                    logger.info("✅ تم العثور على محتوى بالطريقة الإضافية")

                        if content and len(content.strip()) > 20:
                            return {
                                'success': True,
                                'content': content.strip(),
                                'source': 'Gemini 2.5 Pro',
                                'model_type': 'gemini_2.5_pro',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query
                            }
                        else:
                            logger.warning("⚠️ لم يتم العثور على محتوى نصي مفيد في استجابة Gemini 2.5 Pro")
                            logger.warning(f"🔍 البيانات الكاملة: {data}")

                            # محاولة أخيرة: تسجيل تفصيلي للاستجابة
                            logger.warning("🔍 تحليل تفصيلي للاستجابة:")
                            if isinstance(data, dict):
                                for key, value in data.items():
                                    logger.warning(f"  - {key}: {type(value)} - {str(value)[:200]}...")

                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Gemini 2.5 Pro: {response.status} - {error_text}")

                        # معالجة أخطاء محددة
                        if response.status == 429:
                            logger.warning("⚠️ تم تجاوز حد الطلبات لـ Gemini 2.5 Pro")
                        elif response.status == 403:
                            logger.warning("⚠️ مشكلة في مفتاح API لـ Gemini 2.5 Pro")
                        elif response.status == 400:
                            logger.warning("⚠️ طلب غير صحيح لـ Gemini 2.5 Pro")

                        return None

        except asyncio.TimeoutError:
            logger.error("❌ انتهت مهلة الانتظار لـ Gemini 2.5 Pro")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"❌ خطأ في الاتصال بـ Gemini 2.5 Pro: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في Gemini 2.5 Pro: {e}")
            return None

    async def _search_with_deepseek_r1(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام DeepSeek R1 مع التفكير العميق"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {config.api_key}'
            }

            # إنشاء prompt للتفكير العميق والبحث
            deep_thinking_prompt = f"""
            أنت محلل خبير في أخبار الألعاب مع قدرات تفكير عميق وبحث على الإنترنت.

            المهمة: ابحث وحلل بعمق الموضوع التالي: {request.query}

            استخدم التفكير العميق لـ:
            1. تحليل السياق والخلفية
            2. البحث عن أحدث المعلومات
            3. ربط المعلومات ببعضها البعض
            4. تقديم رؤى عميقة ومفيدة

            قدم تحليلاً شاملاً ومفصلاً مع مصادر موثوقة.
            """

            payload = {
                "model": "deepseek-reasoner",
                "messages": [
                    {
                        "role": "user",
                        "content": deep_thinking_prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 2048,
                "stream": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'choices' in data and data['choices']:
                            content = data['choices'][0]['message']['content']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'DeepSeek R1',
                                'model_type': 'deepseek_r1',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'reasoning_included': True
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ DeepSeek R1: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في DeepSeek R1: {e}")
            return None

    async def _search_with_groq(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Groq API للمعالجة السريعة"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {config.api_key}'
            }

            # إنشاء prompt للبحث السريع
            fast_search_prompt = f"""
            أنت محرك بحث سريع ودقيق لأخبار الألعاب.

            ابحث بسرعة عن: {request.query}

            قدم:
            1. ملخص سريع ودقيق
            2. أهم النقاط والتطورات
            3. معلومات حديثة ومفيدة
            4. مصادر موثوقة

            ركز على السرعة والدقة.
            """

            payload = {
                "model": "llama-3.1-70b-versatile",
                "messages": [
                    {
                        "role": "user",
                        "content": fast_search_prompt
                    }
                ],
                "temperature": 0.5,
                "max_tokens": 1024,
                "stream": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'choices' in data and data['choices']:
                            content = data['choices'][0]['message']['content']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'Groq API',
                                'model_type': 'groq_api',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'fast_processing': True
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Groq API: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Groq API: {e}")
            return None

    async def _search_with_gemini_2_5_pro_backup(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Gemini 2.5 Pro كحل احتياطي"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': config.api_key
            }

            # إنشاء prompt بسيط للبحث الاحتياطي
            fallback_prompt = f"""
            أنت مساعد ذكي متخصص في أخبار الألعاب.

            الموضوع: {request.query}

            قدم معلومات مفيدة حول هذا الموضوع بناءً على معرفتك:
            1. معلومات عامة حول الموضوع
            2. السياق والخلفية
            3. نقاط مهمة يجب معرفتها
            4. توقعات أو تحليلات مفيدة

            ملاحظة: هذا بحث احتياطي بدون اتصال بالإنترنت.
            """

            payload = {
                "contents": [{
                    "parts": [{"text": fallback_prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.6,
                    "topK": 32,
                    "topP": 0.9,
                    "maxOutputTokens": 2048,
                },
                "systemInstruction": {
                    "parts": [{"text": "أجب مباشرة بدون تفكير مطول. قدم إجابة مفيدة ومختصرة."}]
                }
            }

            # إضافة timeout محسن للنسخة الاحتياطية
            timeout = aiohttp.ClientTimeout(total=45, connect=8)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        # التحقق من صحة الاستجابة للنسخة الاحتياطية
                        if not self._validate_response_data(data, "Gemini 2.5 Pro Backup"):
                            return None

                        # تسجيل مفصل لفهم بنية الاستجابة
                        logger.debug(f"🔍 بنية استجابة Gemini 2.5 Pro Backup: {list(data.keys())}")

                        # محاولة استخراج المحتوى بطرق مختلفة ومحسنة (Backup)
                        content = None

                        # الطريقة الأولى: البنية المعتادة
                        if 'candidates' in data and data['candidates']:
                            candidate = data['candidates'][0]
                            logger.debug(f"🔍 بنية المرشح Backup: {list(candidate.keys())}")

                            # فحص البنية الكاملة للمرشح
                            if 'content' in candidate:
                                content_obj = candidate['content']
                                logger.debug(f"🔍 بنية المحتوى Backup: {list(content_obj.keys()) if isinstance(content_obj, dict) else type(content_obj)}")

                                if isinstance(content_obj, dict) and 'parts' in content_obj:
                                    parts = content_obj['parts']
                                    if parts and len(parts) > 0:
                                        for part in parts:
                                            if isinstance(part, dict) and 'text' in part:
                                                content = part['text']
                                                logger.info("✅ تم استخراج المحتوى بالطريقة المعتادة (Backup)")
                                                break
                                elif isinstance(content_obj, str):
                                    content = content_obj
                                    logger.info("✅ تم استخراج المحتوى كنص مباشر (Backup)")

                            # الطريقة الثانية: محاولة استخراج مباشر من المرشح
                            if not content and 'text' in candidate:
                                content = candidate['text']
                                logger.info("✅ تم استخراج المحتوى مباشرة من المرشح (Backup)")

                            # الطريقة الثالثة: البحث العميق في جميع المفاتيح
                            if not content:
                                def extract_text_recursively_backup(obj, path=""):
                                    """استخراج النص بشكل تكراري من البنية المعقدة (Backup)"""
                                    if isinstance(obj, str) and len(obj.strip()) > 50:
                                        return obj.strip()
                                    elif isinstance(obj, dict):
                                        for key, value in obj.items():
                                            if key == 'text' and isinstance(value, str) and len(value.strip()) > 20:
                                                return value.strip()
                                        # البحث التكراري في القيم
                                        for key, value in obj.items():
                                            result = extract_text_recursively_backup(value, f"{path}.{key}")
                                            if result:
                                                return result
                                    elif isinstance(obj, list):
                                        for i, item in enumerate(obj):
                                            result = extract_text_recursively_backup(item, f"{path}[{i}]")
                                            if result:
                                                return result
                                    return None

                                content = extract_text_recursively_backup(candidate, "candidate")
                                if content:
                                    logger.info("✅ تم استخراج المحتوى بالبحث العميق (Backup)")

                        # الطريقة الرابعة: البحث في الجذر
                        if not content and 'text' in data:
                            content = data['text']
                            logger.info("✅ تم استخراج المحتوى من الجذر (Backup)")

                        # الطريقة الخامسة: البحث العميق في جميع مفاتيح الجذر
                        if not content:
                            def extract_text_from_root_backup(obj, path="root"):
                                """استخراج النص من الجذر بشكل تكراري (Backup)"""
                                if isinstance(obj, str) and len(obj.strip()) > 50:
                                    return obj.strip()
                                elif isinstance(obj, dict):
                                    # أولوية للمفاتيح المحتملة
                                    priority_keys = ['text', 'content', 'message', 'response', 'output', 'result']
                                    for key in priority_keys:
                                        if key in obj and isinstance(obj[key], str) and len(obj[key].strip()) > 20:
                                            return obj[key].strip()
                                    # البحث في باقي المفاتيح
                                    for key, value in obj.items():
                                        if key not in priority_keys:
                                            result = extract_text_from_root_backup(value, f"{path}.{key}")
                                            if result:
                                                return result
                                elif isinstance(obj, list):
                                    for i, item in enumerate(obj):
                                        result = extract_text_from_root_backup(item, f"{path}[{i}]")
                                        if result:
                                            return result
                                return None

                            content = extract_text_from_root_backup(data)
                            if content:
                                logger.info("✅ تم استخراج المحتوى بالبحث العميق في الجذر (Backup)")

                        # إذا لم نجد محتوى، جرب طرق إضافية (Backup)
                        if not content:
                            logger.warning("⚠️ لم يتم العثور على محتوى بالطرق العادية، جاري المحاولة بطرق إضافية (Backup)...")

                            # طريقة إضافية: البحث عن أي نص طويل في الاستجابة
                            def find_any_meaningful_text_backup(obj, min_length=30):
                                """البحث عن أي نص ذو معنى في الاستجابة (Backup)"""
                                if isinstance(obj, str):
                                    text = obj.strip()
                                    if len(text) >= min_length and not any(error_word in text.lower() for error_word in ['error', 'خطأ', 'فشل']):
                                        return text
                                elif isinstance(obj, dict):
                                    for value in obj.values():
                                        result = find_any_meaningful_text_backup(value, min_length)
                                        if result:
                                            return result
                                elif isinstance(obj, list):
                                    for item in obj:
                                        result = find_any_meaningful_text_backup(item, min_length)
                                        if result:
                                            return result
                                return None

                            content = find_any_meaningful_text_backup(data)
                            if content:
                                logger.info("✅ تم العثور على محتوى بالطريقة الإضافية (Backup)")

                        if content and len(content.strip()) > 20:
                            return {
                                'success': True,
                                'content': content.strip(),
                                'source': 'Gemini 2.5 Pro Backup',
                                'model_type': 'gemini_2_5_pro_backup',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'fallback_mode': True
                            }
                        else:
                            logger.warning("⚠️ لم يتم العثور على محتوى نصي مفيد في استجابة Gemini 2.5 Pro Backup")
                            logger.warning(f"🔍 البيانات الكاملة (Backup): {data}")

                            # محاولة أخيرة: تسجيل تفصيلي للاستجابة
                            logger.warning("🔍 تحليل تفصيلي للاستجابة (Backup):")
                            if isinstance(data, dict):
                                for key, value in data.items():
                                    logger.warning(f"  - {key}: {type(value)} - {str(value)[:200]}...")

                            return None
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Gemini 2.5 Pro Backup: {response.status} - {error_text}")

                        # معالجة أخطاء محددة للنسخة الاحتياطية
                        if response.status == 429:
                            logger.warning("⚠️ تم تجاوز حد الطلبات لـ Gemini 2.5 Pro Backup")
                        elif response.status == 403:
                            logger.warning("⚠️ مشكلة في مفتاح API لـ Gemini 2.5 Pro Backup")
                        elif response.status == 400:
                            logger.warning("⚠️ طلب غير صحيح لـ Gemini 2.5 Pro Backup")

                        return None

        except asyncio.TimeoutError:
            logger.error("❌ انتهت مهلة الانتظار لـ Gemini 2.5 Pro Backup")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"❌ خطأ في الاتصال بـ Gemini 2.5 Pro Backup: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع في Gemini 2.5 Pro Backup: {e}")
            return None

    def get_model_recommendations(self) -> Dict:
        """الحصول على توصيات حول أفضل النماذج للاستخدام"""
        recommendations = {
            'best_for_web_search': AIModelType.GEMINI_2_5_PRO.value,
            'best_for_deep_analysis': AIModelType.DEEPSEEK_R1.value,
            'best_for_speed': AIModelType.GROQ_API.value,
            'most_reliable_fallback': AIModelType.GEMINI_2_5_PRO_BACKUP.value,
            'current_status': {}
        }

        for model_type in AIModelType:
            config = self.models[model_type]
            status = self.model_status[model_type]

            recommendations['current_status'][model_type.value] = {
                'available': self._is_model_available(model_type),
                'quality_score': config.quality_score,
                'speed_score': config.speed_score,
                'daily_usage': status['daily_requests'],
                'max_daily': config.max_requests_per_day,
                'usage_percentage': (status['daily_requests'] / config.max_requests_per_day) * 100
            }

        return recommendations

    async def test_all_models(self) -> Dict:
        """اختبار جميع النماذج للتأكد من عملها"""
        test_request = SearchRequest(
            query="test gaming news",
            max_results=1,
            search_depth="standard"
        )

        test_results = {}

        for model_type in AIModelType:
            if not self._is_model_available(model_type):
                test_results[model_type.value] = {
                    'status': 'unavailable',
                    'reason': 'Model not available (rate limits, blacklisted, or no API key)'
                }
                continue

            try:
                logger.info(f"🧪 اختبار {self.models[model_type].name}...")
                start_time = time.time()

                result = await self._search_with_model(model_type, test_request)

                end_time = time.time()
                response_time = end_time - start_time

                if result and result.get('success'):
                    test_results[model_type.value] = {
                        'status': 'success',
                        'response_time': response_time,
                        'content_length': len(result.get('content', '')),
                        'supports_web_search': result.get('supports_web_search', False)
                    }
                else:
                    test_results[model_type.value] = {
                        'status': 'failed',
                        'reason': 'No valid response received'
                    }

            except Exception as e:
                test_results[model_type.value] = {
                    'status': 'error',
                    'reason': str(e)
                }

        return test_results

# إنشاء مثيل عام
fallback_ai_manager = FallbackAIManager()
