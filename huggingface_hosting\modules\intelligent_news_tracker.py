# نظام تتبع الأخبار الذكي
import asyncio
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import hashlib

from .logger import logger
from .enhanced_search_integration import enhanced_search
from .database import db

@dataclass
class NewsStory:
    """قصة إخبارية"""
    title: str
    summary: str
    keywords: List[str]
    importance_score: float
    first_seen: datetime
    last_updated: datetime
    related_articles: List[Dict]
    follow_up_searches: List[str]
    is_breaking: bool = False
    needs_follow_up: bool = True

class IntelligentNewsTracker:
    """نظام تتبع الأخبار الذكي"""
    
    def __init__(self):
        self.tracked_stories = {}  # تتبع القصص الإخبارية
        self.follow_up_queue = []  # قائمة انتظار المتابعة
        
        # إعدادات التتبع
        self.settings = {
            'importance_threshold': 70.0,  # حد الأهمية للمتابعة
            'max_follow_ups': 3,           # عدد محاولات المتابعة القصوى
            'follow_up_delay': 300,        # تأخير 5 دقائق بين المتابعات
            'story_expiry_hours': 48,      # انتهاء صلاحية القصة بعد 48 ساعة
            'min_related_articles': 2,     # الحد الأدنى للمقالات المرتبطة
        }
        
        # كلمات مفتاحية تدل على الأهمية
        self.importance_indicators = {
            'breaking': 10.0,
            'exclusive': 8.0,
            'announcement': 7.0,
            'release': 6.0,
            'update': 5.0,
            'reveal': 7.0,
            'confirmed': 6.0,
            'official': 6.0,
            'leaked': 8.0,
            'rumor': 3.0,
            'speculation': 2.0
        }
        
        logger.info("🔍 تم تهيئة نظام تتبع الأخبار الذكي")
    
    async def analyze_and_track_news(self, articles: List[Dict]) -> List[Dict]:
        """تحليل وتتبع الأخبار الجديدة"""
        try:
            logger.info(f"🔍 تحليل {len(articles)} مقال للبحث عن أخبار مهمة...")
            
            enhanced_articles = []
            
            for article in articles:
                try:
                    # تحليل أهمية المقال
                    importance_score = self._calculate_importance_score(article)
                    
                    # إذا كان المقال مهم، ابحث عن المزيد من المعلومات
                    if importance_score >= self.settings['importance_threshold']:
                        logger.info(f"📰 مقال مهم تم اكتشافه: {article.get('title', '')[:60]}...")
                        
                        # البحث عن معلومات إضافية
                        enhanced_article = await self._enhance_article_with_follow_up(article, importance_score)
                        enhanced_articles.append(enhanced_article)
                        
                        # تتبع القصة للمتابعة المستقبلية
                        await self._track_story(enhanced_article, importance_score)
                    else:
                        # مقال عادي، أضفه كما هو
                        enhanced_articles.append(article)
                
                except Exception as e:
                    logger.warning(f"⚠️ فشل في تحليل مقال: {e}")
                    enhanced_articles.append(article)
                    continue
            
            # معالجة قائمة انتظار المتابعة
            await self._process_follow_up_queue()
            
            logger.info(f"✅ تم تحليل وتعزيز {len(enhanced_articles)} مقال")
            return enhanced_articles
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل وتتبع الأخبار: {e}")
            return articles
    
    def _calculate_importance_score(self, article: Dict) -> float:
        """حساب نقاط أهمية المقال"""
        try:
            score = 0.0
            
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            summary = article.get('summary', '').lower()
            
            text_to_analyze = f"{title} {content} {summary}"
            
            # نقاط الكلمات المفتاحية المهمة
            for indicator, points in self.importance_indicators.items():
                if indicator in text_to_analyze:
                    score += points
                    logger.debug(f"🔍 كلمة مفتاحية مهمة '{indicator}': +{points} نقاط")
            
            # نقاط إضافية للعناوين الجذابة
            if any(word in title for word in ['breaking', 'exclusive', 'first', 'new']):
                score += 5.0
            
            # نقاط للمحتوى الطويل (يدل على التفصيل)
            if len(content) > 1000:
                score += 3.0
            elif len(content) > 500:
                score += 1.5
            
            # نقاط للمصادر الموثوقة
            source = article.get('source', '').lower()
            trusted_sources = ['ign', 'gamespot', 'polygon', 'kotaku', 'pcgamer']
            if any(trusted in source for trusted in trusted_sources):
                score += 2.0
            
            # نقاط للحداثة
            published_date = article.get('published_date')
            if published_date:
                try:
                    if isinstance(published_date, str):
                        from dateutil import parser
                        published_date = parser.parse(published_date)
                    
                    hours_old = (datetime.now() - published_date).total_seconds() / 3600
                    if hours_old < 2:  # أقل من ساعتين
                        score += 5.0
                    elif hours_old < 6:  # أقل من 6 ساعات
                        score += 3.0
                    elif hours_old < 24:  # أقل من يوم
                        score += 1.0
                except:
                    pass
            
            return min(100.0, score)  # الحد الأقصى 100
            
        except Exception as e:
            logger.debug(f"فشل في حساب نقاط الأهمية: {e}")
            return 0.0
    
    async def _enhance_article_with_follow_up(self, article: Dict, importance_score: float) -> Dict:
        """تعزيز المقال بمعلومات إضافية من البحث المتابع"""
        try:
            logger.info(f"🔍 البحث عن معلومات إضافية للمقال المهم...")
            
            # استخراج الكلمات المفتاحية للبحث
            search_keywords = self._extract_follow_up_keywords(article)
            
            additional_info = []
            related_articles = []
            
            # البحث عن معلومات إضافية
            for keyword in search_keywords[:3]:  # أفضل 3 كلمات مفتاحية
                try:
                    logger.info(f"🔍 البحث المتابع عن: '{keyword}'")
                    
                    # استخدام النظام المحسن للبحث - Tavily أولاً
                    follow_up_results = await enhanced_search.enhanced_search(
                        query=keyword,
                        max_results=5,
                        search_type="gaming_news",
                        priority="free"  # استخدام Tavily كأولوية أولى
                    )
                    
                    if follow_up_results:
                        # فلترة النتائج المرتبطة فقط
                        relevant_results = self._filter_relevant_results(
                            follow_up_results, article, keyword
                        )
                        
                        related_articles.extend(relevant_results)
                        
                        # استخراج معلومات إضافية
                        for result in relevant_results[:2]:  # أفضل نتيجتين
                            additional_info.append({
                                'source': result.get('source', ''),
                                'title': result.get('title', ''),
                                'summary': result.get('summary', '')[:200] + "...",
                                'url': result.get('url', ''),
                                'search_keyword': keyword
                            })
                    
                    await asyncio.sleep(2)  # تأخير بين البحثات
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث المتابع عن '{keyword}': {e}")
                    continue
            
            # تعزيز المقال بالمعلومات الإضافية
            enhanced_article = article.copy()
            enhanced_article.update({
                'importance_score': importance_score,
                'additional_info': additional_info,
                'related_articles': related_articles,
                'follow_up_searches': search_keywords,
                'enhanced_at': datetime.now().isoformat(),
                'is_enhanced': True
            })
            
            # دمج المعلومات الإضافية في المحتوى إذا وجدت
            if additional_info:
                enhanced_content = self._merge_additional_info(article, additional_info)
                enhanced_article['content'] = enhanced_content
                
                logger.info(f"✅ تم تعزيز المقال بـ {len(additional_info)} معلومة إضافية")
            else:
                logger.info("📭 لم يتم العثور على معلومات إضافية مفيدة")
            
            return enhanced_article
            
        except Exception as e:
            logger.error(f"❌ فشل في تعزيز المقال: {e}")
            return article
    
    def _extract_follow_up_keywords(self, article: Dict) -> List[str]:
        """استخراج الكلمات المفتاحية للبحث المتابع"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            
            # استخراج الأسماء والكلمات المهمة
            keywords = []
            
            # أسماء الألعاب (عادة تكون بأحرف كبيرة أو بين علامات اقتباس)
            game_names = re.findall(r'\b[A-Z][a-zA-Z\s]{2,20}\b', title)
            keywords.extend([name.strip() for name in game_names if len(name.strip()) > 3])
            
            # كلمات مفتاحية مهمة من العنوان
            important_words = re.findall(r'\b(?:update|release|announcement|reveal|trailer|gameplay|review|beta|alpha|DLC|expansion)\b', 
                                       title.lower())
            keywords.extend(important_words)
            
            # أسماء الشركات والاستوديوهات
            companies = re.findall(r'\b(?:Sony|Microsoft|Nintendo|Valve|Epic|Ubisoft|EA|Activision|Blizzard|Rockstar|CD Projekt)\b', 
                                 f"{title} {content}", re.IGNORECASE)
            keywords.extend(companies)
            
            # تنظيف وترتيب الكلمات المفتاحية
            cleaned_keywords = []
            for keyword in keywords:
                keyword = keyword.strip()
                if len(keyword) > 2 and keyword not in cleaned_keywords:
                    cleaned_keywords.append(keyword)
            
            # إضافة كلمات مفتاحية مركبة
            if cleaned_keywords:
                # دمج اسم اللعبة مع كلمات مهمة
                for game in cleaned_keywords[:2]:
                    if len(game) > 5:  # أسماء الألعاب عادة أطول
                        for action in ['news', 'update', 'release date', 'trailer']:
                            cleaned_keywords.append(f"{game} {action}")
            
            return cleaned_keywords[:8]  # أفضل 8 كلمات مفتاحية
            
        except Exception as e:
            logger.debug(f"فشل في استخراج الكلمات المفتاحية: {e}")
            return [article.get('title', '')[:50]]  # العودة للعنوان كاحتياطي
    
    def _filter_relevant_results(self, results: List[Dict], original_article: Dict, search_keyword: str) -> List[Dict]:
        """فلترة النتائج المرتبطة بالمقال الأصلي"""
        try:
            original_title = original_article.get('title', '').lower()
            original_content = original_article.get('content', '').lower()
            
            relevant_results = []
            
            for result in results:
                result_title = result.get('title', '').lower()
                result_content = result.get('content', '').lower()
                
                # تجنب النتائج المطابقة تماماً
                if result_title == original_title:
                    continue
                
                # فحص الصلة بالكلمة المفتاحية
                keyword_lower = search_keyword.lower()
                if keyword_lower in result_title or keyword_lower in result_content:
                    
                    # فحص الصلة بالمقال الأصلي
                    relevance_score = self._calculate_relevance_score(
                        original_article, result, search_keyword
                    )
                    
                    if relevance_score >= 0.3:  # حد أدنى للصلة
                        result['relevance_score'] = relevance_score
                        relevant_results.append(result)
            
            # ترتيب حسب الصلة
            relevant_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            return relevant_results[:3]  # أفضل 3 نتائج مرتبطة
            
        except Exception as e:
            logger.debug(f"فشل في فلترة النتائج المرتبطة: {e}")
            return []
    
    def _calculate_relevance_score(self, original: Dict, result: Dict, keyword: str) -> float:
        """حساب نقاط الصلة بين المقالين"""
        try:
            score = 0.0
            
            original_text = f"{original.get('title', '')} {original.get('content', '')}".lower()
            result_text = f"{result.get('title', '')} {result.get('content', '')}".lower()
            
            # نقاط الكلمة المفتاحية
            if keyword.lower() in result_text:
                score += 0.3
            
            # نقاط الكلمات المشتركة
            original_words = set(re.findall(r'\b\w{4,}\b', original_text))
            result_words = set(re.findall(r'\b\w{4,}\b', result_text))
            
            common_words = original_words.intersection(result_words)
            if len(original_words) > 0:
                score += (len(common_words) / len(original_words)) * 0.5
            
            # نقاط إضافية للمصادر الموثوقة
            if result.get('source', '').lower() in ['ign', 'gamespot', 'polygon']:
                score += 0.1
            
            return min(1.0, score)
            
        except Exception as e:
            logger.debug(f"فشل في حساب نقاط الصلة: {e}")
            return 0.0
    
    def _merge_additional_info(self, original_article: Dict, additional_info: List[Dict]) -> str:
        """دمج المعلومات الإضافية في المحتوى"""
        try:
            original_content = original_article.get('content', '')
            
            if not additional_info:
                return original_content
            
            # إضافة قسم للمعلومات الإضافية
            additional_section = "\n\n## معلومات إضافية:\n\n"
            
            for i, info in enumerate(additional_info[:3], 1):  # أقصى 3 معلومات إضافية
                additional_section += f"### {i}. {info.get('title', 'معلومة إضافية')}\n"
                additional_section += f"**المصدر:** {info.get('source', 'غير محدد')}\n"
                additional_section += f"{info.get('summary', '')}\n\n"
            
            return original_content + additional_section
            
        except Exception as e:
            logger.debug(f"فشل في دمج المعلومات الإضافية: {e}")
            return original_article.get('content', '')
    
    async def _track_story(self, article: Dict, importance_score: float):
        """تتبع القصة للمتابعة المستقبلية"""
        try:
            story_id = hashlib.md5(article.get('title', '').encode()).hexdigest()
            
            story = NewsStory(
                title=article.get('title', ''),
                summary=article.get('summary', ''),
                keywords=article.get('follow_up_searches', []),
                importance_score=importance_score,
                first_seen=datetime.now(),
                last_updated=datetime.now(),
                related_articles=article.get('related_articles', []),
                follow_up_searches=article.get('follow_up_searches', []),
                is_breaking=importance_score >= 85.0,
                needs_follow_up=len(article.get('related_articles', [])) < self.settings['min_related_articles']
            )
            
            self.tracked_stories[story_id] = story
            
            # إضافة للمتابعة إذا لزم الأمر
            if story.needs_follow_up:
                self.follow_up_queue.append({
                    'story_id': story_id,
                    'next_search_time': time.time() + self.settings['follow_up_delay'],
                    'attempts': 0
                })
                
                logger.info(f"📋 تم إضافة القصة للمتابعة: {story.title[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ فشل في تتبع القصة: {e}")
    
    async def _process_follow_up_queue(self):
        """معالجة قائمة انتظار المتابعة"""
        try:
            current_time = time.time()
            processed_items = []
            
            for item in self.follow_up_queue:
                if current_time >= item['next_search_time'] and item['attempts'] < self.settings['max_follow_ups']:
                    story_id = item['story_id']
                    story = self.tracked_stories.get(story_id)
                    
                    if story and story.needs_follow_up:
                        logger.info(f"🔄 متابعة القصة: {story.title[:50]}...")
                        
                        # البحث عن تحديثات
                        updates_found = await self._search_for_story_updates(story)
                        
                        if updates_found:
                            story.needs_follow_up = False
                            story.last_updated = datetime.now()
                            logger.info(f"✅ تم العثور على تحديثات للقصة")
                        else:
                            item['attempts'] += 1
                            item['next_search_time'] = current_time + self.settings['follow_up_delay']
                            logger.info(f"📭 لم يتم العثور على تحديثات، المحاولة {item['attempts']}")
                    
                    processed_items.append(item)
            
            # إزالة العناصر المكتملة أو المنتهية الصلاحية
            self.follow_up_queue = [
                item for item in self.follow_up_queue 
                if item not in processed_items or (
                    item['attempts'] < self.settings['max_follow_ups'] and 
                    self.tracked_stories.get(item['story_id'], NewsStory('', '', [], 0, datetime.now(), datetime.now(), [], [], False, False)).needs_follow_up
                )
            ]
            
        except Exception as e:
            logger.error(f"❌ فشل في معالجة قائمة المتابعة: {e}")
    
    async def _search_for_story_updates(self, story: NewsStory) -> bool:
        """البحث عن تحديثات للقصة"""
        try:
            # البحث باستخدام كلمات مفتاحية محدثة
            for keyword in story.keywords[:2]:  # أفضل كلمتين مفتاحيتين
                try:
                    update_query = f"{keyword} update latest news"
                    
                    results = await enhanced_search.enhanced_search(
                        query=update_query,
                        max_results=3,
                        search_type="gaming_news",
                        priority="free"  # استخدام Tavily (مجاني) كأولوية أولى للمتابعة
                    )
                    
                    if results:
                        # فحص ما إذا كانت النتائج جديدة ومرتبطة
                        new_articles = []
                        for result in results:
                            if self._is_new_update(result, story):
                                new_articles.append(result)
                        
                        if new_articles:
                            story.related_articles.extend(new_articles)
                            return True
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.debug(f"فشل في البحث عن تحديثات: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث عن تحديثات القصة: {e}")
            return False
    
    def _is_new_update(self, article: Dict, story: NewsStory) -> bool:
        """فحص ما إذا كان المقال تحديث جديد للقصة"""
        try:
            article_title = article.get('title', '').lower()
            
            # فحص ما إذا كان المقال موجود مسبقاً
            for existing in story.related_articles:
                if existing.get('title', '').lower() == article_title:
                    return False
            
            # فحص الصلة بالقصة الأصلية
            story_keywords = [kw.lower() for kw in story.keywords]
            article_text = f"{article_title} {article.get('content', '')}".lower()
            
            relevance_count = sum(1 for kw in story_keywords if kw in article_text)
            
            return relevance_count >= 1  # على الأقل كلمة مفتاحية واحدة مشتركة
            
        except Exception as e:
            logger.debug(f"فشل في فحص التحديث الجديد: {e}")
            return False
    
    def get_tracking_stats(self) -> Dict:
        """الحصول على إحصائيات التتبع"""
        try:
            active_stories = len(self.tracked_stories)
            pending_follow_ups = len(self.follow_up_queue)
            
            breaking_stories = sum(1 for story in self.tracked_stories.values() if story.is_breaking)
            completed_stories = sum(1 for story in self.tracked_stories.values() if not story.needs_follow_up)
            
            return {
                'active_stories': active_stories,
                'pending_follow_ups': pending_follow_ups,
                'breaking_stories': breaking_stories,
                'completed_stories': completed_stories,
                'completion_rate': (completed_stories / active_stories * 100) if active_stories > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع إحصائيات التتبع: {e}")
            return {}

# إنشاء مثيل عام
intelligent_news_tracker = IntelligentNewsTracker()
