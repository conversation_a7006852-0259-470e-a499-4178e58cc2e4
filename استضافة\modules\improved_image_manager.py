#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة الصور المحسن
يحل مشاكل إنشاء الصور ويوفر بدائل متعددة
"""

import asyncio
import aiohttp
import requests
from typing import Dict, List, Optional, Tuple
import random
import time
from datetime import datetime
import os
import base64
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont

from modules.logger import logger
from modules.error_fix_system import error_fix_system

class ImprovedImageManager:
    """مدير الصور المحسن مع بدائل متعددة"""
    
    def __init__(self):
        self.image_apis = {
            'freepik': {
                'url': 'https://api.freepik.com/v1/ai/text-to-image',
                'enabled': True,
                'priority': 1
            },
            'pollinations': {
                'url': 'https://image.pollinations.ai/prompt/',
                'enabled': True,
                'priority': 2
            },
            'placeholder': {
                'url': 'https://via.placeholder.com/',
                'enabled': True,
                'priority': 3
            },
            'local_generator': {
                'enabled': True,
                'priority': 4
            }
        }
        
        self.fallback_images = [
            "https://via.placeholder.com/800x600/1a1a1a/ffffff?text=Gaming+News",
            "https://via.placeholder.com/800x600/2c3e50/ecf0f1?text=Game+Review",
            "https://via.placeholder.com/800x600/34495e/bdc3c7?text=Tech+News",
            "https://via.placeholder.com/800x600/8e44ad/f8c471?text=Game+Guide",
            "https://via.placeholder.com/800x600/e74c3c/f7dc6f?text=Gaming+Update"
        ]
        
        self.gaming_keywords = [
            'gaming', 'video games', 'esports', 'console', 'pc gaming',
            'mobile games', 'game review', 'gaming news', 'game trailer',
            'gaming technology', 'virtual reality', 'augmented reality'
        ]
    
    async def generate_image(self, prompt: str, article_data: Dict = None) -> Optional[str]:
        """إنشاء صورة مع نظام بدائل متقدم"""
        try:
            logger.info(f"🖼️ بدء إنشاء صورة: {prompt[:50]}...")
            
            # تحسين الـ prompt
            enhanced_prompt = self._enhance_prompt(prompt, article_data)
            
            # محاولة APIs مختلفة حسب الأولوية
            for api_name, api_config in sorted(self.image_apis.items(), 
                                             key=lambda x: x[1].get('priority', 999)):
                
                if not api_config.get('enabled', False):
                    continue
                
                try:
                    image_url = await self._try_api(api_name, enhanced_prompt, api_config)
                    
                    if image_url:
                        logger.info(f"✅ تم إنشاء صورة باستخدام {api_name}")
                        return image_url
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل {api_name}: {e}")
                    await error_fix_system.fix_error(e, {'service': api_name})
                    continue
            
            # استخدام صورة احتياطية
            fallback_url = self._get_fallback_image(article_data)
            logger.info(f"🔄 استخدام صورة احتياطية: {fallback_url}")
            return fallback_url
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة: {e}")
            await error_fix_system.fix_error(e, {'context': 'image_generation'})
            return self._get_fallback_image(article_data)
    
    def _enhance_prompt(self, prompt: str, article_data: Dict = None) -> str:
        """تحسين prompt الصورة"""
        try:
            enhanced = prompt.lower()
            
            # إضافة كلمات مفتاحية للألعاب
            if not any(keyword in enhanced for keyword in self.gaming_keywords):
                enhanced = f"gaming {enhanced}"
            
            # إضافة تفاصيل من المقال
            if article_data:
                category = article_data.get('category', '').lower()
                if category:
                    enhanced = f"{category} {enhanced}"
                
                keywords = article_data.get('keywords', [])
                if keywords:
                    enhanced = f"{enhanced} {' '.join(keywords[:2])}"
            
            # إضافة مواصفات جودة
            enhanced += " high quality, detailed, professional, 4k"
            
            return enhanced
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين prompt: {e}")
            return prompt
    
    async def _try_api(self, api_name: str, prompt: str, api_config: Dict) -> Optional[str]:
        """محاولة API محدد"""
        try:
            if api_name == 'pollinations':
                return await self._try_pollinations(prompt, api_config)
            elif api_name == 'freepik':
                return await self._try_freepik(prompt, api_config)
            elif api_name == 'placeholder':
                return self._try_placeholder(prompt, api_config)
            elif api_name == 'local_generator':
                return await self._try_local_generator(prompt)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في {api_name}: {e}")
            return None
    
    async def _try_pollinations(self, prompt: str, api_config: Dict) -> Optional[str]:
        """محاولة Pollinations AI"""
        try:
            # تنظيف الـ prompt للـ URL
            clean_prompt = prompt.replace(' ', '%20').replace(',', '%2C')
            
            # إنشاء URL
            image_url = f"{api_config['url']}{clean_prompt}?width=800&height=600&seed={random.randint(1, 10000)}"
            
            # فحص الصورة
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'image' in content_type:
                            return image_url
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في Pollinations: {e}")
            return None
    
    async def _try_freepik(self, prompt: str, api_config: Dict) -> Optional[str]:
        """محاولة Freepik API (يتطلب مفتاح API)"""
        try:
            # هذا يتطلب مفتاح API من Freepik
            # للآن نرجع None لاستخدام البدائل
            logger.info("🔄 Freepik API يتطلب مفتاح - تخطي")
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في Freepik: {e}")
            return None
    
    def _try_placeholder(self, prompt: str, api_config: Dict) -> Optional[str]:
        """إنشاء صورة placeholder مخصصة"""
        try:
            # استخراج كلمات مفتاحية من الـ prompt
            words = prompt.split()[:3]  # أول 3 كلمات
            text = '+'.join(words)
            
            # ألوان مختلفة للفئات المختلفة
            colors = [
                ('1a1a1a', 'ffffff'),  # أسود/أبيض
                ('2c3e50', 'ecf0f1'),  # أزرق داكن/رمادي فاتح
                ('34495e', 'bdc3c7'),  # رمادي/فضي
                ('8e44ad', 'f8c471'),  # بنفسجي/أصفر
                ('e74c3c', 'f7dc6f'),  # أحمر/أصفر فاتح
            ]
            
            bg_color, text_color = random.choice(colors)
            
            placeholder_url = f"{api_config['url']}800x600/{bg_color}/{text_color}?text={text}"
            
            return placeholder_url
            
        except Exception as e:
            logger.error(f"❌ خطأ في Placeholder: {e}")
            return None
    
    async def _try_local_generator(self, prompt: str) -> Optional[str]:
        """إنشاء صورة محلياً باستخدام PIL"""
        try:
            # إنشاء صورة بسيطة باستخدام PIL
            width, height = 800, 600
            
            # ألوان عشوائية
            colors = [
                (26, 26, 26),      # أسود داكن
                (44, 62, 80),      # أزرق داكن
                (52, 73, 94),      # رمادي داكن
                (142, 68, 173),    # بنفسجي
                (231, 76, 60),     # أحمر
            ]
            
            bg_color = random.choice(colors)
            text_color = (255, 255, 255)  # أبيض
            
            # إنشاء الصورة
            image = Image.new('RGB', (width, height), bg_color)
            draw = ImageDraw.Draw(image)
            
            # إضافة نص
            text = prompt[:30] + "..." if len(prompt) > 30 else prompt
            
            try:
                # محاولة استخدام خط مخصص
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                # استخدام الخط الافتراضي
                font = ImageFont.load_default()
            
            # حساب موضع النص
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            # رسم النص
            draw.text((x, y), text, fill=text_color, font=font)
            
            # حفظ الصورة
            timestamp = int(time.time())
            filename = f"generated_image_{timestamp}.png"
            filepath = os.path.join("data", "images", filename)
            
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            image.save(filepath)
            
            # إرجاع مسار الصورة
            return filepath
            
        except Exception as e:
            logger.error(f"❌ خطأ في المولد المحلي: {e}")
            return None
    
    def _get_fallback_image(self, article_data: Dict = None) -> str:
        """الحصول على صورة احتياطية"""
        try:
            # اختيار صورة احتياطية حسب الفئة
            if article_data:
                category = article_data.get('category', '').lower()
                
                if 'review' in category or 'مراجعة' in category:
                    return self.fallback_images[1]  # Game Review
                elif 'guide' in category or 'دليل' in category:
                    return self.fallback_images[3]  # Game Guide
                elif 'update' in category or 'تحديث' in category:
                    return self.fallback_images[4]  # Gaming Update
                elif 'tech' in category or 'تكنولوجيا' in category:
                    return self.fallback_images[2]  # Tech News
            
            # صورة افتراضية عامة
            return self.fallback_images[0]  # Gaming News
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على صورة احتياطية: {e}")
            return self.fallback_images[0]
    
    async def validate_image_url(self, image_url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.head(image_url) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        return 'image' in content_type
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الصورة: {e}")
            return False
    
    def get_image_stats(self) -> Dict:
        """الحصول على إحصائيات الصور"""
        return {
            'available_apis': len([api for api in self.image_apis.values() if api.get('enabled')]),
            'fallback_images': len(self.fallback_images),
            'api_status': {name: config.get('enabled', False) for name, config in self.image_apis.items()}
        }

# إنشاء مثيل عام
improved_image_manager = ImprovedImageManager()
