# إعدادات تدفق العمل المحسن للوكيل
"""
إعدادات التحكم في تدفق العمل الجديد
النشر أولاً، التحليل لاحقاً
"""

class WorkflowConfig:
    """إعدادات تدفق العمل المحسن"""
    
    # ========== إعدادات جمع المحتوى السريع ==========
    
    # الحد الأقصى لوقت جمع المحتوى السريع (ثواني)
    FAST_CONTENT_COLLECTION_TIMEOUT = 60  # دقيقة واحدة
    
    # عدد المصادر السريعة للبحث
    QUICK_SOURCES_COUNT = 3
    
    # عدد المقالات من كل مصدر سريع
    ARTICLES_PER_QUICK_SOURCE = 2
    
    # تأخير بين المصادر السريعة (ثواني)
    QUICK_SOURCE_DELAY = 1
    
    # ========== إعدادات النشر السريع ==========
    
    # الحد الأقصى لوقت النشر (ثواني)
    QUICK_PUBLISHING_TIMEOUT = 30  # 30 ثانية
    
    # عدد المقالات للنشر في كل دورة
    ARTICLES_PER_CYCLE = 1
    
    # تأخير بين نشر المقالات (ثواني)
    PUBLISHING_DELAY = 5
    
    # ========== إعدادات المهام الخلفية ==========
    
    # فاصل زمني أساسي للمهام الخلفية (ثواني)
    BACKGROUND_TASK_INTERVAL = 600  # 10 دقائق
    
    # جدولة المهام الخلفية (ثواني)
    BACKGROUND_TASKS_SCHEDULE = {
        'article_analysis': 600,      # تحليل المقالات كل 10 دقائق
        'seo_analysis': 900,          # تحليل SEO كل 15 دقيقة
        'performance_monitoring': 300, # مراقبة الأداء كل 5 دقائق
        'maintenance': 1800,          # الصيانة كل 30 دقيقة
        'database_optimization': 3600, # تحسين قاعدة البيانات كل ساعة
        'backup_creation': 7200       # النسخ الاحتياطية كل ساعتين
    }
    
    # تمكين/تعطيل المهام الخلفية
    BACKGROUND_TASKS_ENABLED = {
        'article_analysis': True,
        'seo_analysis': True,
        'performance_monitoring': True,
        'maintenance': True,
        'database_optimization': True,
        'backup_creation': True
    }
    
    # ========== إعدادات توقيت الدورات ==========
    
    # الحد الأدنى لوقت الانتظار بين الدورات (ثواني)
    MIN_CYCLE_WAIT_TIME = 1800  # 30 دقيقة
    
    # الحد الأقصى لوقت الانتظار بين الدورات (ثواني)
    MAX_CYCLE_WAIT_TIME = 7200  # ساعتين
    
    # وقت الانتظار الافتراضي (ثواني)
    DEFAULT_CYCLE_WAIT_TIME = 3600  # ساعة واحدة
    
    # ========== إعدادات الأداء ==========
    
    # الحد الأقصى لوقت تنفيذ دورة واحدة (ثواني)
    MAX_CYCLE_EXECUTION_TIME = 300  # 5 دقائق
    
    # الحد الأقصى لاستخدام الذاكرة (MB)
    MAX_MEMORY_USAGE = 512
    
    # الحد الأقصى لاستخدام المعالج (%)
    MAX_CPU_USAGE = 80
    
    # ========== إعدادات المحتوى البديل ==========
    
    # تمكين البحث التاريخي
    ENABLE_HISTORICAL_SEARCH = True
    
    # عدد الأيام للبحث التاريخي
    HISTORICAL_SEARCH_DAYS = 30
    
    # تمكين إنشاء المحتوى التلقائي
    ENABLE_AUTO_CONTENT_GENERATION = True
    
    # تمكين البحث العميق
    ENABLE_DEEP_SEARCH = True
    
    # ========== إعدادات جودة المحتوى ==========
    
    # الحد الأدنى لجودة المحتوى للنشر السريع
    MIN_CONTENT_QUALITY_FAST = 60
    
    # الحد الأدنى لجودة المحتوى للنشر العادي
    MIN_CONTENT_QUALITY_NORMAL = 70
    
    # الحد الأدنى لطول العنوان
    MIN_TITLE_LENGTH = 10
    
    # الحد الأدنى لطول المحتوى
    MIN_CONTENT_LENGTH = 100
    
    # ========== إعدادات المراقبة ==========
    
    # تمكين مراقبة الأداء في الوقت الفعلي
    ENABLE_REALTIME_MONITORING = True
    
    # فاصل زمني لمراقبة الأداء (ثواني)
    MONITORING_INTERVAL = 60
    
    # تمكين التنبيهات
    ENABLE_ALERTS = True
    
    # حدود التنبيهات
    ALERT_THRESHOLDS = {
        'memory_usage': 80,      # %
        'cpu_usage': 90,         # %
        'error_rate': 10,        # %
        'response_time': 30      # ثواني
    }
    
    # ========== إعدادات التسجيل ==========
    
    # مستوى التسجيل للمهام الخلفية
    BACKGROUND_LOG_LEVEL = 'INFO'
    
    # تمكين تسجيل تفصيلي للأداء
    ENABLE_PERFORMANCE_LOGGING = True
    
    # تمكين تسجيل المهام الخلفية
    ENABLE_BACKGROUND_TASK_LOGGING = True
    
    # ========== إعدادات التحسين التلقائي ==========
    
    # تمكين التحسين التلقائي للتوقيت
    ENABLE_AUTO_TIMING_OPTIMIZATION = True
    
    # تمكين التحسين التلقائي للموارد
    ENABLE_AUTO_RESOURCE_OPTIMIZATION = True
    
    # تمكين التعلم من الأداء
    ENABLE_PERFORMANCE_LEARNING = True
    
    # ========== دوال المساعدة ==========
    
    @classmethod
    def get_background_task_interval(cls, task_name: str) -> int:
        """الحصول على فاصل زمني لمهمة خلفية محددة"""
        return cls.BACKGROUND_TASKS_SCHEDULE.get(task_name, cls.BACKGROUND_TASK_INTERVAL)
    
    @classmethod
    def is_background_task_enabled(cls, task_name: str) -> bool:
        """فحص ما إذا كانت مهمة خلفية مفعلة"""
        return cls.BACKGROUND_TASKS_ENABLED.get(task_name, False)
    
    @classmethod
    def get_optimal_wait_time(cls, published_count: int, content_quality: float) -> int:
        """حساب وقت الانتظار الأمثل"""
        base_wait = cls.DEFAULT_CYCLE_WAIT_TIME
        
        # تقليل الوقت إذا تم النشر بنجاح
        if published_count > 0:
            base_wait = max(cls.MIN_CYCLE_WAIT_TIME, base_wait * 0.8)
        
        # تعديل حسب جودة المحتوى
        if content_quality > 80:
            base_wait = max(cls.MIN_CYCLE_WAIT_TIME, base_wait * 0.9)
        elif content_quality < 60:
            base_wait = min(cls.MAX_CYCLE_WAIT_TIME, base_wait * 1.2)
        
        return int(base_wait)
    
    @classmethod
    def should_run_background_task(cls, task_name: str, last_run_time: float, current_time: float) -> bool:
        """فحص ما إذا كان يجب تشغيل مهمة خلفية"""
        if not cls.is_background_task_enabled(task_name):
            return False
        
        interval = cls.get_background_task_interval(task_name)
        return (current_time - last_run_time) >= interval
    
    @classmethod
    def validate_config(cls) -> bool:
        """التحقق من صحة الإعدادات"""
        # فحص الحدود الزمنية
        if cls.MIN_CYCLE_WAIT_TIME >= cls.MAX_CYCLE_WAIT_TIME:
            return False
        
        # فحص إعدادات الأداء
        if cls.MAX_MEMORY_USAGE <= 0 or cls.MAX_CPU_USAGE <= 0:
            return False
        
        # فحص إعدادات الجودة
        if cls.MIN_CONTENT_QUALITY_FAST > cls.MIN_CONTENT_QUALITY_NORMAL:
            return False
        
        return True

# إعدادات متقدمة للتحسين
class AdvancedWorkflowConfig:
    """إعدادات متقدمة لتحسين تدفق العمل"""
    
    # تمكين التحسين التكيفي
    ENABLE_ADAPTIVE_OPTIMIZATION = True
    
    # عوامل التحسين التكيفي
    ADAPTIVE_FACTORS = {
        'time_of_day': 0.3,      # تأثير وقت اليوم
        'content_availability': 0.4,  # تأثير توفر المحتوى
        'system_load': 0.2,      # تأثير حمولة النظام
        'user_engagement': 0.1   # تأثير تفاعل المستخدمين
    }
    
    # حدود التحسين التلقائي
    AUTO_OPTIMIZATION_LIMITS = {
        'min_wait_reduction': 0.5,  # أقل تقليل للانتظار (50%)
        'max_wait_increase': 2.0,   # أقصى زيادة للانتظار (200%)
        'min_task_interval': 300,   # أقل فاصل للمهام (5 دقائق)
        'max_task_interval': 3600   # أقصى فاصل للمهام (ساعة)
    }
