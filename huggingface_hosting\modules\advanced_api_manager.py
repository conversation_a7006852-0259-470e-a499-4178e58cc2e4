#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة مفاتيح API متقدم
يدير التناوب التلقائي والقائمة السوداء والاستخدام الآمن
"""

import time
import json
import random
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import sqlite3
import os

from modules.logger import logger
from config.api_config import APIConfig

@dataclass
class APIKeyStatus:
    """حالة مفتاح API"""
    key: str
    service: str
    is_active: bool = True
    is_blacklisted: bool = False
    usage_count: int = 0
    daily_usage: int = 0
    last_used: Optional[datetime] = None
    last_error: Optional[str] = None
    error_count: int = 0
    success_rate: float = 100.0
    daily_limit: int = 1000
    rate_limit_reset: Optional[datetime] = None

class AdvancedAPIManager:
    """مدير مفاتيح API متقدم"""
    
    def __init__(self):
        self.db_path = "data/api_keys.db"
        self.current_keys = {}  # المفاتيح الحالية لكل خدمة
        self.blacklisted_keys = set()
        self.usage_stats = {}
        self.rate_limits = {}
        
        # إعداد قاعدة البيانات
        self._init_database()
        
        # تحميل مفاتيح Gemini
        self._load_gemini_keys()
        
        # تحميل الإحصائيات
        self._load_usage_stats()
    
    def _init_database(self):
        """إنشاء قاعدة بيانات مفاتيح API"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول مفاتيح API
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_keys (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key_hash TEXT UNIQUE NOT NULL,
                        service TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        is_blacklisted BOOLEAN DEFAULT 0,
                        usage_count INTEGER DEFAULT 0,
                        daily_usage INTEGER DEFAULT 0,
                        last_used TIMESTAMP,
                        last_error TEXT,
                        error_count INTEGER DEFAULT 0,
                        success_rate REAL DEFAULT 100.0,
                        daily_limit INTEGER DEFAULT 1000,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول إحصائيات الاستخدام
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS usage_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service TEXT NOT NULL,
                        date DATE NOT NULL,
                        total_requests INTEGER DEFAULT 0,
                        successful_requests INTEGER DEFAULT 0,
                        failed_requests INTEGER DEFAULT 0,
                        unique_keys_used INTEGER DEFAULT 0,
                        UNIQUE(service, date)
                    )
                ''')
                
                # جدول الأخطاء
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service TEXT NOT NULL,
                        key_hash TEXT NOT NULL,
                        error_type TEXT NOT NULL,
                        error_message TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء قاعدة بيانات مفاتيح API")
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء قاعدة بيانات API: {e}")
    
    def _load_gemini_keys(self):
        """تحميل مفاتيح Gemini من الإعدادات"""
        try:
            gemini_keys = APIConfig.GEMINI_API_KEYS_POOL
            
            if not gemini_keys:
                logger.warning("⚠️ لا توجد مفاتيح Gemini متوفرة")
                return
            
            # تسجيل المفاتيح في قاعدة البيانات
            for key in gemini_keys:
                if key.strip():
                    self._register_api_key(key, 'gemini')
            
            logger.info(f"✅ تم تحميل {len(gemini_keys)} مفتاح Gemini")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل مفاتيح Gemini: {e}")
    
    def _register_api_key(self, key: str, service: str, daily_limit: int = 1000):
        """تسجيل مفتاح API جديد"""
        try:
            key_hash = self._hash_key(key)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO api_keys 
                    (key_hash, service, daily_limit) 
                    VALUES (?, ?, ?)
                ''', (key_hash, service, daily_limit))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل مفتاح API: {e}")
    
    def _hash_key(self, key: str) -> str:
        """إنشاء hash للمفتاح للأمان"""
        import hashlib
        return hashlib.sha256(key.encode()).hexdigest()[:16]
    
    def get_active_key(self, service: str) -> Optional[str]:
        """الحصول على مفتاح نشط للخدمة"""
        try:
            # البحث عن مفتاح نشط غير محظور
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT key_hash, daily_usage, daily_limit, error_count 
                    FROM api_keys 
                    WHERE service = ? AND is_active = 1 AND is_blacklisted = 0
                    ORDER BY daily_usage ASC, error_count ASC, success_rate DESC
                    LIMIT 1
                ''', (service,))
                
                result = cursor.fetchone()
                
                if result:
                    key_hash, daily_usage, daily_limit, error_count = result
                    
                    # فحص الحد اليومي
                    if daily_usage >= daily_limit:
                        logger.warning(f"⚠️ مفتاح {service} وصل للحد اليومي")
                        return self._get_next_available_key(service)
                    
                    # العثور على المفتاح الفعلي
                    actual_key = self._find_key_by_hash(key_hash, service)
                    
                    if actual_key:
                        # تحديث آخر استخدام
                        self._update_key_usage(key_hash)
                        return actual_key
                
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على مفتاح {service}: {e}")
            return None
    
    def _find_key_by_hash(self, key_hash: str, service: str) -> Optional[str]:
        """العثور على المفتاح الفعلي من hash"""
        try:
            if service == 'gemini':
                for key in APIConfig.GEMINI_API_KEYS_POOL:
                    if self._hash_key(key) == key_hash:
                        return key
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في العثور على المفتاح: {e}")
            return None
    
    def _get_next_available_key(self, service: str) -> Optional[str]:
        """الحصول على المفتاح التالي المتاح"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT key_hash 
                    FROM api_keys 
                    WHERE service = ? AND is_active = 1 AND is_blacklisted = 0 
                    AND daily_usage < daily_limit
                    ORDER BY daily_usage ASC, error_count ASC
                    LIMIT 1
                ''', (service,))
                
                result = cursor.fetchone()
                
                if result:
                    key_hash = result[0]
                    actual_key = self._find_key_by_hash(key_hash, service)
                    
                    if actual_key:
                        self._update_key_usage(key_hash)
                        return actual_key
                
                logger.warning(f"⚠️ لا توجد مفاتيح متاحة لـ {service}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المفتاح التالي: {e}")
            return None
    
    def _update_key_usage(self, key_hash: str):
        """تحديث إحصائيات استخدام المفتاح"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE api_keys 
                    SET usage_count = usage_count + 1,
                        daily_usage = daily_usage + 1,
                        last_used = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE key_hash = ?
                ''', (key_hash,))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث استخدام المفتاح: {e}")
    
    def report_key_error(self, key: str, service: str, error_type: str, error_message: str = ""):
        """تسجيل خطأ في مفتاح API"""
        try:
            key_hash = self._hash_key(key)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # تسجيل الخطأ
                cursor.execute('''
                    INSERT INTO api_errors (service, key_hash, error_type, error_message)
                    VALUES (?, ?, ?, ?)
                ''', (service, key_hash, error_type, error_message))
                
                # تحديث إحصائيات المفتاح
                cursor.execute('''
                    UPDATE api_keys 
                    SET error_count = error_count + 1,
                        last_error = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE key_hash = ?
                ''', (error_message, key_hash))
                
                # إضافة للقائمة السوداء إذا كان الخطأ خطير
                if error_type in ['PermissionDenied', 'InvalidKey', 'Forbidden']:
                    cursor.execute('''
                        UPDATE api_keys 
                        SET is_blacklisted = 1
                        WHERE key_hash = ?
                    ''', (key_hash,))
                    
                    self.blacklisted_keys.add(key)
                    logger.warning(f"🚫 تم وضع المفتاح في القائمة السوداء لـ {service}: {error_type}")
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل خطأ المفتاح: {e}")
    
    def report_key_success(self, key: str, service: str):
        """تسجيل نجاح استخدام مفتاح API"""
        try:
            key_hash = self._hash_key(key)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # حساب معدل النجاح الجديد
                cursor.execute('''
                    SELECT usage_count, error_count 
                    FROM api_keys 
                    WHERE key_hash = ?
                ''', (key_hash,))
                
                result = cursor.fetchone()
                if result:
                    usage_count, error_count = result
                    success_rate = ((usage_count - error_count) / max(usage_count, 1)) * 100
                    
                    cursor.execute('''
                        UPDATE api_keys 
                        SET success_rate = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE key_hash = ?
                    ''', (success_rate, key_hash))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل نجاح المفتاح: {e}")
    
    def reset_daily_usage(self):
        """إعادة تعيين الاستخدام اليومي"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE api_keys 
                    SET daily_usage = 0,
                        updated_at = CURRENT_TIMESTAMP
                ''')
                
                conn.commit()
                logger.info("✅ تم إعادة تعيين الاستخدام اليومي لجميع المفاتيح")
                
        except Exception as e:
            logger.error(f"❌ خطأ في إعادة تعيين الاستخدام اليومي: {e}")
    
    def get_usage_statistics(self, service: str = None) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if service:
                    cursor.execute('''
                        SELECT COUNT(*) as total_keys,
                               SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_keys,
                               SUM(CASE WHEN is_blacklisted = 1 THEN 1 ELSE 0 END) as blacklisted_keys,
                               SUM(daily_usage) as total_daily_usage,
                               AVG(success_rate) as avg_success_rate
                        FROM api_keys 
                        WHERE service = ?
                    ''', (service,))
                else:
                    cursor.execute('''
                        SELECT COUNT(*) as total_keys,
                               SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_keys,
                               SUM(CASE WHEN is_blacklisted = 1 THEN 1 ELSE 0 END) as blacklisted_keys,
                               SUM(daily_usage) as total_daily_usage,
                               AVG(success_rate) as avg_success_rate
                        FROM api_keys
                    ''')
                
                result = cursor.fetchone()
                
                if result:
                    return {
                        'total_keys': result[0] or 0,
                        'active_keys': result[1] or 0,
                        'blacklisted_keys': result[2] or 0,
                        'total_daily_usage': result[3] or 0,
                        'avg_success_rate': result[4] or 0.0
                    }
                
                return {}
                
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الاستخدام: {e}")
            return {}
    
    def _load_usage_stats(self):
        """تحميل إحصائيات الاستخدام"""
        try:
            stats = self.get_usage_statistics('gemini')
            logger.info(f"📊 إحصائيات Gemini: {stats}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل إحصائيات الاستخدام: {e}")

# إنشاء مثيل عام
advanced_api_manager = AdvancedAPIManager()
