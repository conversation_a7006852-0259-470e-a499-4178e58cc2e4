#!/usr/bin/env python3
"""
محسن معدل النشر الناجح
"""

import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class PublishingSuccessOptimizer:
    """محسن معدل النشر الناجح"""
    
    def __init__(self):
        self.publishing_stats = {
            'total_attempts': 0,
            'successful_publishes': 0,
            'failed_publishes': 0,
            'retry_successes': 0,
            'quality_rejections': 0
        }
        
        self.optimization_strategies = {
            'quality_enhancement': True,
            'retry_mechanism': True,
            'content_validation': True,
            'seo_optimization': True,
            'error_recovery': True
        }
    
    def optimize_article_for_publishing(self, article: Dict) -> Dict:
        """تحسين المقال للنشر الناجح"""
        try:
            optimized_article = article.copy()
            
            # 1. تحسين الجودة
            if self.optimization_strategies['quality_enhancement']:
                optimized_article = self._enhance_article_quality(optimized_article)
            
            # 2. تحسين SEO
            if self.optimization_strategies['seo_optimization']:
                optimized_article = self._optimize_seo_for_publishing(optimized_article)
            
            # 3. التحقق من صحة المحتوى
            if self.optimization_strategies['content_validation']:
                validation_result = self._validate_content_for_publishing(optimized_article)
                if not validation_result['valid']:
                    optimized_article = self._fix_validation_issues(optimized_article, validation_result)
            
            # 4. إضافة معلومات النشر
            optimized_article = self._add_publishing_metadata(optimized_article)
            
            logger.info("✅ تم تحسين المقال للنشر الناجح")
            return optimized_article
            
        except Exception as e:
            logger.error(f"❌ فشل في تحسين المقال للنشر: {e}")
            return article
    
    def _enhance_article_quality(self, article: Dict) -> Dict:
        """تحسين جودة المقال"""
        # تحسين العنوان
        title = article.get('title', '')
        if len(title) < 30:
            # إضافة كلمات جذابة للعنوان القصير
            attractive_words = ['🎮 ', '🔥 ', '⚡ ']
            if not any(word in title for word in attractive_words):
                article['title'] = f"🎮 {title}"
        
        # تحسين المحتوى
        content = article.get('content', '')
        if len(content.split()) < 200:
            # إضافة محتوى إضافي للمقالات القصيرة
            additional_content = """

## معلومات إضافية

هذا المحتوى يقدم معلومات قيمة للاعبين ومتابعي أخبار الألعاب. نحرص على تقديم أحدث الأخبار والمراجعات.

## خلاصة

نأمل أن تكونوا قد استفدتم من هذا المحتوى. تابعونا للمزيد من أخبار الألعاب المثيرة.
"""
            article['content'] = content + additional_content
        
        # إضافة كلمات مفتاحية إذا لم توجد
        if not article.get('keywords'):
            article['keywords'] = ['ألعاب', 'أخبار الألعاب', 'مراجعات', 'تحديثات']
        
        return article
    
    def _optimize_seo_for_publishing(self, article: Dict) -> Dict:
        """تحسين SEO للنشر"""
        # إضافة وصف قصير إذا لم يوجد
        if not article.get('summary'):
            content = article.get('content', '')
            if content:
                # أخذ أول 150 حرف كوصف
                summary = content[:150].strip()
                if summary:
                    article['summary'] = summary + "..."
        
        # إضافة تصنيف
        if not article.get('category'):
            article['category'] = 'أخبار الألعاب'
        
        # إضافة وسوم
        if not article.get('tags'):
            keywords = article.get('keywords', [])
            article['tags'] = keywords[:5]  # أول 5 كلمات مفتاحية كوسوم
        
        return article
    
    def _validate_content_for_publishing(self, article: Dict) -> Dict:
        """التحقق من صحة المحتوى للنشر"""
        validation_result = {
            'valid': True,
            'issues': [],
            'warnings': []
        }
        
        # فحص العنوان
        title = article.get('title', '')
        if not title:
            validation_result['valid'] = False
            validation_result['issues'].append('العنوان مفقود')
        elif len(title) < 10:
            validation_result['warnings'].append('العنوان قصير جداً')
        
        # فحص المحتوى
        content = article.get('content', '')
        if not content:
            validation_result['valid'] = False
            validation_result['issues'].append('المحتوى مفقود')
        elif len(content.split()) < 50:
            validation_result['warnings'].append('المحتوى قصير جداً')
        
        # فحص الكلمات المفتاحية
        keywords = article.get('keywords', [])
        if not keywords:
            validation_result['warnings'].append('الكلمات المفتاحية مفقودة')
        
        return validation_result
    
    def _fix_validation_issues(self, article: Dict, validation_result: Dict) -> Dict:
        """إصلاح مشاكل التحقق"""
        for issue in validation_result['issues']:
            if 'العنوان مفقود' in issue:
                article['title'] = 'أخبار الألعاب الجديدة'
            elif 'المحتوى مفقود' in issue:
                article['content'] = 'محتوى متعلق بأخبار الألعاب والتحديثات الجديدة.'
        
        return article
    
    def _add_publishing_metadata(self, article: Dict) -> Dict:
        """إضافة معلومات النشر"""
        article['publishing_metadata'] = {
            'optimized_for_publishing': True,
            'optimization_date': datetime.now().isoformat(),
            'estimated_success_rate': self._estimate_success_rate(article),
            'publishing_priority': self._calculate_publishing_priority(article)
        }
        
        return article
    
    def _estimate_success_rate(self, article: Dict) -> float:
        """تقدير معدل نجاح النشر"""
        score = 50.0  # نقطة البداية
        
        # العنوان
        title = article.get('title', '')
        if len(title) >= 30:
            score += 15
        if any(emoji in title for emoji in ['🎮', '🔥', '⚡', '🚀']):
            score += 10
        
        # المحتوى
        content = article.get('content', '')
        word_count = len(content.split())
        if word_count >= 200:
            score += 20
        if word_count >= 500:
            score += 10
        
        # الكلمات المفتاحية
        if article.get('keywords'):
            score += 15
        
        # الوصف
        if article.get('summary'):
            score += 10
        
        # التصنيف
        if article.get('category'):
            score += 5
        
        return min(100.0, score)
    
    def _calculate_publishing_priority(self, article: Dict) -> str:
        """حساب أولوية النشر"""
        success_rate = self._estimate_success_rate(article)
        
        if success_rate >= 85:
            return 'high'
        elif success_rate >= 70:
            return 'medium'
        else:
            return 'low'
    
    def attempt_publish_with_retry(self, article: Dict, max_retries: int = 3) -> Dict:
        """محاولة النشر مع إعادة المحاولة"""
        self.publishing_stats['total_attempts'] += 1
        
        for attempt in range(max_retries + 1):
            try:
                # تحسين المقال قبل كل محاولة
                if attempt > 0:
                    article = self.optimize_article_for_publishing(article)
                
                # محاكاة عملية النشر
                success = self._simulate_publishing(article)
                
                if success:
                    self.publishing_stats['successful_publishes'] += 1
                    if attempt > 0:
                        self.publishing_stats['retry_successes'] += 1
                    
                    return {
                        'success': True,
                        'attempt': attempt + 1,
                        'message': 'تم النشر بنجاح'
                    }
                else:
                    if attempt < max_retries:
                        logger.warning(f"⚠️ فشل النشر - محاولة {attempt + 1}/{max_retries + 1}")
                        time.sleep(2)  # انتظار قبل إعادة المحاولة
                    
            except Exception as e:
                logger.error(f"❌ خطأ في محاولة النشر {attempt + 1}: {e}")
        
        # فشل جميع المحاولات
        self.publishing_stats['failed_publishes'] += 1
        return {
            'success': False,
            'attempts': max_retries + 1,
            'message': 'فشل النشر بعد جميع المحاولات'
        }
    
    def _simulate_publishing(self, article: Dict) -> bool:
        """محاكاة عملية النشر"""
        # محاكاة بناءً على جودة المقال
        success_rate = self._estimate_success_rate(article)
        
        # إضافة عشوائية للمحاكاة
        import random
        random_factor = random.uniform(0.8, 1.2)
        final_rate = min(100.0, success_rate * random_factor)
        
        return final_rate > 70.0
    
    def get_publishing_statistics(self) -> Dict:
        """الحصول على إحصاءات النشر"""
        total = self.publishing_stats['total_attempts']
        if total == 0:
            return {'message': 'لا توجد محاولات نشر بعد'}
        
        success_rate = (self.publishing_stats['successful_publishes'] / total) * 100
        
        return {
            'total_attempts': total,
            'successful_publishes': self.publishing_stats['successful_publishes'],
            'failed_publishes': self.publishing_stats['failed_publishes'],
            'success_rate': round(success_rate, 1),
            'retry_successes': self.publishing_stats['retry_successes'],
            'quality_rejections': self.publishing_stats['quality_rejections']
        }

# إنشاء مثيل عام
publishing_optimizer = PublishingSuccessOptimizer()
