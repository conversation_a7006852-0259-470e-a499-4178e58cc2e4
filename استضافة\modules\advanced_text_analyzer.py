# نظام تحليل متقدم للنص المستخرج من Whisper
import re
import asyncio
import aiohttp
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json
import hashlib
from .logger import logger
from .database import db

class AdvancedTextAnalyzer:
    """نظام AI متقدم لتحليل النص المستخرج والتحقق من جودته"""
    
    def __init__(self):
        # معايير التحليل المتقدم
        self.analysis_criteria = {
            'coherence': {
                'weight': 0.25,
                'min_score': 60
            },
            'relevance': {
                'weight': 0.30,
                'min_score': 70
            },
            'completeness': {
                'weight': 0.20,
                'min_score': 50
            },
            'accuracy': {
                'weight': 0.25,
                'min_score': 65
            }
        }
        
        # قواميس التحليل الذكي
        self.gaming_context_indicators = {
            'ar': {
                'strong': ['لعبة', 'ألعاب', 'جيمنج', 'لاعب', 'مستوى', 'تحدي', 'بطولة', 'مسابقة'],
                'medium': ['تطبيق', 'برنامج', 'تقنية', 'رقمي', 'إلكتروني', 'تفاعلي'],
                'weak': ['ترفيه', 'وقت', 'متعة', 'تسلية', 'نشاط']
            },
            'en': {
                'strong': ['game', 'gaming', 'gamer', 'player', 'level', 'challenge', 'tournament', 'esports'],
                'medium': ['app', 'software', 'technology', 'digital', 'electronic', 'interactive'],
                'weak': ['entertainment', 'fun', 'leisure', 'activity', 'hobby']
            }
        }
        
        # أنماط النص المشبوهة المتقدمة
        self.suspicious_patterns = {
            'repetitive_words': r'\b(\w+)\s+\1\s+\1',  # تكرار نفس الكلمة 3 مرات
            'nonsense_sequences': r'[a-zA-Z]{1,2}\s[a-zA-Z]{1,2}\s[a-zA-Z]{1,2}',  # حروف متقطعة
            'excessive_punctuation': r'[.!?]{3,}',  # علامات ترقيم مفرطة
            'random_numbers': r'\b\d+\s\d+\s\d+\s\d+',  # أرقام عشوائية متتالية
            'mixed_languages_chaos': r'[a-zA-Z]+[أ-ي]+[a-zA-Z]+',  # خلط عشوائي للغات
        }
    
    async def perform_deep_analysis(self, transcript: str, video_data: Dict = None) -> Dict:
        """تحليل عميق شامل للنص المستخرج"""
        try:
            logger.info("🧠 بدء التحليل العميق للنص المستخرج...")
            
            if not transcript or not transcript.strip():
                return self._create_analysis_report(0, "نص فارغ", {
                    'empty_text': True,
                    'analysis_type': 'failed'
                })
            
            # التحليلات المختلفة
            coherence_analysis = await self._analyze_text_coherence(transcript)
            relevance_analysis = await self._analyze_gaming_relevance(transcript, video_data)
            completeness_analysis = await self._analyze_text_completeness(transcript, video_data)
            accuracy_analysis = await self._analyze_transcription_accuracy(transcript, video_data)
            
            # تحليل الأنماط المشبوهة
            suspicious_analysis = self._detect_suspicious_patterns(transcript)
            
            # تحليل السياق والمعنى
            context_analysis = await self._analyze_context_consistency(transcript, video_data)
            
            # حساب النقاط الإجمالية
            total_score = self._calculate_weighted_score({
                'coherence': coherence_analysis,
                'relevance': relevance_analysis,
                'completeness': completeness_analysis,
                'accuracy': accuracy_analysis
            })
            
            # تحديد مستوى الجودة
            quality_level = self._determine_advanced_quality_level(total_score, suspicious_analysis)
            
            # إنشاء التقرير الشامل
            analysis_report = self._create_analysis_report(
                total_score, quality_level, {
                    'coherence_analysis': coherence_analysis,
                    'relevance_analysis': relevance_analysis,
                    'completeness_analysis': completeness_analysis,
                    'accuracy_analysis': accuracy_analysis,
                    'suspicious_analysis': suspicious_analysis,
                    'context_analysis': context_analysis,
                    'text_length': len(transcript),
                    'analysis_timestamp': datetime.now().isoformat()
                }
            )
            
            # تسجيل النتائج
            self._log_analysis_results(analysis_report, video_data)
            
            logger.info(f"✅ اكتمل التحليل العميق - النقاط: {total_score}/100 ({quality_level})")
            return analysis_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل العميق: {e}")
            return self._create_analysis_report(0, "خطأ في التحليل", {'error': str(e)})
    
    async def _analyze_text_coherence(self, transcript: str) -> Dict:
        """تحليل تماسك النص ومنطقيته"""
        try:
            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(transcript)
            
            if len(sentences) < 2:
                return {'score': 30, 'issues': ['نص قصير جداً'], 'sentence_count': len(sentences)}
            
            coherence_issues = []
            coherence_score = 100
            
            # فحص التماسك بين الجمل
            for i in range(len(sentences) - 1):
                current_sentence = sentences[i].strip()
                next_sentence = sentences[i + 1].strip()
                
                # فحص التكرار المفرط
                if self._calculate_sentence_similarity(current_sentence, next_sentence) > 0.8:
                    coherence_issues.append(f"تكرار مفرط بين الجملة {i+1} و {i+2}")
                    coherence_score -= 10
                
                # فحص الانقطاع المفاجئ في الموضوع
                if not self._check_topic_continuity(current_sentence, next_sentence):
                    coherence_issues.append(f"انقطاع في الموضوع بين الجملة {i+1} و {i+2}")
                    coherence_score -= 5
            
            # فحص التدفق العام للنص
            flow_score = self._analyze_text_flow(sentences)
            coherence_score = (coherence_score + flow_score) / 2
            
            return {
                'score': max(coherence_score, 0),
                'issues': coherence_issues,
                'sentence_count': len(sentences),
                'flow_score': flow_score
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل التماسك: {e}")
            return {'score': 0, 'issues': ['خطأ في التحليل'], 'error': str(e)}
    
    async def _analyze_gaming_relevance(self, transcript: str, video_data: Dict = None) -> Dict:
        """تحليل مدى صلة النص بالألعاب"""
        try:
            text_lower = transcript.lower()
            
            # تحديد اللغة الرئيسية
            language = self._detect_primary_language(transcript)
            
            # حساب نقاط الصلة بالألعاب
            relevance_scores = {
                'strong_indicators': 0,
                'medium_indicators': 0,
                'weak_indicators': 0
            }
            
            indicators = self.gaming_context_indicators.get(language, self.gaming_context_indicators['en'])
            
            for strength, keywords in indicators.items():
                count = sum(1 for keyword in keywords if keyword in text_lower)
                relevance_scores[f'{strength}_indicators'] = count
            
            # حساب النقاط الإجمالية
            total_score = (
                relevance_scores['strong_indicators'] * 30 +
                relevance_scores['medium_indicators'] * 15 +
                relevance_scores['weak_indicators'] * 5
            )
            
            # تحليل السياق مع بيانات الفيديو
            context_bonus = 0
            if video_data:
                context_bonus = await self._analyze_video_context_match(transcript, video_data)
            
            final_score = min(total_score + context_bonus, 100)
            
            return {
                'score': final_score,
                'strong_indicators': relevance_scores['strong_indicators'],
                'medium_indicators': relevance_scores['medium_indicators'],
                'weak_indicators': relevance_scores['weak_indicators'],
                'context_bonus': context_bonus,
                'detected_language': language
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الصلة بالألعاب: {e}")
            return {'score': 0, 'error': str(e)}
    
    async def _analyze_text_completeness(self, transcript: str, video_data: Dict = None) -> Dict:
        """تحليل اكتمال النص"""
        try:
            # فحص طول النص
            text_length = len(transcript.strip())
            word_count = len(transcript.split())
            
            # تقدير الطول المتوقع بناءً على مدة الفيديو
            expected_length = 0
            if video_data and video_data.get('duration'):
                # تقدير: حوالي 150 كلمة في الدقيقة للكلام العادي
                duration_minutes = video_data['duration'] / 60
                expected_length = duration_minutes * 150
            
            # حساب نسبة الاكتمال
            completeness_ratio = 1.0
            if expected_length > 0:
                completeness_ratio = min(word_count / expected_length, 1.0)
            
            # فحص وجود بداية ونهاية منطقية
            has_proper_start = self._check_proper_text_start(transcript)
            has_proper_end = self._check_proper_text_end(transcript)
            
            # فحص الانقطاعات المفاجئة
            abrupt_cuts = self._detect_abrupt_cuts(transcript)
            
            # حساب النقاط
            base_score = completeness_ratio * 60
            structure_bonus = 0
            
            if has_proper_start:
                structure_bonus += 15
            if has_proper_end:
                structure_bonus += 15
            
            cut_penalty = len(abrupt_cuts) * 5
            
            final_score = max(base_score + structure_bonus - cut_penalty, 0)
            
            return {
                'score': min(final_score, 100),
                'text_length': text_length,
                'word_count': word_count,
                'expected_length': expected_length,
                'completeness_ratio': completeness_ratio,
                'has_proper_start': has_proper_start,
                'has_proper_end': has_proper_end,
                'abrupt_cuts': abrupt_cuts
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الاكتمال: {e}")
            return {'score': 0, 'error': str(e)}
    
    async def _analyze_transcription_accuracy(self, transcript: str, video_data: Dict = None) -> Dict:
        """تحليل دقة التحويل من الصوت إلى النص"""
        try:
            accuracy_indicators = {
                'proper_grammar': 0,
                'logical_word_sequences': 0,
                'consistent_language': 0,
                'realistic_content': 0
            }
            
            # فحص القواعد النحوية الأساسية
            grammar_score = self._check_basic_grammar(transcript)
            accuracy_indicators['proper_grammar'] = grammar_score
            
            # فحص تسلسل الكلمات المنطقي
            sequence_score = self._check_word_sequences(transcript)
            accuracy_indicators['logical_word_sequences'] = sequence_score
            
            # فحص ثبات اللغة
            language_consistency = self._check_language_consistency(transcript)
            accuracy_indicators['consistent_language'] = language_consistency
            
            # فحص واقعية المحتوى
            realism_score = self._check_content_realism(transcript)
            accuracy_indicators['realistic_content'] = realism_score
            
            # حساب النقاط الإجمالية
            total_score = sum(accuracy_indicators.values()) / len(accuracy_indicators)
            
            return {
                'score': total_score,
                'grammar_score': grammar_score,
                'sequence_score': sequence_score,
                'language_consistency': language_consistency,
                'realism_score': realism_score,
                'indicators': accuracy_indicators
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل دقة التحويل: {e}")
            return {'score': 0, 'error': str(e)}
    
    def _detect_suspicious_patterns(self, transcript: str) -> Dict:
        """كشف الأنماط المشبوهة في النص"""
        try:
            suspicious_findings = {}
            
            for pattern_name, pattern_regex in self.suspicious_patterns.items():
                matches = re.findall(pattern_regex, transcript)
                if matches:
                    suspicious_findings[pattern_name] = {
                        'count': len(matches),
                        'examples': matches[:3]  # أول 3 أمثلة
                    }
            
            # حساب مستوى الشك
            suspicion_level = len(suspicious_findings)
            if suspicion_level == 0:
                suspicion_rating = 'منخفض'
            elif suspicion_level <= 2:
                suspicion_rating = 'متوسط'
            else:
                suspicion_rating = 'عالي'
            
            return {
                'suspicion_level': suspicion_level,
                'suspicion_rating': suspicion_rating,
                'findings': suspicious_findings,
                'is_suspicious': suspicion_level > 2
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في كشف الأنماط المشبوهة: {e}")
            return {'suspicion_level': 0, 'suspicion_rating': 'غير محدد', 'error': str(e)}
    
    async def _analyze_context_consistency(self, transcript: str, video_data: Dict = None) -> Dict:
        """تحليل ثبات السياق"""
        try:
            if not video_data:
                return {'score': 50, 'note': 'لا توجد بيانات فيديو للمقارنة'}
            
            # مقارنة النص مع عنوان الفيديو
            title_match = self._calculate_text_similarity(
                transcript.lower(), 
                video_data.get('title', '').lower()
            )
            
            # مقارنة النص مع وصف الفيديو
            description_match = 0
            if video_data.get('description'):
                description_match = self._calculate_text_similarity(
                    transcript.lower(),
                    video_data['description'].lower()
                )
            
            # فحص ثبات الموضوع عبر النص
            topic_consistency = self._check_topic_consistency_throughout(transcript)
            
            # حساب النقاط الإجمالية
            context_score = (title_match * 40 + description_match * 30 + topic_consistency * 30) / 100
            
            return {
                'score': min(context_score * 100, 100),
                'title_match': title_match,
                'description_match': description_match,
                'topic_consistency': topic_consistency
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل ثبات السياق: {e}")
            return {'score': 0, 'error': str(e)}
    
    def _calculate_weighted_score(self, analyses: Dict) -> float:
        """حساب النقاط المرجحة"""
        try:
            total_score = 0
            total_weight = 0
            
            for analysis_type, analysis_result in analyses.items():
                if analysis_type in self.analysis_criteria:
                    weight = self.analysis_criteria[analysis_type]['weight']
                    score = analysis_result.get('score', 0)
                    
                    total_score += score * weight
                    total_weight += weight
            
            return total_score / total_weight if total_weight > 0 else 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في حساب النقاط المرجحة: {e}")
            return 0.0
    
    def _determine_advanced_quality_level(self, score: float, suspicious_analysis: Dict) -> str:
        """تحديد مستوى الجودة المتقدم"""
        try:
            # تعديل النقاط بناءً على الأنماط المشبوهة
            adjusted_score = score
            
            if suspicious_analysis.get('is_suspicious', False):
                adjusted_score -= 20
            elif suspicious_analysis.get('suspicion_rating') == 'متوسط':
                adjusted_score -= 10
            
            # تحديد المستوى
            if adjusted_score >= 85:
                return 'ممتاز جداً'
            elif adjusted_score >= 75:
                return 'ممتاز'
            elif adjusted_score >= 65:
                return 'جيد جداً'
            elif adjusted_score >= 55:
                return 'جيد'
            elif adjusted_score >= 45:
                return 'مقبول'
            elif adjusted_score >= 30:
                return 'ضعيف'
            else:
                return 'سيء جداً'
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحديد مستوى الجودة: {e}")
            return 'غير محدد'
    
    def _create_analysis_report(self, score: float, quality_level: str, details: Dict) -> Dict:
        """إنشاء تقرير التحليل"""
        return {
            'score': round(score, 2),
            'quality_level': quality_level,
            'is_acceptable': score >= 60,  # حد أعلى للقبول
            'is_excellent': score >= 80,
            'timestamp': datetime.now().isoformat(),
            'analysis_details': details,
            'recommendations': self._generate_advanced_recommendations(score, details)
        }
    
    def _generate_advanced_recommendations(self, score: float, details: Dict) -> List[str]:
        """إنشاء توصيات متقدمة"""
        recommendations = []
        
        if score < 60:
            recommendations.append("النص يحتاج إلى تحسين كبير - قد يكون Whisper فشل في التحويل")
        
        if details.get('suspicious_analysis', {}).get('is_suspicious'):
            recommendations.append("تم اكتشاف أنماط مشبوهة - يُنصح بمراجعة يدوية")
        
        coherence = details.get('coherence_analysis', {})
        if coherence.get('score', 0) < 50:
            recommendations.append("النص يفتقر للتماسك المنطقي")
        
        relevance = details.get('relevance_analysis', {})
        if relevance.get('score', 0) < 40:
            recommendations.append("النص لا يبدو متعلقاً بالألعاب")
        
        completeness = details.get('completeness_analysis', {})
        if completeness.get('score', 0) < 40:
            recommendations.append("النص يبدو غير مكتمل")
        
        if not recommendations:
            recommendations.append("جودة النص مقبولة للمعالجة")
        
        return recommendations
    
    def _log_analysis_results(self, analysis_report: Dict, video_data: Dict = None):
        """تسجيل نتائج التحليل"""
        try:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'quality_score': analysis_report.get('score', 0.0),  # تصحيح اسم المفتاح
                'quality_level': analysis_report.get('quality_level', 'غير محدد'),
                'is_acceptable': analysis_report.get('is_acceptable', False),
                'video_id': video_data.get('id') if video_data else None,
                'video_title': video_data.get('title') if video_data else None,
                'analysis_type': 'advanced_deep_analysis'
            }

            # حفظ في قاعدة البيانات
            db.log_whisper_quality_check(log_data)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تسجيل نتائج التحليل: {e}")
            logger.debug(f"بيانات التحليل: {analysis_report}")

    # دوال مساعدة
    def _split_into_sentences(self, text: str) -> List[str]:
        """تقسيم النص إلى جمل"""
        sentences = re.split(r'[.!?؟।]', text)
        return [s.strip() for s in sentences if len(s.strip()) > 5]

    def _calculate_sentence_similarity(self, sent1: str, sent2: str) -> float:
        """حساب التشابه بين جملتين"""
        words1 = set(sent1.lower().split())
        words2 = set(sent2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _check_topic_continuity(self, sent1: str, sent2: str) -> bool:
        """فحص استمرارية الموضوع بين جملتين"""
        # فحص بسيط للاستمرارية
        similarity = self._calculate_sentence_similarity(sent1, sent2)
        return similarity > 0.1  # حد أدنى للاستمرارية

    def _analyze_text_flow(self, sentences: List[str]) -> float:
        """تحليل تدفق النص"""
        if len(sentences) < 2:
            return 50.0

        flow_score = 100.0
        for i in range(len(sentences) - 1):
            if not self._check_topic_continuity(sentences[i], sentences[i + 1]):
                flow_score -= 10

        return max(flow_score, 0.0)

    def _detect_primary_language(self, text: str) -> str:
        """تحديد اللغة الرئيسية للنص"""
        arabic_chars = len(re.findall(r'[\u0600-\u06FF]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))

        if arabic_chars > english_chars:
            return 'ar'
        else:
            return 'en'

    async def _analyze_video_context_match(self, transcript: str, video_data: Dict) -> float:
        """تحليل تطابق النص مع سياق الفيديو"""
        try:
            title = video_data.get('title', '').lower()
            description = video_data.get('description', '').lower()

            title_similarity = self._calculate_text_similarity(transcript.lower(), title)
            desc_similarity = self._calculate_text_similarity(transcript.lower(), description)

            return (title_similarity * 60 + desc_similarity * 40) / 100
        except:
            return 0.0

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """حساب التشابه بين نصين"""
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _check_proper_text_start(self, text: str) -> bool:
        """فحص وجود بداية منطقية للنص"""
        start_indicators = ['مرحبا', 'أهلا', 'السلام', 'hello', 'hi', 'welcome', 'today', 'اليوم']
        first_words = text.lower().split()[:5]
        return any(indicator in ' '.join(first_words) for indicator in start_indicators)

    def _check_proper_text_end(self, text: str) -> bool:
        """فحص وجود نهاية منطقية للنص"""
        end_indicators = ['شكرا', 'وداعا', 'إلى اللقاء', 'thanks', 'goodbye', 'see you', 'bye']
        last_words = text.lower().split()[-5:]
        return any(indicator in ' '.join(last_words) for indicator in end_indicators)

    def _detect_abrupt_cuts(self, text: str) -> List[str]:
        """كشف الانقطاعات المفاجئة في النص"""
        cuts = []
        sentences = self._split_into_sentences(text)

        for i, sentence in enumerate(sentences):
            if len(sentence.split()) < 3:  # جملة قصيرة جداً
                cuts.append(f"جملة قصيرة في الموضع {i+1}: {sentence}")

        return cuts

    def _check_basic_grammar(self, text: str) -> float:
        """فحص القواعد النحوية الأساسية"""
        # فحص بسيط للقواعد
        score = 100.0

        # فحص وجود مساحات مناسبة
        if re.search(r'\w{20,}', text):  # كلمات طويلة جداً
            score -= 20

        # فحص علامات الترقيم
        if not re.search(r'[.!?؟]', text):  # لا توجد علامات ترقيم
            score -= 15

        return max(score, 0.0)

    def _check_word_sequences(self, text: str) -> float:
        """فحص تسلسل الكلمات المنطقي"""
        words = text.split()
        if len(words) < 3:
            return 50.0

        score = 100.0

        # فحص التكرار المفرط
        for i in range(len(words) - 2):
            if words[i] == words[i+1] == words[i+2]:
                score -= 15

        return max(score, 0.0)

    def _check_language_consistency(self, text: str) -> float:
        """فحص ثبات اللغة"""
        arabic_ratio = len(re.findall(r'[\u0600-\u06FF]', text)) / len(text) if text else 0
        english_ratio = len(re.findall(r'[a-zA-Z]', text)) / len(text) if text else 0

        # النص يجب أن يكون في لغة واحدة أساسية
        if arabic_ratio > 0.7 or english_ratio > 0.7:
            return 100.0
        elif arabic_ratio > 0.4 or english_ratio > 0.4:
            return 70.0
        else:
            return 30.0  # خليط غير منطقي

    def _check_content_realism(self, text: str) -> float:
        """فحص واقعية المحتوى"""
        # فحص بسيط للواقعية
        score = 100.0

        # فحص وجود كلمات غريبة أو غير منطقية
        weird_patterns = [
            r'[a-zA-Z]{1}\s[a-zA-Z]{1}\s[a-zA-Z]{1}',  # حروف منفردة
            r'\d+\s\d+\s\d+\s\d+',  # أرقام متتالية
        ]

        for pattern in weird_patterns:
            if re.search(pattern, text):
                score -= 20

        return max(score, 0.0)

    def _check_topic_consistency_throughout(self, text: str) -> float:
        """فحص ثبات الموضوع عبر النص"""
        sentences = self._split_into_sentences(text)
        if len(sentences) < 3:
            return 70.0

        # فحص التماسك بين الجمل
        consistency_score = 100.0

        for i in range(len(sentences) - 1):
            similarity = self._calculate_sentence_similarity(sentences[i], sentences[i+1])
            if similarity < 0.05:  # لا يوجد تشابه تقريباً
                consistency_score -= 10

        return max(consistency_score, 0.0)

# إنشاء مثيل عام
advanced_text_analyzer = AdvancedTextAnalyzer()
