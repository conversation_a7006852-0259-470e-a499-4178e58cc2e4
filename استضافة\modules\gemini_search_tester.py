# أداة اختبار Gemini 2.5 Pro API مع البحث العميق على الويب
import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import os

from .logger import logger

@dataclass
class GeminiTestResult:
    """نتيجة اختبار Gemini"""
    success: bool
    response_text: str
    execution_time: float
    model_used: str
    has_web_search: bool
    search_results: List[Dict] = None
    error_message: str = None
    metadata: Dict[str, Any] = None

class GeminiSearchTester:
    """أداة اختبار Gemini 2.5 Pro مع البحث العميق"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        
        # إعدادات API
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        # استخدام Gemini 2.5 Pro فقط
        self.models = {
            'gemini-2.5-pro': 'models/gemini-2.5-pro'
        }
        
        # اختبارات البحث العميق
        self.web_search_tests = [
            {
                'name': 'Gaming News Search',
                'query': 'What are the latest gaming news today? Search for recent PlayStation 5 and Xbox Series X updates.',
                'expected_keywords': ['gaming', 'playstation', 'xbox', 'news', 'today']
            },
            {
                'name': 'Tech Product Search',
                'query': 'Search for the latest reviews of iPhone 16 Pro and compare with Samsung Galaxy S25.',
                'expected_keywords': ['iphone', 'samsung', 'review', 'comparison']
            },
            {
                'name': 'Current Events Search',
                'query': 'What are the current trending topics in technology? Search for recent AI developments.',
                'expected_keywords': ['technology', 'ai', 'trending', 'recent']
            },
            {
                'name': 'Specific Information Search',
                'query': 'Search for the current price of Bitcoin and Ethereum today.',
                'expected_keywords': ['bitcoin', 'ethereum', 'price', 'today']
            }
        ]
        
        # إحصائيات الاختبار
        self.test_stats = {
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'models_tested': [],
            'web_search_available': False,
            'average_response_time': 0.0
        }
        
        logger.info("🧪 تم تهيئة أداة اختبار Gemini 2.5 Pro")
    
    async def test_api_key_validity(self) -> Dict[str, Any]:
        """اختبار صحة API Key"""
        if not self.api_key:
            return {
                'valid': False,
                'error': 'لم يتم توفير API Key',
                'suggestion': 'قم بتعيين GEMINI_API_KEY أو GOOGLE_API_KEY في متغيرات البيئة'
            }
        
        try:
            # اختبار بسيط للتحقق من صحة المفتاح
            test_url = f"{self.base_url}/models/gemini-2.5-flash:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': self.api_key
            }
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": "Hello, this is a test message."
                    }]
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(test_url, headers=headers, params=params, json=payload, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'valid': True,
                            'model': 'gemini-2.5-pro',
                            'response_preview': data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')[:100]
                        }
                    elif response.status == 403:
                        return {
                            'valid': False,
                            'error': 'API Key غير صحيح أو منتهي الصلاحية',
                            'status_code': response.status
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'valid': False,
                            'error': f'خطأ في API: {response.status}',
                            'details': error_text,
                            'status_code': response.status
                        }
        
        except Exception as e:
            return {
                'valid': False,
                'error': f'فشل في الاتصال بـ API: {str(e)}',
                'suggestion': 'تحقق من الاتصال بالإنترنت وصحة API Key'
            }
    
    async def test_model_availability(self) -> Dict[str, Any]:
        """اختبار توفر النماذج المختلفة"""
        results = {}
        
        for model_name, model_path in self.models.items():
            try:
                logger.info(f"🔍 اختبار النموذج: {model_name}")
                
                test_url = f"{self.base_url}/{model_path}:generateContent"
                
                headers = {
                    'Content-Type': 'application/json'
                }
                
                params = {
                    'key': self.api_key
                }
                
                payload = {
                    "contents": [{
                        "parts": [{
                            "text": f"Test message for {model_name}. Please respond briefly."
                        }]
                    }]
                }
                
                start_time = time.time()
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(test_url, headers=headers, params=params, json=payload, timeout=30) as response:
                        execution_time = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            response_text = data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
                            
                            results[model_name] = {
                                'available': True,
                                'response_time': execution_time,
                                'response_preview': response_text[:100],
                                'full_response': response_text
                            }
                            
                            self.test_stats['models_tested'].append(model_name)
                            
                        else:
                            error_text = await response.text()
                            results[model_name] = {
                                'available': False,
                                'error': f'HTTP {response.status}',
                                'details': error_text
                            }
            
            except Exception as e:
                results[model_name] = {
                    'available': False,
                    'error': str(e)
                }
        
        return results
    
    async def test_web_search_capability(self, model_name: str = 'gemini-2.5-pro') -> List[GeminiTestResult]:
        """اختبار قدرة البحث على الويب"""
        results = []
        
        if not self.api_key:
            logger.error("❌ لا يوجد API Key للاختبار")
            return results
        
        model_path = self.models.get(model_name, self.models['gemini-2.5-pro'])
        
        for test_case in self.web_search_tests:
            try:
                logger.info(f"🌐 اختبار البحث العميق: {test_case['name']}")
                
                start_time = time.time()
                result = await self._perform_web_search_test(model_path, test_case)
                execution_time = time.time() - start_time
                
                # تحليل النتيجة للبحث عن دلائل البحث على الويب
                has_web_search = self._analyze_web_search_response(result.get('response_text', ''), test_case['expected_keywords'])
                
                test_result = GeminiTestResult(
                    success=result.get('success', False),
                    response_text=result.get('response_text', ''),
                    execution_time=execution_time,
                    model_used=model_name,
                    has_web_search=has_web_search,
                    search_results=result.get('search_results', []),
                    error_message=result.get('error'),
                    metadata={
                        'test_name': test_case['name'],
                        'query': test_case['query'],
                        'expected_keywords': test_case['expected_keywords']
                    }
                )
                
                results.append(test_result)
                
                # تحديث الإحصائيات
                self.test_stats['total_tests'] += 1
                if test_result.success:
                    self.test_stats['successful_tests'] += 1
                    if test_result.has_web_search:
                        self.test_stats['web_search_available'] = True
                else:
                    self.test_stats['failed_tests'] += 1
                
                # انتظار قصير بين الاختبارات
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ فشل في اختبار {test_case['name']}: {e}")
                
                error_result = GeminiTestResult(
                    success=False,
                    response_text="",
                    execution_time=0,
                    model_used=model_name,
                    has_web_search=False,
                    error_message=str(e),
                    metadata={'test_name': test_case['name'], 'query': test_case['query']}
                )
                results.append(error_result)
                self.test_stats['total_tests'] += 1
                self.test_stats['failed_tests'] += 1
        
        # حساب متوسط وقت الاستجابة
        successful_times = [r.execution_time for r in results if r.success]
        if successful_times:
            self.test_stats['average_response_time'] = sum(successful_times) / len(successful_times)
        
        return results
    
    async def _perform_web_search_test(self, model_path: str, test_case: Dict) -> Dict[str, Any]:
        """تنفيذ اختبار البحث على الويب"""
        try:
            test_url = f"{self.base_url}/{model_path}:generateContent"
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            params = {
                'key': self.api_key
            }
            
            # إضافة تعليمات واضحة للبحث على الويب
            enhanced_query = f"""
            {test_case['query']}
            
            Please search the web for current and up-to-date information. 
            If you have access to web search, please use it to provide the most recent information.
            Include sources and links if available.
            """
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": enhanced_query
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 2048
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(test_url, headers=headers, params=params, json=payload, timeout=60) as response:
                    if response.status == 200:
                        data = await response.json()
                        response_text = data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
                        
                        return {
                            'success': True,
                            'response_text': response_text,
                            'raw_response': data
                        }
                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'HTTP {response.status}: {error_text}',
                            'response_text': ''
                        }
        
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_text': ''
            }
    
    def _analyze_web_search_response(self, response_text: str, expected_keywords: List[str]) -> bool:
        """تحليل الاستجابة للبحث عن دلائل البحث على الويب"""
        if not response_text:
            return False
        
        response_lower = response_text.lower()
        
        # دلائل البحث على الويب
        web_search_indicators = [
            'according to recent reports',
            'based on current information',
            'latest news',
            'recent updates',
            'as of today',
            'current price',
            'latest data',
            'recent developments',
            'up-to-date information',
            'source:',
            'according to',
            'reported by',
            'news sources',
            'latest information'
        ]
        
        # فحص وجود دلائل البحث
        has_web_indicators = any(indicator in response_lower for indicator in web_search_indicators)
        
        # فحص وجود الكلمات المفتاحية المتوقعة
        has_expected_keywords = any(keyword.lower() in response_lower for keyword in expected_keywords)
        
        # فحص طول الاستجابة (الاستجابات من البحث عادة أطول)
        is_detailed_response = len(response_text) > 200
        
        # فحص وجود تواريخ حديثة
        current_year = datetime.now().year
        has_recent_dates = str(current_year) in response_text or 'today' in response_lower or 'recent' in response_lower
        
        # تقييم شامل
        score = 0
        if has_web_indicators:
            score += 3
        if has_expected_keywords:
            score += 2
        if is_detailed_response:
            score += 1
        if has_recent_dates:
            score += 2
        
        return score >= 4  # عتبة للحكم على وجود بحث ويب
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """تشغيل اختبار شامل لـ Gemini 2.5 Pro"""
        logger.info("🚀 بدء الاختبار الشامل لـ Gemini 2.5 Pro")
        
        comprehensive_results = {
            'timestamp': datetime.now().isoformat(),
            'api_key_test': {},
            'model_availability': {},
            'web_search_tests': [],
            'summary': {},
            'recommendations': []
        }
        
        try:
            # 1. اختبار صحة API Key
            logger.info("🔑 اختبار صحة API Key...")
            api_key_result = await self.test_api_key_validity()
            comprehensive_results['api_key_test'] = api_key_result
            
            if not api_key_result.get('valid', False):
                comprehensive_results['summary'] = {
                    'overall_status': 'failed',
                    'reason': 'API Key غير صحيح',
                    'web_search_available': False
                }
                return comprehensive_results
            
            # 2. اختبار توفر النماذج
            logger.info("🤖 اختبار توفر النماذج...")
            model_results = await self.test_model_availability()
            comprehensive_results['model_availability'] = model_results
            
            # 3. اختبار البحث على الويب
            logger.info("🌐 اختبار قدرة البحث على الويب...")
            web_search_results = await self.test_web_search_capability('gemini-2.5-pro')
            comprehensive_results['web_search_tests'] = [
                {
                    'test_name': result.metadata.get('test_name'),
                    'success': result.success,
                    'has_web_search': result.has_web_search,
                    'execution_time': result.execution_time,
                    'response_preview': result.response_text[:200] + "..." if len(result.response_text) > 200 else result.response_text,
                    'error': result.error_message
                }
                for result in web_search_results
            ]
            
            # 4. إنشاء الملخص
            comprehensive_results['summary'] = self._generate_test_summary()
            
            # 5. إنشاء التوصيات
            comprehensive_results['recommendations'] = self._generate_recommendations()
            
            logger.info("✅ الاختبار الشامل مكتمل")
            
        except Exception as e:
            logger.error(f"❌ فشل في الاختبار الشامل: {e}")
            comprehensive_results['summary'] = {
                'overall_status': 'error',
                'reason': str(e),
                'web_search_available': False
            }
        
        return comprehensive_results
    
    def _generate_test_summary(self) -> Dict[str, Any]:
        """إنشاء ملخص نتائج الاختبار"""
        total_tests = self.test_stats['total_tests']
        success_rate = (self.test_stats['successful_tests'] / total_tests * 100) if total_tests > 0 else 0
        
        return {
            'overall_status': 'success' if success_rate >= 70 else 'partial' if success_rate >= 30 else 'failed',
            'total_tests': total_tests,
            'successful_tests': self.test_stats['successful_tests'],
            'failed_tests': self.test_stats['failed_tests'],
            'success_rate': f"{success_rate:.1f}%",
            'web_search_available': self.test_stats['web_search_available'],
            'models_tested': self.test_stats['models_tested'],
            'average_response_time': f"{self.test_stats['average_response_time']:.2f}s"
        }
    
    def _generate_recommendations(self) -> List[str]:
        """إنشاء توصيات بناءً على نتائج الاختبار"""
        recommendations = []
        
        if self.test_stats['web_search_available']:
            recommendations.append("✅ Gemini 2.5 Pro يدعم البحث على الويب - يمكن استخدامه للبحث العميق")
        else:
            recommendations.append("❌ لم يتم اكتشاف قدرة البحث على الويب - قد يكون محدود للمعلومات المدربة")
        
        if self.test_stats['average_response_time'] > 10:
            recommendations.append("⚠️ أوقات الاستجابة بطيئة - قد تحتاج لتحسين الاستعلامات")
        
        if self.test_stats['successful_tests'] < self.test_stats['total_tests']:
            recommendations.append("⚠️ بعض الاختبارات فشلت - راجع حدود API أو صياغة الاستعلامات")
        
        if len(self.test_stats['models_tested']) > 0:
            recommendations.append(f"✅ النماذج المتاحة: {', '.join(self.test_stats['models_tested'])}")
        
        return recommendations
    
    def get_test_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاختبار"""
        return self.test_stats.copy()

# دالة مساعدة للاختبار السريع
async def quick_gemini_test(api_key: str = None) -> Dict[str, Any]:
    """اختبار سريع لـ Gemini 2.5 Pro"""
    tester = GeminiSearchTester(api_key)
    return await tester.run_comprehensive_test()

# إنشاء مثيل عام
gemini_search_tester = GeminiSearchTester()
