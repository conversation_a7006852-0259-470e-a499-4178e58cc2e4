# إعدادات توفير استهلاك API
from dataclasses import dataclass
from typing import Dict, List, Any
from enum import Enum

class ConservationLevel(Enum):
    """مستوى التوفير"""
    MINIMAL = "minimal"        # توفير أدنى
    MODERATE = "moderate"      # توفير متوسط
    AGGRESSIVE = "aggressive"  # توفير قوي
    MAXIMUM = "maximum"        # توفير أقصى

@dataclass
class APIConservationConfig:
    """إعدادات توفير API"""
    
    # مستوى التوفير العام
    conservation_level: ConservationLevel = ConservationLevel.MODERATE
    
    # إعدادات التخزين المؤقت
    enable_aggressive_caching: bool = True
    cache_ttl_multiplier: float = 2.0  # مضاعف مدة التخزين المؤقت
    max_cache_size: int = 50000
    
    # إعدادات البحث المحلي
    prefer_local_search: bool = True
    local_search_threshold: float = 0.6  # عتبة جودة البحث المحلي
    max_local_results_ratio: float = 0.7  # نسبة النتائج المحلية القصوى
    
    # إعدادات الحدود اليومية
    daily_budget_usd: float = 2.0
    emergency_reserve_usd: float = 0.5
    auto_disable_expensive_apis: bool = True
    
    # إعدادات الأولوية
    api_priority_order: List[str] = None
    disable_ai_enhancement_when_expensive: bool = True
    reduce_semantic_analysis_when_expensive: bool = True
    
    # إعدادات التنبيهات
    enable_cost_alerts: bool = True
    cost_warning_threshold: float = 1.5  # دولار
    cost_critical_threshold: float = 1.8  # دولار
    
    # إعدادات الاستراتيجيات
    fallback_strategies: Dict[str, Any] = None
    
    def __post_init__(self):
        """تهيئة الإعدادات الافتراضية"""
        if self.api_priority_order is None:
            self.api_priority_order = [
                'local',      # أولوية عالية - مجاني
                'tavily',     # أولوية متوسطة - رخيص
                'serpapi',    # أولوية منخفضة - مكلف
                'openai',     # للضرورة فقط
                'anthropic'   # للضرورة فقط
            ]
        
        if self.fallback_strategies is None:
            self.fallback_strategies = {
                'when_api_limit_reached': 'use_local_search',
                'when_budget_exceeded': 'cache_only',
                'when_all_apis_down': 'local_search_only',
                'when_low_quality_results': 'combine_local_and_cached'
            }
        
        # تطبيق إعدادات مستوى التوفير
        self._apply_conservation_level()
    
    def _apply_conservation_level(self):
        """تطبيق إعدادات مستوى التوفير"""
        if self.conservation_level == ConservationLevel.MINIMAL:
            self.cache_ttl_multiplier = 1.2
            self.prefer_local_search = False
            self.local_search_threshold = 0.8
            self.daily_budget_usd = 5.0
            
        elif self.conservation_level == ConservationLevel.MODERATE:
            self.cache_ttl_multiplier = 2.0
            self.prefer_local_search = True
            self.local_search_threshold = 0.6
            self.daily_budget_usd = 2.0
            
        elif self.conservation_level == ConservationLevel.AGGRESSIVE:
            self.cache_ttl_multiplier = 3.0
            self.prefer_local_search = True
            self.local_search_threshold = 0.4
            self.daily_budget_usd = 1.0
            self.disable_ai_enhancement_when_expensive = True
            self.reduce_semantic_analysis_when_expensive = True
            
        elif self.conservation_level == ConservationLevel.MAXIMUM:
            self.cache_ttl_multiplier = 5.0
            self.prefer_local_search = True
            self.local_search_threshold = 0.2
            self.daily_budget_usd = 0.5
            self.disable_ai_enhancement_when_expensive = True
            self.reduce_semantic_analysis_when_expensive = True
            self.auto_disable_expensive_apis = True

class APIConservationManager:
    """مدير توفير API"""
    
    def __init__(self):
        self.config = APIConservationConfig()
        self.conservation_stats = {
            'api_calls_saved': 0,
            'cost_saved': 0.0,
            'local_searches_used': 0,
            'cache_hits_gained': 0,
            'conservation_events': []
        }
        
        # قواعد التوفير
        self.conservation_rules = {
            'high_cost_day': {
                'condition': lambda stats: stats.get('daily_cost', 0) > self.config.cost_warning_threshold,
                'action': 'reduce_api_usage',
                'description': 'تقليل استخدام API بسبب التكلفة العالية'
            },
            'api_limit_near': {
                'condition': lambda stats: stats.get('api_usage_ratio', 0) > 0.8,
                'action': 'switch_to_local',
                'description': 'التحول للبحث المحلي بسبب اقتراب الحد'
            },
            'low_quality_expensive': {
                'condition': lambda stats: stats.get('quality_score', 1) < 0.6 and stats.get('daily_cost', 0) > 1.0,
                'action': 'prefer_local_and_cache',
                'description': 'تفضيل البحث المحلي والتخزين المؤقت'
            },
            'weekend_conservation': {
                'condition': lambda stats: self._is_weekend(),
                'action': 'reduce_non_essential',
                'description': 'تقليل الاستخدام غير الضروري في عطلة نهاية الأسبوع'
            }
        }
    
    def should_use_api(self, api_name: str, query_priority: str = "normal") -> tuple[bool, str]:
        """تحديد ما إذا كان يجب استخدام API"""
        try:
            # فحص الميزانية اليومية
            from .api_usage_manager import api_usage_manager
            usage_report = api_usage_manager.get_usage_report()
            daily_cost = usage_report['totals']['total_cost']
            
            # فحص تجاوز الميزانية
            if daily_cost >= self.config.daily_budget_usd:
                return False, f"تم تجاوز الميزانية اليومية ({daily_cost:.2f}$)"
            
            # فحص الاحتياطي الطارئ
            if daily_cost >= (self.config.daily_budget_usd - self.config.emergency_reserve_usd):
                if query_priority not in ['high', 'critical']:
                    return False, "الاحتفاظ بالاحتياطي الطارئ"
            
            # فحص APIs المكلفة
            expensive_apis = ['openai', 'anthropic', 'serpapi']
            if api_name in expensive_apis and self.config.auto_disable_expensive_apis:
                if daily_cost > self.config.cost_warning_threshold:
                    return False, f"API مكلف والتكلفة عالية ({daily_cost:.2f}$)"
            
            # فحص الأولوية
            if api_name not in self.config.api_priority_order[:3]:  # أول 3 فقط
                if query_priority == "low":
                    return False, "أولوية منخفضة لـ API غير مفضل"
            
            return True, "يمكن الاستخدام"
            
        except Exception as e:
            return False, f"خطأ في فحص الاستخدام: {e}"
    
    def get_recommended_strategy(self, query_context: Dict[str, Any]) -> Dict[str, Any]:
        """الحصول على الاستراتيجية الموصى بها"""
        try:
            strategy = {
                'use_local_search': True,
                'use_api_search': True,
                'enable_ai_enhancement': True,
                'enable_semantic_analysis': True,
                'cache_aggressively': True,
                'reasoning': []
            }
            
            # تطبيق قواعد التوفير
            from .api_usage_manager import api_usage_manager
            usage_report = api_usage_manager.get_usage_report()
            
            stats = {
                'daily_cost': usage_report['totals']['total_cost'],
                'api_usage_ratio': self._calculate_api_usage_ratio(usage_report),
                'quality_score': query_context.get('expected_quality', 1.0)
            }
            
            for rule_name, rule in self.conservation_rules.items():
                if rule['condition'](stats):
                    action = rule['action']
                    strategy = self._apply_conservation_action(strategy, action)
                    strategy['reasoning'].append(rule['description'])
            
            # تطبيق مستوى التوفير
            if self.config.conservation_level in [ConservationLevel.AGGRESSIVE, ConservationLevel.MAXIMUM]:
                strategy['use_local_search'] = True
                strategy['enable_ai_enhancement'] = False
                strategy['reasoning'].append(f"مستوى توفير: {self.config.conservation_level.value}")
            
            return strategy
            
        except Exception as e:
            # استراتيجية آمنة افتراضية
            return {
                'use_local_search': True,
                'use_api_search': False,
                'enable_ai_enhancement': False,
                'enable_semantic_analysis': False,
                'cache_aggressively': True,
                'reasoning': [f"خطأ في التحليل، استخدام استراتيجية آمنة: {e}"]
            }
    
    def _apply_conservation_action(self, strategy: Dict[str, Any], action: str) -> Dict[str, Any]:
        """تطبيق إجراء التوفير"""
        if action == 'reduce_api_usage':
            strategy['use_api_search'] = False
            strategy['enable_ai_enhancement'] = False
            
        elif action == 'switch_to_local':
            strategy['use_local_search'] = True
            strategy['use_api_search'] = False
            
        elif action == 'prefer_local_and_cache':
            strategy['use_local_search'] = True
            strategy['cache_aggressively'] = True
            strategy['enable_ai_enhancement'] = False
            
        elif action == 'reduce_non_essential':
            strategy['enable_ai_enhancement'] = False
            strategy['enable_semantic_analysis'] = False
        
        return strategy
    
    def _calculate_api_usage_ratio(self, usage_report: Dict[str, Any]) -> float:
        """حساب نسبة استخدام API"""
        try:
            total_used = 0
            total_limit = 0
            
            for provider_name, provider_data in usage_report['providers'].items():
                total_used += provider_data['requests_today']
                total_limit += provider_data['daily_limit']
            
            return total_used / total_limit if total_limit > 0 else 0
            
        except Exception:
            return 0
    
    def _is_weekend(self) -> bool:
        """فحص ما إذا كان اليوم عطلة نهاية أسبوع"""
        from datetime import datetime
        return datetime.now().weekday() >= 5  # السبت والأحد
    
    def record_conservation_event(self, event_type: str, details: Dict[str, Any]):
        """تسجيل حدث توفير"""
        try:
            event = {
                'timestamp': datetime.now().isoformat(),
                'type': event_type,
                'details': details
            }
            
            self.conservation_stats['conservation_events'].append(event)
            
            # تحديث الإحصائيات
            if event_type == 'api_call_saved':
                self.conservation_stats['api_calls_saved'] += 1
                self.conservation_stats['cost_saved'] += details.get('cost_saved', 0)
                
            elif event_type == 'local_search_used':
                self.conservation_stats['local_searches_used'] += 1
                
            elif event_type == 'cache_hit_gained':
                self.conservation_stats['cache_hits_gained'] += 1
            
            # الاحتفاظ بآخر 1000 حدث فقط
            if len(self.conservation_stats['conservation_events']) > 1000:
                self.conservation_stats['conservation_events'] = self.conservation_stats['conservation_events'][-1000:]
                
        except Exception as e:
            print(f"فشل في تسجيل حدث التوفير: {e}")
    
    def get_conservation_report(self) -> Dict[str, Any]:
        """تقرير التوفير"""
        try:
            from datetime import datetime, timedelta
            
            # حساب التوفير اليومي
            today = datetime.now().date()
            today_events = [
                event for event in self.conservation_stats['conservation_events']
                if datetime.fromisoformat(event['timestamp']).date() == today
            ]
            
            daily_savings = sum(
                event['details'].get('cost_saved', 0)
                for event in today_events
                if event['type'] == 'api_call_saved'
            )
            
            report = {
                'conservation_level': self.config.conservation_level.value,
                'daily_budget': self.config.daily_budget_usd,
                'total_savings': {
                    'api_calls_saved': self.conservation_stats['api_calls_saved'],
                    'cost_saved': self.conservation_stats['cost_saved'],
                    'daily_cost_saved': daily_savings
                },
                'usage_patterns': {
                    'local_searches_used': self.conservation_stats['local_searches_used'],
                    'cache_hits_gained': self.conservation_stats['cache_hits_gained']
                },
                'efficiency_metrics': {
                    'conservation_rate': self._calculate_conservation_rate(),
                    'cost_efficiency': self._calculate_cost_efficiency()
                },
                'recommendations': self._generate_conservation_recommendations()
            }
            
            return report
            
        except Exception as e:
            return {'error': f"فشل في إنشاء تقرير التوفير: {e}"}
    
    def _calculate_conservation_rate(self) -> float:
        """حساب معدل التوفير"""
        total_events = len(self.conservation_stats['conservation_events'])
        conservation_events = sum(
            1 for event in self.conservation_stats['conservation_events']
            if event['type'] in ['api_call_saved', 'local_search_used', 'cache_hit_gained']
        )
        
        return conservation_events / total_events if total_events > 0 else 0
    
    def _calculate_cost_efficiency(self) -> float:
        """حساب كفاءة التكلفة"""
        try:
            from .api_usage_manager import api_usage_manager
            usage_report = api_usage_manager.get_usage_report()
            
            actual_cost = usage_report['totals']['total_cost']
            potential_cost = actual_cost + self.conservation_stats['cost_saved']
            
            return (potential_cost - actual_cost) / potential_cost if potential_cost > 0 else 0
            
        except Exception:
            return 0
    
    def _generate_conservation_recommendations(self) -> List[str]:
        """إنشاء توصيات التوفير"""
        recommendations = []
        
        try:
            from .api_usage_manager import api_usage_manager
            usage_report = api_usage_manager.get_usage_report()
            daily_cost = usage_report['totals']['total_cost']
            
            if daily_cost > self.config.cost_warning_threshold:
                recommendations.append("التكلفة اليومية عالية - فعل مستوى التوفير القوي")
            
            if self.conservation_stats['cost_saved'] < 0.5:
                recommendations.append("التوفير منخفض - استخدم البحث المحلي أكثر")
            
            cache_hit_rate = usage_report['totals']['cache_hit_rate']
            if cache_hit_rate < 40:
                recommendations.append("معدل استخدام التخزين المؤقت منخفض - زد مدة الحفظ")
            
            if self._calculate_conservation_rate() < 0.3:
                recommendations.append("معدل التوفير منخفض - راجع الاستراتيجيات")
            
            if not recommendations:
                recommendations.append("التوفير يعمل بكفاءة جيدة")
                
        except Exception:
            recommendations.append("تعذر تحليل التوفير - راجع الإعدادات")
        
        return recommendations

# إنشاء مثيل عام
api_conservation_manager = APIConservationManager()

# دوال مساعدة
def should_use_expensive_api(api_name: str, priority: str = "normal") -> bool:
    """فحص ما إذا كان يجب استخدام API مكلف"""
    can_use, _ = api_conservation_manager.should_use_api(api_name, priority)
    return can_use

def get_conservation_strategy(context: Dict[str, Any]) -> Dict[str, Any]:
    """الحصول على استراتيجية التوفير"""
    return api_conservation_manager.get_recommended_strategy(context)

def record_cost_saving(amount: float, method: str):
    """تسجيل توفير في التكلفة"""
    api_conservation_manager.record_conservation_event('api_call_saved', {
        'cost_saved': amount,
        'method': method
    })
