# واجهة Search1API للبحث المتقدم
import asyncio
import aiohttp
import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .logger import logger
from .api_key_manager import Api<PERSON>eyManager
from config.settings import BotConfig

@dataclass
class Search1APIResult:
    """نتيجة بحث Search1API"""
    title: str
    url: str
    snippet: str
    published_date: Optional[str] = None
    source: Optional[str] = None
    rank: int = 0
    relevance_score: float = 0.0

class Search1APISearch:
    """واجهة Search1API للبحث المتقدم"""
    
    def __init__(self):
        self.api_keys = BotConfig.SEARCH1API_KEYS
        self.base_url = "https://api.search1api.com/search"
        self.enabled = len(self.api_keys) > 0
        
        # إعداد مدير المفاتيح
        if self.enabled:
            self.api_manager = ApiKeyManager(
                api_keys=self.api_keys,
                service_name="Search1API",
                auto_recovery_minutes=60,  # إعادة تفعيل بعد ساعة
                load_balancing=True
            )
        else:
            self.api_manager = None
            
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'daily_usage': {},
            'monthly_usage': {},
            'last_request_time': None
        }
        
        # حدود الاستخدام
        self.daily_limit_keyless = 20    # 20 طلب/يوم للخطة Keyless
        self.daily_limit_developer = 200  # 200 طلب/يوم للخطة Developer
        
        # تخزين مؤقت للنتائج
        self.cache = {}
        self.cache_duration = 1800  # 30 دقيقة
        
        if self.enabled:
            logger.info(f"🔍 تم تهيئة Search1API مع {len(self.api_keys)} مفتاح")
        else:
            logger.warning("⚠️ Search1API غير مفعل - لا توجد مفاتيح API")
            
    def _get_cache_key(self, query: str, **kwargs) -> str:
        """إنشاء مفتاح تخزين مؤقت"""
        cache_data = f"{query}_{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(cache_data.encode()).hexdigest()
        
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """فحص صحة التخزين المؤقت"""
        if not cache_entry:
            return False
        cache_time = cache_entry.get('timestamp', 0)
        return (time.time() - cache_time) < self.cache_duration
        
    def _get_daily_usage(self) -> int:
        """الحصول على الاستخدام اليومي"""
        today = datetime.now().strftime('%Y-%m-%d')
        return self.usage_stats['daily_usage'].get(today, 0)
        
    def _update_usage_stats(self, success: bool):
        """تحديث إحصائيات الاستخدام"""
        today = datetime.now().strftime('%Y-%m-%d')
        current_month = datetime.now().strftime('%Y-%m')
        
        self.usage_stats['total_requests'] += 1
        self.usage_stats['last_request_time'] = datetime.now().isoformat()
        
        if success:
            self.usage_stats['successful_requests'] += 1
        else:
            self.usage_stats['failed_requests'] += 1
            
        # تحديث الاستخدام اليومي
        if today not in self.usage_stats['daily_usage']:
            self.usage_stats['daily_usage'][today] = 0
        self.usage_stats['daily_usage'][today] += 1
        
        # تحديث الاستخدام الشهري
        if current_month not in self.usage_stats['monthly_usage']:
            self.usage_stats['monthly_usage'][current_month] = 0
        self.usage_stats['monthly_usage'][current_month] += 1
        
    def can_make_request(self) -> bool:
        """فحص إمكانية إجراء طلب جديد"""
        if not self.enabled:
            return False
            
        daily_usage = self._get_daily_usage()
        # استخدام الحد الأعلى (Developer plan) كافتراضي
        return daily_usage < self.daily_limit_developer
        
    async def search(self, query: str, max_results: int = 10, 
                    search_type: str = "web", **kwargs) -> List[Dict]:
        """البحث باستخدام Search1API"""
        
        if not self.enabled:
            logger.warning("⚠️ Search1API غير مفعل")
            return []
            
        if not self.can_make_request():
            logger.warning("⚠️ تجاوز الحد اليومي لـ Search1API")
            return []
            
        # فحص التخزين المؤقت
        cache_key = self._get_cache_key(query, max_results=max_results, search_type=search_type, **kwargs)
        if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
            logger.info("📋 استخدام النتائج المخزنة مؤقتاً لـ Search1API")
            return self.cache[cache_key]['results']
            
        try:
            # الحصول على مفتاح API
            api_key = self.api_manager.get_key()
            if not api_key:
                logger.error("❌ لا يوجد مفتاح Search1API متاح")
                return []
                
            # إعداد المعاملات
            params = {
                'query': query,
                'max_results': min(max_results, 50),  # حد أقصى 50 نتيجة
                'search_type': search_type,
                'language': 'en',
                'country': 'US',
                'safe_search': 'moderate'
            }
            
            # إضافة معاملات إضافية
            params.update(kwargs)
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'Gaming-News-Bot/1.0'
            }
            
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.base_url,
                    params=params,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    processing_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        results = self._process_results(data, query)
                        
                        # حفظ في التخزين المؤقت
                        self.cache[cache_key] = {
                            'results': results,
                            'timestamp': time.time()
                        }
                        
                        self._update_usage_stats(True)
                        logger.info(f"✅ Search1API: {len(results)} نتيجة في {processing_time:.2f}ث")
                        
                        return results
                        
                    elif response.status == 429:
                        logger.warning("⚠️ تجاوز حد الطلبات لـ Search1API")
                        self.api_manager.mark_key_as_failed(api_key, "Rate limit exceeded")
                        self._update_usage_stats(False)
                        return []
                        
                    elif response.status == 401:
                        logger.error("❌ مفتاح Search1API غير صحيح")
                        self.api_manager.mark_key_as_failed(api_key, "Invalid API key")
                        self._update_usage_stats(False)
                        return []
                        
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ في Search1API: {response.status} - {error_text}")
                        self._update_usage_stats(False)
                        return []
                        
        except asyncio.TimeoutError:
            logger.warning("⏰ انتهت مهلة Search1API")
            self._update_usage_stats(False)
            return []
            
        except Exception as e:
            logger.error(f"❌ خطأ في Search1API: {e}")
            self._update_usage_stats(False)
            return []
            
    def _process_results(self, data: Dict, query: str) -> List[Dict]:
        """معالجة نتائج Search1API"""
        results = []
        
        try:
            # Search1API قد يكون له هيكل مختلف، نحتاج للتكيف
            search_results = data.get('results', [])
            if not search_results and 'organic_results' in data:
                search_results = data['organic_results']
            elif not search_results and 'web' in data:
                search_results = data['web'].get('results', [])
                
            for i, result in enumerate(search_results):
                try:
                    processed_result = {
                        'title': result.get('title', ''),
                        'url': result.get('url', result.get('link', '')),
                        'snippet': result.get('snippet', result.get('description', '')),
                        'published_date': result.get('published_date', result.get('date')),
                        'source': result.get('source', result.get('domain')),
                        'rank': i + 1,
                        'relevance_score': self._calculate_relevance(result, query),
                        'search_engine': 'search1api'
                    }
                    
                    # فلترة النتائج الفارغة
                    if processed_result['title'] and processed_result['url']:
                        results.append(processed_result)
                        
                except Exception as e:
                    logger.debug(f"خطأ في معالجة نتيجة Search1API: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتائج Search1API: {e}")
            
        return results
        
    def _calculate_relevance(self, result: Dict, query: str) -> float:
        """حساب درجة الصلة"""
        try:
            score = 0.0
            query_lower = query.lower()
            
            # فحص العنوان
            title = result.get('title', '').lower()
            if query_lower in title:
                score += 0.4
                
            # فحص الوصف
            snippet = result.get('snippet', result.get('description', '')).lower()
            if query_lower in snippet:
                score += 0.3
                
            # فحص الرابط
            url = result.get('url', result.get('link', '')).lower()
            if any(word in url for word in query_lower.split()):
                score += 0.2
                
            # نقاط إضافية للمواقع الموثوقة
            trusted_domains = ['ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com', 'pcgamer.com']
            if any(domain in url for domain in trusted_domains):
                score += 0.1
                
            return min(score, 1.0)
            
        except Exception:
            return 0.5  # درجة افتراضية
            
    async def search_news(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث في الأخبار"""
        return await self.search(
            query=f"{query} gaming news",
            max_results=max_results,
            search_type="news",
            freshness="week"  # أخبار الأسبوع الماضي
        )
        
    async def search_gaming_content(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث المتخصص في محتوى الألعاب"""
        return await self.search(
            query=f"{query} video games gaming",
            max_results=max_results,
            search_type="web",
            site_filter="ign.com OR gamespot.com OR polygon.com OR kotaku.com"
        )
        
    def get_usage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        daily_usage = self._get_daily_usage()
        success_rate = 0
        
        if self.usage_stats['total_requests'] > 0:
            success_rate = (self.usage_stats['successful_requests'] / self.usage_stats['total_requests']) * 100
            
        return {
            'service_name': 'Search1API',
            'enabled': self.enabled,
            'api_keys_count': len(self.api_keys) if self.api_keys else 0,
            'daily_usage': daily_usage,
            'daily_limit': self.daily_limit_developer,
            'remaining_requests': max(0, self.daily_limit_developer - daily_usage),
            'total_requests': self.usage_stats['total_requests'],
            'successful_requests': self.usage_stats['successful_requests'],
            'failed_requests': self.usage_stats['failed_requests'],
            'success_rate': round(success_rate, 2),
            'cache_entries': len(self.cache),
            'last_request_time': self.usage_stats['last_request_time']
        }
        
    async def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال مع Search1API"""
        if not self.enabled:
            return {
                'success': False,
                'error': 'Search1API غير مفعل - لا توجد مفاتيح API'
            }
            
        try:
            results = await self.search("test gaming news", max_results=3)
            
            return {
                'success': len(results) > 0,
                'results_count': len(results),
                'message': f'تم العثور على {len(results)} نتيجة اختبار',
                'api_keys_available': len(self.api_keys),
                'daily_usage': self._get_daily_usage()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'فشل اختبار Search1API: {str(e)}'
            }


# إنشاء مثيل عام
search1api_search = Search1APISearch()
