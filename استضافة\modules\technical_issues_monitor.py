#!/usr/bin/env python3
"""
مراقب المشاكل التقنية
"""

import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List
from pathlib import Path

logger = logging.getLogger(__name__)

class TechnicalIssuesMonitor:
    """مراقب المشاكل التقنية"""
    
    def __init__(self):
        self.issues_log = []
        self.performance_metrics = {}
        self.last_check = datetime.now()
        
    def monitor_system_health(self) -> Dict:
        """مراقبة صحة النظام"""
        try:
            health_report = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'issues_detected': [],
                'performance_metrics': {},
                'recommendations': []
            }
            
            # 1. فحص استخدام الذاكرة
            memory_usage = self._check_memory_usage()
            health_report['performance_metrics']['memory'] = memory_usage
            
            if memory_usage > 80:
                health_report['issues_detected'].append('high_memory_usage')
                health_report['recommendations'].append('إعادة تشغيل النظام لتحرير الذاكرة')
            
            # 2. فحص الاتصال بالإنترنت
            internet_status = self._check_internet_connection()
            health_report['performance_metrics']['internet'] = internet_status
            
            if not internet_status:
                health_report['issues_detected'].append('no_internet_connection')
                health_report['recommendations'].append('فحص اتصال الإنترنت')
            
            # 3. فحص APIs
            api_status = self._check_apis_status()
            health_report['performance_metrics']['apis'] = api_status
            
            # 4. فحص مساحة القرص
            disk_usage = self._check_disk_space()
            health_report['performance_metrics']['disk'] = disk_usage
            
            if disk_usage > 90:
                health_report['issues_detected'].append('low_disk_space')
                health_report['recommendations'].append('تنظيف مساحة القرص')
            
            # تحديد الحالة العامة
            if health_report['issues_detected']:
                if len(health_report['issues_detected']) > 2:
                    health_report['overall_status'] = 'critical'
                else:
                    health_report['overall_status'] = 'warning'
            
            # حفظ التقرير
            self._save_health_report(health_report)
            
            return health_report
            
        except Exception as e:
            logger.error(f"❌ خطأ في مراقبة صحة النظام: {e}")
            return {'overall_status': 'error', 'error': str(e)}
    
    def _check_memory_usage(self) -> float:
        """فحص استخدام الذاكرة"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            # محاكاة إذا لم تكن psutil متوفرة
            return 45.0
        except Exception:
            return 50.0
    
    def _check_internet_connection(self) -> bool:
        """فحص الاتصال بالإنترنت"""
        try:
            import requests
            response = requests.get('https://www.google.com', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _check_apis_status(self) -> Dict:
        """فحص حالة APIs"""
        api_status = {}
        
        try:
            # فحص Gemini API
            from config.settings import google_api_manager
            if google_api_manager:
                api_status['gemini'] = 'available'
            else:
                api_status['gemini'] = 'unavailable'
        except:
            api_status['gemini'] = 'error'
        
        try:
            # فحص YouTube Transcript API
            from youtube_transcript_api import YouTubeTranscriptApi
            api_status['youtube_transcript'] = 'available'
        except:
            api_status['youtube_transcript'] = 'unavailable'
        
        return api_status
    
    def _check_disk_space(self) -> float:
        """فحص مساحة القرص"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            return (used / total) * 100
        except:
            return 50.0
    
    def _save_health_report(self, report: Dict):
        """حفظ تقرير الصحة"""
        try:
            reports_dir = Path("health_reports")
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"health_report_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.warning(f"⚠️ فشل في حفظ تقرير الصحة: {e}")
    
    def get_system_recommendations(self) -> List[str]:
        """الحصول على توصيات النظام"""
        recommendations = [
            "تشغيل مراقبة دورية للنظام",
            "تنظيف الملفات المؤقتة بانتظام",
            "مراقبة استخدام APIs لتجنب تجاوز الحدود",
            "إجراء نسخ احتياطية للبيانات المهمة",
            "تحديث المكتبات والتبعيات بانتظام"
        ]
        
        return recommendations

# إنشاء مثيل عام
technical_monitor = TechnicalIssuesMonitor()
