#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكامل نظام SEOwl مع الوكيل الحالي
SEOwl Integration with Current Agent
"""

import asyncio
import threading
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional

from modules.logger import logger
from modules.seowl_indexing_checker import seowl_checker
from modules.database import db


class SEOwlIntegration:
    """تكامل نظام SEOwl مع الوكيل"""
    
    def __init__(self):
        self.integration_active = False
        self.monitoring_thread = None
        self.last_publish_time = None
        self.publish_cooldown_hours = 3  # 3 ساعات انتظار
        
        # إحصائيات التكامل
        self.integration_stats = {
            'articles_checked': 0,
            'issues_found': 0,
            'issues_fixed': 0,
            'last_check': None,
            'integration_started': None
        }
    
    def start_integration(self):
        """بدء التكامل مع الوكيل"""
        try:
            if self.integration_active:
                logger.warning("⚠️ التكامل نشط بالفعل")
                return
            
            logger.info("🔗 بدء تكامل نظام SEOwl مع الوكيل...")
            
            # تعديل دالة النشر في الوكيل
            self._integrate_with_publisher()
            
            # بدء مراقبة الفحوصات المجدولة
            self._start_monitoring_thread()
            
            self.integration_active = True
            self.integration_stats['integration_started'] = datetime.now().isoformat()
            
            logger.info("✅ تم تفعيل تكامل SEOwl بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء التكامل: {e}")
            raise
    
    def stop_integration(self):
        """إيقاف التكامل"""
        try:
            self.integration_active = False
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            logger.info("⏹️ تم إيقاف تكامل SEOwl")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف التكامل: {e}")
    
    def _integrate_with_publisher(self):
        """تكامل مع نظام النشر"""
        try:
            from modules.publisher import PublisherManager
            
            # حفظ الدالة الأصلية
            original_publish = PublisherManager.publish_article
            
            def enhanced_publish_article(self, article: Dict) -> Optional[str]:
                """دالة نشر محسنة مع فحص SEOwl"""
                try:
                    # النشر العادي
                    result = original_publish(self, article)
                    
                    if result:
                        # تحديث وقت النشر الأخير
                        seowl_integration.last_publish_time = datetime.now()
                        
                        # جدولة فحص SEOwl بعد 3 ساعات
                        asyncio.create_task(
                            seowl_checker.schedule_article_check(article, result)
                        )
                        
                        logger.info(f"📅 تم جدولة فحص SEOwl للمقال: {result}")
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في النشر المحسن: {e}")
                    return original_publish(self, article)
            
            # استبدال الدالة
            PublisherManager.publish_article = enhanced_publish_article
            
            logger.info("✅ تم تكامل نظام النشر مع SEOwl")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تكامل النشر: {e}")
    
    def _start_monitoring_thread(self):
        """بدء خيط مراقبة الفحوصات"""
        try:
            def monitoring_loop():
                """حلقة مراقبة الفحوصات المجدولة"""
                while self.integration_active:
                    try:
                        # تشغيل الفحوصات المجدولة
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(seowl_checker.run_scheduled_checks())
                        loop.close()
                        
                        # تحديث الإحصائيات
                        self._update_integration_stats()
                        
                        # انتظار 10 دقائق قبل الفحص التالي
                        time.sleep(600)
                        
                    except Exception as e:
                        logger.error(f"❌ خطأ في حلقة المراقبة: {e}")
                        time.sleep(60)  # انتظار دقيقة عند الخطأ
            
            self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            logger.info("✅ تم بدء خيط مراقبة SEOwl")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء المراقبة: {e}")
    
    def _update_integration_stats(self):
        """تحديث إحصائيات التكامل"""
        try:
            seowl_stats = seowl_checker.get_stats()
            
            self.integration_stats.update({
                'articles_checked': seowl_stats.get('total_checks', 0),
                'issues_found': seowl_stats.get('issues_found', 0),
                'issues_fixed': seowl_stats.get('issues_fixed', 0),
                'last_check': seowl_stats.get('last_check')
            })
            
            # حفظ في قاعدة البيانات
            db.save_integration_stats(self.integration_stats)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث إحصائيات التكامل: {e}")
    
    def can_publish_now(self) -> bool:
        """فحص إمكانية النشر الآن (مع مراعاة فترة الانتظار)"""
        try:
            if not self.last_publish_time:
                return True
            
            time_since_last_publish = datetime.now() - self.last_publish_time
            cooldown_period = timedelta(hours=self.publish_cooldown_hours)
            
            return time_since_last_publish >= cooldown_period
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص إمكانية النشر: {e}")
            return True
    
    def get_time_until_next_publish(self) -> Optional[timedelta]:
        """الحصول على الوقت المتبقي حتى النشر التالي"""
        try:
            if not self.last_publish_time:
                return None
            
            time_since_last_publish = datetime.now() - self.last_publish_time
            cooldown_period = timedelta(hours=self.publish_cooldown_hours)
            
            if time_since_last_publish >= cooldown_period:
                return None
            
            return cooldown_period - time_since_last_publish
            
        except Exception as e:
            logger.error(f"❌ خطأ في حساب الوقت المتبقي: {e}")
            return None
    
    def get_integration_status(self) -> Dict:
        """الحصول على حالة التكامل"""
        try:
            seowl_stats = seowl_checker.get_stats()
            pending_checks = seowl_checker.get_pending_checks()
            
            time_until_next = self.get_time_until_next_publish()
            
            status = {
                'integration_active': self.integration_active,
                'can_publish_now': self.can_publish_now(),
                'time_until_next_publish': str(time_until_next) if time_until_next else None,
                'last_publish_time': self.last_publish_time.isoformat() if self.last_publish_time else None,
                'cooldown_hours': self.publish_cooldown_hours,
                'seowl_stats': seowl_stats,
                'pending_checks': len(pending_checks),
                'integration_stats': self.integration_stats
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على حالة التكامل: {e}")
            return {'integration_active': False, 'error': str(e)}
    
    def force_check_article(self, article_data: Dict, published_url: str) -> bool:
        """فرض فحص مقال فوراً (للاختبار)"""
        try:
            logger.info(f"🧪 فرض فحص فوري للمقال: {published_url}")
            
            # تشغيل الفحص فوراً
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                seowl_checker._check_article_indexing(published_url, article_data)
            )
            
            loop.close()
            
            if result['success']:
                logger.info(f"✅ تم فحص المقال بنجاح: {published_url}")
                
                # إصلاح المشاكل إذا وجدت
                if result.get('issues'):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    fixed_count = loop.run_until_complete(
                        seowl_checker._fix_article_issues(published_url, result['issues'])
                    )
                    
                    loop.close()
                    
                    logger.info(f"🔧 تم إصلاح {fixed_count} مشكلة")
                
                return True
            
            else:
                logger.error(f"❌ فشل فحص المقال: {result.get('error')}")
                return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في الفحص الفوري: {e}")
            return False
    
    def get_recent_checks(self, limit: int = 10) -> List[Dict]:
        """الحصول على الفحوصات الحديثة"""
        try:
            pending_checks = seowl_checker.get_pending_checks()
            
            # ترتيب حسب التاريخ
            sorted_checks = sorted(
                pending_checks,
                key=lambda x: x['created_at'],
                reverse=True
            )
            
            return sorted_checks[:limit]
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الفحوصات الحديثة: {e}")
            return []
    
    def get_issues_summary(self) -> Dict:
        """الحصول على ملخص المشاكل"""
        try:
            # الحصول على المشاكل من قاعدة البيانات
            seo_issues = db.get_pending_seo_improvements()
            performance_issues = db.get_pending_performance_improvements()
            link_issues = db.get_pending_link_fixes()
            
            summary = {
                'seo_issues': len(seo_issues),
                'performance_issues': len(performance_issues),
                'link_issues': len(link_issues),
                'total_issues': len(seo_issues) + len(performance_issues) + len(link_issues),
                'recent_seo_issues': seo_issues[:5],
                'recent_performance_issues': performance_issues[:5],
                'recent_link_issues': link_issues[:5]
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على ملخص المشاكل: {e}")
            return {'total_issues': 0, 'error': str(e)}


# إنشاء مثيل عام للتكامل
seowl_integration = SEOwlIntegration()
