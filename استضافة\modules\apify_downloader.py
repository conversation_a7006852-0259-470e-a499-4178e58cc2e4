# واجهة Apify YouTube Downloader API
import asyncio
import aiohttp
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from modules.logger import logger
from config.settings import BotConfig

@dataclass
class ApifyDownloadResult:
    """نتيجة تحميل Apify"""
    success: bool
    download_url: Optional[str]
    file_size: int
    duration_seconds: float
    format: str
    quality: str
    run_id: str
    processing_time: float
    error_message: Optional[str] = None
    cost_units: float = 0.0

class ApifyYouTubeDownloader:
    """واجهة Apify YouTube to MP3 Downloader"""
    
    def __init__(self):
        self.api_token = BotConfig.APIFY_API_TOKEN
        self.actor_id = BotConfig.APIFY_ACTOR_ID
        self.api_url = BotConfig.APIFY_API_URL
        self.monthly_credit = BotConfig.APIFY_FREE_CREDIT_MONTHLY
        self.cost_per_run = BotConfig.APIFY_COST_PER_RUN
        
        # تتبع الاستخدام
        self.usage_file = "cache/apify_usage.json"
        self.usage_data = self._load_usage_data()
        
        logger.info("🔵 تم تهيئة Apify YouTube Downloader")
        
    def _load_usage_data(self) -> Dict[str, Any]:
        """تحميل بيانات الاستخدام"""
        try:
            if os.path.exists(self.usage_file):
                with open(self.usage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل بيانات استخدام Apify: {e}")
        
        return {
            "monthly_usage": {},
            "total_runs": 0,
            "successful_runs": 0,
            "failed_runs": 0
        }
        
    def _save_usage_data(self):
        """حفظ بيانات الاستخدام"""
        try:
            os.makedirs(os.path.dirname(self.usage_file), exist_ok=True)
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ بيانات استخدام Apify: {e}")
            
    def _get_monthly_usage(self) -> float:
        """الحصول على الاستخدام الشهري"""
        current_month = datetime.now().strftime("%Y-%m")
        return self.usage_data["monthly_usage"].get(current_month, 0.0)
        
    def _update_usage(self, cost: float):
        """تحديث الاستخدام"""
        current_month = datetime.now().strftime("%Y-%m")
        
        if current_month not in self.usage_data["monthly_usage"]:
            self.usage_data["monthly_usage"][current_month] = 0.0
            
        self.usage_data["monthly_usage"][current_month] += cost
        self.usage_data["total_runs"] += 1
        
        self._save_usage_data()
        
    def is_available(self) -> bool:
        """فحص ما إذا كانت الخدمة متاحة"""
        if not self.api_token:
            return False
            
        monthly_usage = self._get_monthly_usage()
        remaining_credit = self.monthly_credit - monthly_usage
        
        return remaining_credit >= self.cost_per_run
        
    def get_remaining_credit(self) -> float:
        """الحصول على الرصيد المتبقي"""
        monthly_usage = self._get_monthly_usage()
        return max(0.0, self.monthly_credit - monthly_usage)
        
    async def download_audio(self, video_url: str, video_id: str = "", 
                           quality: str = "medium", format: str = "mp3") -> ApifyDownloadResult:
        """تحميل الصوت من YouTube باستخدام Apify"""
        
        start_time = time.time()
        
        if not self.is_available():
            return ApifyDownloadResult(
                success=False, download_url=None, file_size=0,
                duration_seconds=0.0, format=format, quality=quality,
                run_id="", processing_time=0.0,
                error_message="تجاوز الحد الشهري لـ Apify أو لا يوجد API token"
            )
            
        try:
            # إعداد معاملات التحميل
            actor_input = self._prepare_actor_input(video_url, video_id, quality, format)
            
            # بدء تشغيل الـ Actor
            run_id = await self._start_actor_run(actor_input)
            if not run_id:
                return ApifyDownloadResult(
                    success=False, download_url=None, file_size=0,
                    duration_seconds=0.0, format=format, quality=quality,
                    run_id="", processing_time=time.time() - start_time,
                    error_message="فشل في بدء تشغيل Apify Actor"
                )
                
            # انتظار اكتمال التشغيل
            result = await self._wait_for_completion(run_id, format, quality)
            result.processing_time = time.time() - start_time
            
            # تحديث الاستخدام
            if result.success:
                self.usage_data["successful_runs"] += 1
                self._update_usage(self.cost_per_run)
            else:
                self.usage_data["failed_runs"] += 1
                self._save_usage_data()
                
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في Apify downloader: {e}")
            return ApifyDownloadResult(
                success=False, download_url=None, file_size=0,
                duration_seconds=0.0, format=format, quality=quality,
                run_id="", processing_time=time.time() - start_time,
                error_message=f"خطأ في Apify: {str(e)}"
            )
            
    def _prepare_actor_input(self, video_url: str, video_id: str, 
                           quality: str, format: str) -> Dict[str, Any]:
        """إعداد معاملات الـ Actor"""
        
        # تحديد جودة الصوت
        quality_map = {
            "high": "192",
            "medium": "128", 
            "low": "64"
        }
        
        audio_quality = quality_map.get(quality, "128")
        
        return {
            "startUrls": [{"url": video_url}],
            "format": format,
            "quality": audio_quality,
            "onlyAudio": True,
            "filenameTemplate": f"{video_id or 'audio'}.%(ext)s",
            "downloadMode": "url",  # إرجاع رابط التحميل فقط
            "maxItems": 1,
            "proxyConfiguration": {"useApifyProxy": True}
        }
        
    async def _start_actor_run(self, actor_input: Dict[str, Any]) -> Optional[str]:
        """بدء تشغيل الـ Actor"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            }
            
            run_url = f"{self.api_url}/acts/{self.actor_id}/runs"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    run_url,
                    headers=headers,
                    json=actor_input,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 201:
                        run_data = await response.json()
                        run_id = run_data["data"]["id"]
                        logger.info(f"🔵 بدء تشغيل Apify Actor: {run_id}")
                        return run_id
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ فشل بدء Apify Actor: {error_text}")
                        return None
                        
        except Exception as e:
            logger.error(f"❌ خطأ في بدء Apify Actor: {e}")
            return None
            
    async def _wait_for_completion(self, run_id: str, format: str, quality: str) -> ApifyDownloadResult:
        """انتظار اكتمال التشغيل"""
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
        max_wait_time = 300  # 5 دقائق
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            while time.time() - start_time < max_wait_time:
                try:
                    # فحص حالة التشغيل
                    status_url = f"{self.api_url}/actor-runs/{run_id}"
                    
                    async with session.get(
                        status_url,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        
                        if response.status == 200:
                            status_data = await response.json()
                            status = status_data["data"]["status"]
                            
                            if status == "SUCCEEDED":
                                # الحصول على النتائج
                                return await self._get_download_results(session, headers, run_id, format, quality)
                            elif status == "FAILED":
                                error_msg = status_data["data"].get("statusMessage", "فشل غير محدد")
                                return ApifyDownloadResult(
                                    success=False, download_url=None, file_size=0,
                                    duration_seconds=0.0, format=format, quality=quality,
                                    run_id=run_id, processing_time=0.0,
                                    error_message=f"فشل Apify Actor: {error_msg}"
                                )
                            elif status == "RUNNING":
                                logger.debug(f"🔄 Apify Actor قيد التشغيل: {run_id}")
                            
                except Exception as e:
                    logger.warning(f"⚠️ خطأ في فحص حالة Apify: {e}")
                
                await asyncio.sleep(10)  # انتظار 10 ثوان
                
        return ApifyDownloadResult(
            success=False, download_url=None, file_size=0,
            duration_seconds=0.0, format=format, quality=quality,
            run_id=run_id, processing_time=0.0,
            error_message="انتهت مهلة انتظار Apify Actor"
        )
        
    async def _get_download_results(self, session: aiohttp.ClientSession, headers: Dict,
                                  run_id: str, format: str, quality: str) -> ApifyDownloadResult:
        """الحصول على نتائج التحميل"""
        try:
            results_url = f"{self.api_url}/actor-runs/{run_id}/dataset/items"
            
            async with session.get(
                results_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    results = await response.json()
                    
                    if results and len(results) > 0:
                        result = results[0]  # أول نتيجة
                        
                        download_url = result.get("downloadUrl") or result.get("url")
                        file_size = result.get("fileSize", 0)
                        duration = result.get("duration", 0.0)
                        
                        if download_url:
                            return ApifyDownloadResult(
                                success=True, download_url=download_url, file_size=file_size,
                                duration_seconds=duration, format=format, quality=quality,
                                run_id=run_id, processing_time=0.0,
                                cost_units=self.cost_per_run
                            )
                        else:
                            return ApifyDownloadResult(
                                success=False, download_url=None, file_size=0,
                                duration_seconds=0.0, format=format, quality=quality,
                                run_id=run_id, processing_time=0.0,
                                error_message="لم يتم العثور على رابط التحميل في النتائج"
                            )
                    else:
                        return ApifyDownloadResult(
                            success=False, download_url=None, file_size=0,
                            duration_seconds=0.0, format=format, quality=quality,
                            run_id=run_id, processing_time=0.0,
                            error_message="لا توجد نتائج من Apify Actor"
                        )
                else:
                    error_text = await response.text()
                    return ApifyDownloadResult(
                        success=False, download_url=None, file_size=0,
                        duration_seconds=0.0, format=format, quality=quality,
                        run_id=run_id, processing_time=0.0,
                        error_message=f"فشل الحصول على النتائج: {error_text}"
                    )
                    
        except Exception as e:
            return ApifyDownloadResult(
                success=False, download_url=None, file_size=0,
                duration_seconds=0.0, format=format, quality=quality,
                run_id=run_id, processing_time=0.0,
                error_message=f"خطأ في الحصول على النتائج: {str(e)}"
            )
            
    def get_usage_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        monthly_usage = self._get_monthly_usage()
        remaining_credit = self.get_remaining_credit()
        
        return {
            "service_name": "Apify YouTube Downloader",
            "monthly_credit": self.monthly_credit,
            "monthly_usage": monthly_usage,
            "remaining_credit": remaining_credit,
            "usage_percentage": (monthly_usage / self.monthly_credit) * 100 if self.monthly_credit > 0 else 0,
            "cost_per_run": self.cost_per_run,
            "total_runs": self.usage_data["total_runs"],
            "successful_runs": self.usage_data["successful_runs"],
            "failed_runs": self.usage_data["failed_runs"],
            "success_rate": (self.usage_data["successful_runs"] / max(1, self.usage_data["total_runs"])) * 100,
            "is_available": self.is_available()
        }


# إنشاء مثيل عام
apify_downloader = ApifyYouTubeDownloader()
