# معالج تنظيف وتحسين النصوص القصيرة والرموز الغريبة
import re
import unicodedata
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from .logger import logger

class TextCleanupProcessor:
    """معالج متخصص لتنظيف وتحسين النصوص القصيرة والرموز الغريبة من Whisper"""
    
    def __init__(self):
        # أنماط الرموز الغريبة الشائعة
        self.weird_patterns = {
            'single_chars': r'\b[a-zA-Zأ-ي]\b',  # حروف منفردة
            'repeated_chars': r'(.)\1{3,}',  # تكرار حرف أكثر من 3 مرات
            'mixed_scripts': r'[a-zA-Z]+[أ-ي]+[a-zA-Z]+',  # خلط عشوائي للغات
            'number_sequences': r'\b\d+\s\d+\s\d+\b',  # أرقام متتالية
            'punctuation_spam': r'[.!?]{3,}',  # علامات ترقيم مفرطة
            'unicode_artifacts': r'[\u200b-\u200f\u202a-\u202e\u2060-\u206f]',  # رموز Unicode غير مرئية
            'broken_words': r'\b\w{1,2}\s\w{1,2}\s\w{1,2}\b',  # كلمات مكسورة
        }
        
        # قاموس الإصلاحات الشائعة
        self.common_fixes = {
            'ar': {
                'ا ل ل ع ب ة': 'اللعبة',
                'م ي ن ك ر ا ف ت': 'ماين كرافت',
                'ف و ر ت ن ا ي ت': 'فورتنايت',
                'ب ل ا ي س ت ي ش ن': 'بلايستيشن',
                'إ ك س ب و ك س': 'إكسبوكس',
            },
            'en': {
                'm i n e c r a f t': 'minecraft',
                'f o r t n i t e': 'fortnite',
                'p l a y s t a t i o n': 'playstation',
                'x b o x': 'xbox',
                'g a m e': 'game',
            }
        }
        
        # كلمات مفتاحية للسياق
        self.context_keywords = {
            'gaming': ['game', 'play', 'level', 'score', 'win', 'lose', 'لعبة', 'لعب', 'مستوى', 'نقاط'],
            'technology': ['tech', 'digital', 'app', 'software', 'تقنية', 'رقمي', 'تطبيق', 'برنامج'],
            'entertainment': ['fun', 'enjoy', 'watch', 'video', 'متعة', 'مشاهدة', 'فيديو', 'ترفيه']
        }
    
    async def process_problematic_text(self, text: str, video_data: Dict = None) -> Dict:
        """معالجة شاملة للنصوص المشكوك فيها"""
        try:
            logger.info("🔧 بدء معالجة النص المشكوك فيه...")
            
            if not text or not text.strip():
                return self._create_processing_result(text, "نص فارغ", False)
            
            original_text = text
            processing_steps = []
            
            # الخطوة 1: تنظيف أساسي
            cleaned_text, basic_steps = self._basic_cleanup(text)
            processing_steps.extend(basic_steps)
            
            # الخطوة 2: إصلاح الرموز الغريبة
            fixed_text, symbol_steps = self._fix_weird_symbols(cleaned_text)
            processing_steps.extend(symbol_steps)
            
            # الخطوة 3: إصلاح الكلمات المكسورة
            reconstructed_text, word_steps = self._reconstruct_broken_words(fixed_text)
            processing_steps.extend(word_steps)
            
            # الخطوة 4: تحسين النص القصير
            enhanced_text, enhancement_steps = await self._enhance_short_text(reconstructed_text, video_data)
            processing_steps.extend(enhancement_steps)
            
            # الخطوة 5: التحقق من الجودة النهائية
            quality_check = self._final_quality_check(enhanced_text, original_text)
            
            # تحديد ما إذا كان النص قابل للاستخدام
            is_usable = self._determine_usability(enhanced_text, quality_check)
            
            result = self._create_processing_result(
                enhanced_text, 
                "معالجة مكتملة", 
                is_usable,
                {
                    'original_text': original_text,
                    'processing_steps': processing_steps,
                    'quality_check': quality_check,
                    'improvement_ratio': len(enhanced_text) / len(original_text) if original_text else 0
                }
            )
            
            logger.info(f"✅ اكتملت معالجة النص - قابل للاستخدام: {is_usable}")
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة النص: {e}")
            return self._create_processing_result(text, f"خطأ: {str(e)}", False)
    
    def _basic_cleanup(self, text: str) -> Tuple[str, List[str]]:
        """تنظيف أساسي للنص"""
        steps = []
        cleaned = text
        
        try:
            # إزالة الرموز غير المرئية
            original_len = len(cleaned)
            cleaned = re.sub(self.weird_patterns['unicode_artifacts'], '', cleaned)
            if len(cleaned) != original_len:
                steps.append("إزالة رموز Unicode غير مرئية")
            
            # تنظيف المساحات الزائدة
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            steps.append("تنظيف المساحات")
            
            # إزالة علامات الترقيم المفرطة
            original_len = len(cleaned)
            cleaned = re.sub(self.weird_patterns['punctuation_spam'], '.', cleaned)
            if len(cleaned) != original_len:
                steps.append("إصلاح علامات الترقيم المفرطة")
            
            # تطبيع Unicode
            cleaned = unicodedata.normalize('NFKC', cleaned)
            steps.append("تطبيع Unicode")
            
            return cleaned, steps
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في التنظيف الأساسي: {e}")
            return text, ["فشل التنظيف الأساسي"]
    
    def _fix_weird_symbols(self, text: str) -> Tuple[str, List[str]]:
        """إصلاح الرموز والأنماط الغريبة"""
        steps = []
        fixed = text
        
        try:
            # إصلاح التكرار المفرط للأحرف
            original_len = len(fixed)
            fixed = re.sub(self.weird_patterns['repeated_chars'], r'\1\1', fixed)
            if len(fixed) != original_len:
                steps.append("إصلاح التكرار المفرط للأحرف")
            
            # إزالة الحروف المنفردة غير المفيدة
            words = fixed.split()
            meaningful_words = []
            removed_count = 0
            
            for word in words:
                if len(word) == 1 and word.lower() not in ['a', 'i', 'و', 'أ', 'في']:
                    removed_count += 1
                else:
                    meaningful_words.append(word)
            
            if removed_count > 0:
                fixed = ' '.join(meaningful_words)
                steps.append(f"إزالة {removed_count} حرف منفرد غير مفيد")
            
            # إصلاح الأرقام المتتالية العشوائية
            original_len = len(fixed)
            fixed = re.sub(self.weird_patterns['number_sequences'], '', fixed)
            if len(fixed) != original_len:
                steps.append("إزالة أرقام عشوائية متتالية")
            
            return fixed.strip(), steps
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إصلاح الرموز: {e}")
            return text, ["فشل إصلاح الرموز"]
    
    def _reconstruct_broken_words(self, text: str) -> Tuple[str, List[str]]:
        """إعادة بناء الكلمات المكسورة"""
        steps = []
        reconstructed = text
        
        try:
            # تحديد اللغة الرئيسية
            language = self._detect_language(text)
            
            # تطبيق الإصلاحات الشائعة
            fixes_applied = 0
            for broken_word, correct_word in self.common_fixes.get(language, {}).items():
                if broken_word in reconstructed:
                    reconstructed = reconstructed.replace(broken_word, correct_word)
                    fixes_applied += 1
            
            if fixes_applied > 0:
                steps.append(f"إصلاح {fixes_applied} كلمة مكسورة شائعة")
            
            # محاولة إعادة بناء الكلمات المكسورة تلقائياً
            words = reconstructed.split()
            rebuilt_words = []
            i = 0
            
            while i < len(words):
                current_word = words[i]
                
                # إذا كانت الكلمة قصيرة جداً، حاول دمجها مع التالية
                if len(current_word) <= 2 and i + 1 < len(words):
                    next_word = words[i + 1]
                    if len(next_word) <= 2:
                        # دمج كلمتين قصيرتين
                        combined = current_word + next_word
                        if self._is_meaningful_word(combined):
                            rebuilt_words.append(combined)
                            i += 2
                            continue
                
                rebuilt_words.append(current_word)
                i += 1
            
            if len(rebuilt_words) != len(words):
                reconstructed = ' '.join(rebuilt_words)
                steps.append("إعادة بناء كلمات مكسورة")
            
            return reconstructed, steps
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إعادة بناء الكلمات: {e}")
            return text, ["فشل إعادة بناء الكلمات"]
    
    async def _enhance_short_text(self, text: str, video_data: Dict = None) -> Tuple[str, List[str]]:
        """تحسين النص القصير باستخدام السياق"""
        steps = []
        enhanced = text
        
        try:
            # إذا كان النص قصير جداً، حاول إضافة سياق
            if len(text.split()) < 10 and video_data:
                context_added = await self._add_context_from_video(text, video_data)
                if context_added != text:
                    enhanced = context_added
                    steps.append("إضافة سياق من بيانات الفيديو")
            
            # تحسين النص بناءً على الكلمات المفتاحية
            improved_text = self._improve_with_keywords(enhanced)
            if improved_text != enhanced:
                enhanced = improved_text
                steps.append("تحسين باستخدام الكلمات المفتاحية")
            
            return enhanced, steps
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحسين النص القصير: {e}")
            return text, ["فشل تحسين النص القصير"]
    
    async def _add_context_from_video(self, text: str, video_data: Dict) -> str:
        """إضافة سياق من بيانات الفيديو"""
        try:
            title = video_data.get('title', '')
            description = video_data.get('description', '')
            
            # إذا كان النص قصير جداً، أضف معلومات من العنوان والوصف
            if len(text.split()) < 5:
                enhanced_text = f"من فيديو '{title}': {text}"
                
                # أضف جزء من الوصف إذا كان متاحاً
                if description and len(description) > 50:
                    desc_snippet = description[:100]
                    enhanced_text += f"\n\nمن وصف الفيديو: {desc_snippet}"
                
                return enhanced_text
            
            return text
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في إضافة السياق: {e}")
            return text
    
    def _improve_with_keywords(self, text: str) -> str:
        """تحسين النص باستخدام الكلمات المفتاحية"""
        try:
            # تحديد السياق الرئيسي
            text_lower = text.lower()
            context_scores = {}
            
            for context, keywords in self.context_keywords.items():
                score = sum(1 for keyword in keywords if keyword in text_lower)
                context_scores[context] = score
            
            # إذا كان هناك سياق واضح، أضف معلومات ذات صلة
            main_context = max(context_scores, key=context_scores.get)
            
            if context_scores[main_context] > 0:
                if main_context == 'gaming' and 'لعبة' not in text and 'game' not in text:
                    text = f"محتوى متعلق بالألعاب: {text}"
                elif main_context == 'technology' and 'تقنية' not in text and 'tech' not in text:
                    text = f"محتوى تقني: {text}"
            
            return text
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحسين الكلمات المفتاحية: {e}")
            return text
    
    def _final_quality_check(self, processed_text: str, original_text: str) -> Dict:
        """فحص الجودة النهائي"""
        try:
            return {
                'length_improvement': len(processed_text) - len(original_text),
                'word_count_improvement': len(processed_text.split()) - len(original_text.split()),
                'has_meaningful_content': len(processed_text.split()) >= 3,
                'contains_gaming_keywords': any(
                    keyword in processed_text.lower() 
                    for keywords in self.context_keywords.values() 
                    for keyword in keywords
                ),
                'readability_score': self._calculate_readability(processed_text)
            }
        except Exception as e:
            logger.warning(f"⚠️ خطأ في فحص الجودة: {e}")
            return {'error': str(e)}
    
    def _determine_usability(self, text: str, quality_check: Dict) -> bool:
        """تحديد ما إذا كان النص قابل للاستخدام"""
        try:
            # معايير القبول
            min_words = 3
            min_readability = 30
            
            word_count = len(text.split())
            readability = quality_check.get('readability_score', 0)
            has_content = quality_check.get('has_meaningful_content', False)
            
            return (
                word_count >= min_words and
                readability >= min_readability and
                has_content
            )
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحديد القابلية للاستخدام: {e}")
            return False
    
    def _detect_language(self, text: str) -> str:
        """تحديد لغة النص"""
        arabic_chars = len(re.findall(r'[\u0600-\u06FF]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        return 'ar' if arabic_chars > english_chars else 'en'
    
    def _is_meaningful_word(self, word: str) -> bool:
        """فحص ما إذا كانت الكلمة ذات معنى"""
        # فحص بسيط للكلمات ذات المعنى
        if len(word) < 3:
            return False
        
        # فحص وجود نمط منطقي للأحرف
        vowels = 'aeiouاةيوأإآ'
        consonants = 'bcdfghjklmnpqrstvwxyzبتثجحخدذرزسشصضطظعغفقكلمنهو'
        
        vowel_count = sum(1 for char in word.lower() if char in vowels)
        consonant_count = sum(1 for char in word.lower() if char in consonants)
        
        # كلمة منطقية تحتوي على مزيج من الحروف الصوتية والساكنة
        return vowel_count > 0 and consonant_count > 0
    
    def _calculate_readability(self, text: str) -> float:
        """حساب قابلية القراءة"""
        try:
            words = text.split()
            if not words:
                return 0
            
            # حساب بسيط لقابلية القراءة
            avg_word_length = sum(len(word) for word in words) / len(words)
            sentence_count = len(re.split(r'[.!?؟]', text))
            
            # نقاط بناءً على طول الكلمات وعدد الجمل
            readability = 100 - (avg_word_length * 5) - (len(words) / sentence_count * 2)
            
            return max(min(readability, 100), 0)
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حساب قابلية القراءة: {e}")
            return 50
    
    def _create_processing_result(self, text: str, status: str, is_usable: bool, details: Dict = None) -> Dict:
        """إنشاء نتيجة المعالجة"""
        return {
            'processed_text': text,
            'status': status,
            'is_usable': is_usable,
            'processing_timestamp': datetime.now().isoformat(),
            'details': details or {},
            'text_length': len(text),
            'word_count': len(text.split()) if text else 0
        }

# إنشاء مثيل عام
text_cleanup_processor = TextCleanupProcessor()
