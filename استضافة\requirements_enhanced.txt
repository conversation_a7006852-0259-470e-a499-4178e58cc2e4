# متطلبات الأنظمة المحسنة لوكيل أخبار الألعاب

# الأنظمة الأساسية (موجودة مسبقاً)
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0
python-telegram-bot>=20.0
google-api-python-client>=2.70.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.8.0
openai>=1.0.0
python-dotenv>=0.19.0
schedule>=1.1.0
asyncio>=3.4.3
aiohttp>=3.8.0
feedparser>=6.0.0
newspaper3k>=0.2.8
nltk>=3.7
textblob>=0.17.1
googletrans==4.0.0rc1
Pillow>=9.0.0
matplotlib>=3.5.0
seaborn>=0.11.0
pandas>=1.4.0
numpy>=1.21.0
scikit-learn>=1.1.0
wordcloud>=1.8.0
arabic-reshaper>=2.1.0
python-bidi>=0.4.2

# الأنظمة المحسنة الجديدة

# نظام RAG المتقدم
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
networkx>=2.8.0
transformers>=4.21.0
torch>=1.12.0
pinecone-client>=2.2.0

# نظام التحليل متعدد الوسائط
opencv-python>=4.6.0
pytesseract>=0.3.10
easyocr>=1.6.0
moviepy>=1.0.3
speechrecognition>=3.8.1
pydub>=0.25.1

# نماذج الذكاء الاصطناعي للرؤية
torch>=1.12.0
torchvision>=0.13.0
timm>=0.6.0

# معالجة الصور المتقدمة
opencv-contrib-python>=4.6.0
scikit-image>=0.19.0
imageio>=2.19.0

# معالجة الصوت
librosa>=0.9.0
soundfile>=0.10.0

# قواعد البيانات المتجهة
chromadb>=0.3.0
weaviate-client>=3.15.0

# أدوات التحليل والإحصائيات
plotly>=5.10.0
dash>=2.6.0
streamlit>=1.12.0

# أدوات التطوير والاختبار
pytest>=7.1.0
pytest-asyncio>=0.19.0
black>=22.6.0
flake8>=5.0.0

# مكتبات إضافية للأداء
numba>=0.56.0
joblib>=1.1.0
tqdm>=4.64.0

# أمان وتشفير
cryptography>=37.0.0
bcrypt>=3.2.0

# تحسين الأداء
uvloop>=0.16.0  # للأنظمة Unix فقط
psutil>=5.9.0

# ملاحظات التثبيت:
# 
# 1. للحصول على أفضل أداء، قم بتثبيت PyTorch مع دعم CUDA:
#    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
#
# 2. لتثبيت Tesseract OCR:
#    - Windows: تحميل من https://github.com/UB-Mannheim/tesseract/wiki
#    - Ubuntu: sudo apt install tesseract-ocr tesseract-ocr-ara
#    - macOS: brew install tesseract tesseract-lang
#
# 3. لتثبيت FFmpeg (مطلوب لـ moviepy):
#    - Windows: تحميل من https://ffmpeg.org/download.html
#    - Ubuntu: sudo apt install ffmpeg
#    - macOS: brew install ffmpeg
#
# 4. للحصول على مفاتيح API:
#    - Pinecone: https://www.pinecone.io/
#    - OpenAI: https://platform.openai.com/
#    - Google Cloud: https://console.cloud.google.com/
#
# 5. المتطلبات الاختيارية (للأداء المحسن):
#    - NVIDIA GPU مع CUDA للمعالجة السريعة
#    - 8GB+ RAM للنماذج الكبيرة
#    - SSD للتخزين السريع

# تثبيت سريع للأنظمة المحسنة فقط:
# pip install sentence-transformers faiss-cpu networkx opencv-python pytesseract easyocr

# تثبيت كامل:
# pip install -r requirements_enhanced.txt
