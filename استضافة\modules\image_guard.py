# ImageGuard Pro - نظام البحث عن الصور الآمنة والقانونية + إنشاء الصور بالذكاء الاصطناعي
import aiohttp
import asyncio
import random
import re
import urllib.parse
import base64
import hashlib
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json
from .logger import logger
from config.settings import BotConfig

class ImageGuard:
    """نظام البحث عن الصور الآمنة والقانونية للوكيل البرمجي"""
    
    def __init__(self):
        # مفاتيح APIs (سيتم إضافتها للإعدادات)
        self.pexels_api_key = getattr(BotConfig, 'PEXELS_API_KEY', '')
        self.pixabay_api_key = getattr(BotConfig, 'PIXABAY_API_KEY', '')
        self.unsplash_access_key = getattr(BotConfig, 'UNSPLASH_ACCESS_KEY', '')
        
        # إعدادات الأمان
        self.safe_keywords = [
            'gaming', 'controller', 'console', 'computer', 'technology',
            'esports', 'digital', 'modern', 'setup', 'workspace'
        ]
        
        # كلمات محظورة لضمان الامتثال لـ AdSense
        self.forbidden_keywords = [
            'violence', 'blood', 'weapon', 'gun', 'fight', 'war',
            'alcohol', 'beer', 'wine', 'cigarette', 'smoking',
            'gambling', 'casino', 'poker', 'adult', 'sexy'
        ]
        
        # صور احتياطية آمنة (URLs ثابتة لصور مجانية)
        self.fallback_images = [
            {
                'url': 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg',
                'description': 'Gaming controller on dark background',
                'license': 'Pexels License',
                'attribution': 'Photo by Lucie Liz from Pexels'
            },
            {
                'url': 'https://images.pexels.com/photos/1174746/pexels-photo-1174746.jpeg',
                'description': 'Modern gaming setup',
                'license': 'Pexels License', 
                'attribution': 'Photo by FOX from Pexels'
            },
            {
                'url': 'https://images.pexels.com/photos/735911/pexels-photo-735911.jpeg',
                'description': 'Retro gaming console',
                'license': 'Pexels License',
                'attribution': 'Photo by Garrett Morrow from Pexels'
            }
        ]
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'pexels_calls': 0,
            'pixabay_calls': 0,
            'unsplash_calls': 0,
            'fallback_used': 0,
            'successful_searches': 0,
            'failed_searches': 0
        }

class AIImageGenerator:
    """مولد الصور بالذكاء الاصطناعي للمقالات الاحترافية"""

    def __init__(self):
        # مفاتيح APIs لإنشاء الصور
        self.freepik_api_key = getattr(BotConfig, 'FREEPIK_API_KEY', 'FPSX1ee910637a8ec349e6d8c7f17a57740b')
        self.fluxai_api_key = getattr(BotConfig, 'FLUXAI_API_KEY', 'b6863038ac459a1f8cd9e30d82cdd989')

        # إعدادات إنشاء الصور
        self.image_styles = {
            'gaming_news': {
                'style': 'modern digital art, gaming aesthetic, vibrant colors',
                'quality': 'high quality, 4k, professional',
                'mood': 'exciting, dynamic, futuristic'
            },
            'game_review': {
                'style': 'cinematic, game screenshot style, detailed',
                'quality': 'ultra high quality, photorealistic',
                'mood': 'immersive, atmospheric'
            },
            'gaming_tips': {
                'style': 'infographic style, clean design, educational',
                'quality': 'clear, professional, minimalist',
                'mood': 'helpful, informative, friendly'
            },
            'esports': {
                'style': 'competitive gaming, arena lighting, dramatic',
                'quality': 'high energy, dynamic composition',
                'mood': 'intense, competitive, professional'
            }
        }

        # قوالب الـ prompts المحسنة للألعاب
        self.prompt_templates = {
            'game_character': "A {character_description} from {game_name}, {style_description}, {quality_settings}",
            'game_scene': "A {scene_description} from {game_name} showing {action_description}, {style_description}, {quality_settings}",
            'gaming_setup': "A professional gaming setup with {equipment_description}, {style_description}, {quality_settings}",
            'game_logo': "A modern logo design for {game_name} featuring {design_elements}, {style_description}, {quality_settings}",
            'gaming_concept': "A concept art showing {concept_description} related to {game_topic}, {style_description}, {quality_settings}"
        }

        # إحصائيات إنشاء الصور
        self.generation_stats = {
            'freepik_generations': 0,
            'fluxai_generations': 0,
            'successful_generations': 0,
            'failed_generations': 0,
            'total_images_created': 0
        }

        # تخزين مؤقت للصور المولدة
        self.image_cache = {}

    async def generate_article_images(self, article_data: Dict, num_images: int = 3) -> List[Dict]:
        """إنشاء صور احترافية للمقال"""
        try:
            logger.info(f"🎨 بدء إنشاء {num_images} صورة احترافية للمقال: {article_data.get('title', '')[:50]}...")

            generated_images = []
            article_title = article_data.get('title', '')
            article_content = article_data.get('content', '')
            article_keywords = article_data.get('keywords', [])

            # تحليل المقال لاستخراج العناصر المرئية
            visual_elements = self._analyze_article_for_visuals(article_title, article_content, article_keywords)

            # إنشاء prompts محسنة
            prompts = self._create_optimized_prompts(visual_elements, num_images)

            # إنشاء الصور باستخدام APIs مختلفة
            for i, prompt_data in enumerate(prompts):
                try:
                    # تناوب بين الخدمات للحصول على تنوع
                    if i % 2 == 0:
                        # استخدام Freepik للصور الزوجية
                        image_result = await self._generate_with_freepik(prompt_data)
                    else:
                        # استخدام FluxAI للصور الفردية
                        image_result = await self._generate_with_fluxai(prompt_data)

                    if image_result:
                        # إضافة معلومات إضافية للصورة
                        image_result.update({
                            'article_title': article_title,
                            'prompt_used': prompt_data['prompt'],
                            'style_category': prompt_data['category'],
                            'generation_timestamp': datetime.now().isoformat(),
                            'safe_for_adsense': True,
                            'copyright_free': True
                        })

                        generated_images.append(image_result)
                        self.generation_stats['successful_generations'] += 1
                        logger.info(f"✅ تم إنشاء الصورة {i+1}/{num_images} بنجاح")

                    # تأخير بين الطلبات لتجنب تجاوز الحدود
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.warning(f"⚠️ فشل في إنشاء الصورة {i+1}: {e}")
                    self.generation_stats['failed_generations'] += 1
                    continue

            # إضافة صور احتياطية إذا لم نحصل على العدد المطلوب
            if len(generated_images) < num_images:
                fallback_needed = num_images - len(generated_images)
                logger.info(f"📸 إضافة {fallback_needed} صورة احتياطية...")

                fallback_images = await self._get_fallback_gaming_images(fallback_needed, visual_elements)
                generated_images.extend(fallback_images)

            self.generation_stats['total_images_created'] += len(generated_images)
            logger.info(f"🎨 تم إنشاء {len(generated_images)} صورة احترافية للمقال")

            return generated_images[:num_images]  # التأكد من عدم تجاوز العدد المطلوب

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صور المقال: {e}")
            return await self._get_fallback_gaming_images(num_images, {})

    def _analyze_article_for_visuals(self, title: str, content: str, keywords: List[str]) -> Dict:
        """تحليل المقال لاستخراج العناصر المرئية"""
        try:
            text_to_analyze = f"{title} {content}".lower()

            # استخراج أسماء الألعاب
            game_names = []
            common_games = [
                'minecraft', 'fortnite', 'call of duty', 'fifa', 'gta', 'cyberpunk',
                'assassins creed', 'the witcher', 'god of war', 'spider-man',
                'valorant', 'league of legends', 'overwatch', 'apex legends'
            ]

            for game in common_games:
                if game in text_to_analyze:
                    game_names.append(game)

            # استخراج نوع المحتوى
            content_type = 'gaming_news'  # افتراضي
            if any(word in text_to_analyze for word in ['review', 'مراجعة', 'تقييم']):
                content_type = 'game_review'
            elif any(word in text_to_analyze for word in ['tips', 'guide', 'نصائح', 'دليل']):
                content_type = 'gaming_tips'
            elif any(word in text_to_analyze for word in ['esports', 'tournament', 'بطولة', 'منافسة']):
                content_type = 'esports'

            # استخراج العناصر المرئية
            visual_elements = {
                'game_names': game_names,
                'content_type': content_type,
                'keywords': keywords,
                'main_theme': self._extract_main_theme(text_to_analyze),
                'visual_style': self.image_styles.get(content_type, self.image_styles['gaming_news'])
            }

            return visual_elements

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحليل المقال للعناصر المرئية: {e}")
            return {
                'game_names': [],
                'content_type': 'gaming_news',
                'keywords': keywords,
                'main_theme': 'gaming',
                'visual_style': self.image_styles['gaming_news']
            }

    def _extract_main_theme(self, text: str) -> str:
        """استخراج الموضوع الرئيسي من النص"""
        themes = {
            'action': ['action', 'fight', 'battle', 'combat', 'war'],
            'adventure': ['adventure', 'explore', 'quest', 'journey'],
            'strategy': ['strategy', 'tactical', 'planning', 'management'],
            'racing': ['racing', 'car', 'speed', 'driving'],
            'sports': ['sports', 'football', 'basketball', 'soccer'],
            'rpg': ['rpg', 'role playing', 'character', 'level up'],
            'simulation': ['simulation', 'sim', 'realistic', 'life'],
            'puzzle': ['puzzle', 'brain', 'logic', 'solve']
        }

        for theme, keywords in themes.items():
            if any(keyword in text for keyword in keywords):
                return theme

        return 'gaming'  # افتراضي

    def _create_optimized_prompts(self, visual_elements: Dict, num_images: int) -> List[Dict]:
        """إنشاء prompts محسنة للصور"""
        prompts = []

        game_names = visual_elements.get('game_names', [])
        content_type = visual_elements.get('content_type', 'gaming_news')
        main_theme = visual_elements.get('main_theme', 'gaming')
        visual_style = visual_elements.get('visual_style', {})

        # قوالب prompts مختلفة حسب نوع المحتوى
        base_prompts = []

        if content_type == 'game_review':
            base_prompts = [
                f"A cinematic screenshot from a {main_theme} video game, showing epic gameplay moments",
                f"Professional game review thumbnail with {main_theme} elements, modern UI design",
                f"High-quality game artwork featuring {main_theme} characters and environments"
            ]
        elif content_type == 'gaming_tips':
            base_prompts = [
                f"Infographic style gaming guide illustration about {main_theme}",
                f"Educational gaming tutorial image with clear visual elements",
                f"Professional gaming tips visualization with modern design"
            ]
        elif content_type == 'esports':
            base_prompts = [
                f"Professional esports tournament scene with {main_theme} game elements",
                f"Competitive gaming arena with dramatic lighting and screens",
                f"Esports championship atmosphere with modern technology"
            ]
        else:  # gaming_news
            base_prompts = [
                f"Breaking gaming news illustration featuring {main_theme} elements",
                f"Modern gaming news thumbnail with vibrant colors and technology",
                f"Professional gaming journalism image with futuristic design"
            ]

        # إضافة أسماء الألعاب إذا كانت متوفرة
        if game_names:
            for i, prompt in enumerate(base_prompts):
                if i < len(game_names):
                    base_prompts[i] = f"{prompt} related to {game_names[i]}"

        # إنشاء prompts نهائية مع إعدادات الجودة
        style_desc = visual_style.get('style', 'modern digital art')
        quality_desc = visual_style.get('quality', 'high quality, 4k')
        mood_desc = visual_style.get('mood', 'exciting, dynamic')

        for i in range(num_images):
            if i < len(base_prompts):
                base_prompt = base_prompts[i]
            else:
                # إنشاء prompts إضافية إذا احتجنا المزيد
                base_prompt = f"Professional gaming content illustration about {main_theme}"

            final_prompt = f"{base_prompt}, {style_desc}, {quality_desc}, {mood_desc}, safe for work, no violence, family friendly"

            prompts.append({
                'prompt': final_prompt,
                'category': content_type,
                'priority': i + 1,
                'style_settings': visual_style
            })

        return prompts

    async def _generate_with_freepik(self, prompt_data: Dict) -> Optional[Dict]:
        """إنشاء صورة باستخدام Freepik API مع معالجة محسنة للأخطاء"""
        try:
            if not self.freepik_api_key:
                logger.warning("⚠️ مفتاح Freepik API غير متوفر")
                return None

            logger.info("🎨 إنشاء صورة باستخدام Freepik...")

            # إعداد طلب Freepik API - استخدام header الصحيح
            headers = {
                'x-freepik-api-key': self.freepik_api_key,
                'Content-Type': 'application/json',
                'User-Agent': 'GamingNewsBot/1.0'
            }

            payload = {
                'prompt': prompt_data['prompt'],
                'resolution': '2k',
                'aspect_ratio': 'square_1_1',
                'model': 'realism',
                'filter_nsfw': True,
                'fixed_generation': False
            }

            timeout = aiohttp.ClientTimeout(total=90, connect=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    # استخدام Freepik Mystic API الصحيح
                    async with session.post(
                        'https://api.freepik.com/v1/ai/mystic',
                        headers=headers,
                        json=payload
                    ) as response:

                        if response.status == 200:
                            result = await response.json()

                            # Freepik Mystic API يعيد task_id داخل data
                            if result.get('data') and result['data'].get('task_id'):
                                task_id = result['data']['task_id']
                                logger.info(f"🎨 تم إنشاء مهمة Freepik بنجاح: {task_id}")

                                # محاولة الحصول على النتيجة مع polling محسن
                                task_result = await self._poll_freepik_task_result(session, headers, task_id)
                                if task_result:
                                    self.generation_stats['freepik_generations'] += 1
                                    return task_result
                                else:
                                    logger.warning("⚠️ لم يتم الحصول على نتيجة من Freepik في الوقت المحدد")
                                    # إرجاع معلومات المهمة على الأقل
                                    return {
                                        'url': None,
                                        'description': f"Freepik task created: {task_id}",
                                        'source': 'Freepik AI',
                                        'license': 'Freepik License',
                                        'attribution': 'Generated by Freepik AI',
                                        'task_id': task_id,
                                        'status': 'pending',
                                        'generation_method': 'ai_generated'
                                    }
                        elif response.status == 401:
                            logger.warning(f"⚠️ خطأ مصادقة في Freepik API (401) - المفتاح قد يكون منتهي الصلاحية")
                            return None
                        elif response.status == 403:
                            logger.warning(f"⚠️ خطأ صلاحيات في Freepik API (403) - المفتاح قد لا يملك الصلاحيات المطلوبة")
                            return None
                        else:
                            logger.warning(f"⚠️ خطأ في Freepik API: {response.status}")
                            return None

                except aiohttp.ClientConnectorError as e:
                    logger.warning(f"⚠️ فشل الاتصال بـ Freepik API: {e}")
                    return None
                except asyncio.TimeoutError:
                    logger.warning("⚠️ انتهت مهلة الاتصال بـ Freepik API")
                    return None

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صورة بـ Freepik: {e}")
            return None

    async def _poll_freepik_task_result(self, session, headers, task_id: str) -> Optional[Dict]:
        """الحصول على نتيجة مهمة Freepik مع polling محسن"""
        max_wait_time = 60  # 60 ثانية كحد أقصى
        poll_interval = 5   # فحص كل 5 ثوانٍ
        waited_time = 0

        logger.info(f"🔄 بدء polling لمهمة Freepik: {task_id}")

        while waited_time < max_wait_time:
            try:
                result = await self._get_freepik_task_result(session, headers, task_id)

                if result:
                    logger.info(f"✅ تم إنجاز مهمة Freepik بنجاح بعد {waited_time}s")
                    return result

                # انتظار قبل المحاولة التالية
                await asyncio.sleep(poll_interval)
                waited_time += poll_interval

                logger.info(f"🔄 polling Freepik: انتظر {waited_time}s من {max_wait_time}s")

            except Exception as e:
                logger.warning(f"خطأ في polling Freepik: {e}")
                await asyncio.sleep(poll_interval)
                waited_time += poll_interval

        logger.warning(f"⚠️ لم يتم الحصول على نتيجة من Freepik في الوقت المحدد")
        return None

    async def _get_freepik_task_result(self, session, headers, task_id: str) -> Optional[Dict]:
        """الحصول على نتيجة مهمة Freepik"""
        try:
            async with session.get(
                f'https://api.freepik.com/v1/ai/mystic/{task_id}',
                headers=headers
            ) as response:

                if response.status == 200:
                    result = await response.json()

                    if result.get('task_status') == 'COMPLETED' and result.get('generated'):
                        generated_images = result['generated']
                        if generated_images and len(generated_images) > 0:
                            image_data = generated_images[0]

                            return {
                                'url': image_data.get('url'),
                                'description': f"Generated by Freepik Mystic",
                                'source': 'Freepik AI',
                                'license': 'Freepik License',
                                'attribution': 'Generated by Freepik AI',
                                'width': 2048,  # 2k resolution
                                'height': 2048,
                                'format': 'PNG',
                                'generation_method': 'ai_generated'
                            }
                    else:
                        logger.info(f"🔄 مهمة Freepik لا تزال قيد المعالجة: {result.get('task_status', 'UNKNOWN')}")
                        return None
                else:
                    logger.warning(f"⚠️ خطأ في جلب نتيجة مهمة Freepik: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ فشل في جلب نتيجة مهمة Freepik: {e}")
            return None

    async def _generate_with_fluxai(self, prompt_data: Dict) -> Optional[Dict]:
        """إنشاء صورة باستخدام FluxAI/Bylo.ai مع معالجة محسنة للأخطاء"""
        try:
            logger.info("🎨 إنشاء صورة باستخدام FluxAI...")

            # FluxAI/Bylo.ai مجاني ولا يحتاج مفتاح API حسب المعلومات المقدمة
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'GamingNewsBot/1.0'
            }

            # إذا كان هناك مفتاح API
            if self.fluxai_api_key:
                headers['Authorization'] = f'Bearer {self.fluxai_api_key}'

            payload = {
                'prompt': prompt_data['prompt'],
                'model': 'flux-1',
                'width': 1024,
                'height': 1024,
                'steps': 20,
                'guidance_scale': 7.5
            }

            # محاولة الاتصال مع timeout أطول ومعالجة أفضل للأخطاء
            timeout = aiohttp.ClientTimeout(total=120, connect=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    async with session.post(
                        'https://api.fluxai.art/v1/generate',
                        headers=headers,
                        json=payload
                    ) as response:

                        if response.status == 200:
                            result = await response.json()

                            if result.get('image_url'):
                                self.generation_stats['fluxai_generations'] += 1

                                return {
                                    'url': result['image_url'],
                                    'description': prompt_data['prompt'][:100],
                                    'source': 'FluxAI',
                                    'license': 'AI Generated - Free Use',
                                    'attribution': 'Generated by FluxAI',
                                    'width': 1024,
                                    'height': 1024,
                                    'format': 'PNG',
                                    'generation_method': 'ai_generated'
                                }
                        else:
                            logger.warning(f"⚠️ خطأ في FluxAI API: {response.status}")
                            return None

                except aiohttp.ClientConnectorError as e:
                    logger.warning(f"⚠️ فشل الاتصال بـ FluxAI API: {e}")
                    logger.info("🔄 سيتم استخدام Freepik كبديل...")
                    return None
                except asyncio.TimeoutError:
                    logger.warning("⚠️ انتهت مهلة الاتصال بـ FluxAI API")
                    return None

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صورة بـ FluxAI: {e}")
            return None

    async def _get_fallback_gaming_images(self, num_images: int, visual_elements: Dict) -> List[Dict]:
        """الحصول على صور احتياطية عالية الجودة للألعاب"""
        try:
            fallback_images = [
                {
                    'url': 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Professional gaming controller with RGB lighting',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by Lucie Liz from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                },
                {
                    'url': 'https://images.pexels.com/photos/1174746/pexels-photo-1174746.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Modern gaming setup with multiple monitors',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by FOX from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                },
                {
                    'url': 'https://images.pexels.com/photos/735911/pexels-photo-735911.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Retro gaming console and controller setup',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by Garrett Morrow from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                },
                {
                    'url': 'https://images.pexels.com/photos/194511/pexels-photo-194511.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Gaming keyboard with colorful backlighting',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by Lukas from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                },
                {
                    'url': 'https://images.pexels.com/photos/3165335/pexels-photo-3165335.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Professional esports gaming arena',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by Yan Krukov from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                },
                {
                    'url': 'https://images.pexels.com/photos/2047905/pexels-photo-2047905.jpeg?auto=compress&cs=tinysrgb&w=1024',
                    'description': 'Gaming headset and accessories',
                    'source': 'Pexels',
                    'license': 'Pexels License',
                    'attribution': 'Photo by Garrett Morrow from Pexels',
                    'width': 1024,
                    'height': 683,
                    'format': 'JPEG',
                    'generation_method': 'stock_photo'
                }
            ]

            # اختيار صور عشوائية حسب العدد المطلوب
            selected_images = random.sample(fallback_images, min(num_images, len(fallback_images)))

            # إضافة معلومات إضافية
            for image in selected_images:
                image.update({
                    'safe_for_adsense': True,
                    'copyright_free': True,
                    'fallback_image': True,
                    'generation_timestamp': datetime.now().isoformat()
                })

            return selected_images

        except Exception as e:
            logger.error(f"❌ فشل في الحصول على صور احتياطية: {e}")
            return []

    def get_generation_stats(self) -> Dict:
        """الحصول على إحصائيات إنشاء الصور"""
        return {
            **self.generation_stats,
            'success_rate': (
                self.generation_stats['successful_generations'] /
                max(1, self.generation_stats['successful_generations'] + self.generation_stats['failed_generations'])
            ) * 100,
            'cache_size': len(self.image_cache)
        }

    async def test_image_generation_apis(self) -> Dict:
        """اختبار جميع APIs إنشاء الصور"""
        test_results = {
            'freepik': {'available': False, 'error': None},
            'fluxai': {'available': False, 'error': None},
            'overall_status': False
        }

        # اختبار Freepik
        try:
            if self.freepik_api_key:
                test_prompt = {
                    'prompt': 'A simple gaming controller, digital art style, high quality',
                    'category': 'test'
                }
                result = await self._generate_with_freepik(test_prompt)
                test_results['freepik']['available'] = result is not None
                if not result:
                    test_results['freepik']['error'] = 'API call failed'
            else:
                test_results['freepik']['error'] = 'API key not provided'
        except Exception as e:
            test_results['freepik']['error'] = str(e)

        # اختبار FluxAI
        try:
            test_prompt = {
                'prompt': 'A simple gaming controller, digital art style, high quality',
                'category': 'test'
            }
            result = await self._generate_with_fluxai(test_prompt)
            test_results['fluxai']['available'] = result is not None
            if not result:
                test_results['fluxai']['error'] = 'API call failed'
        except Exception as e:
            test_results['fluxai']['error'] = str(e)

        # تحديد الحالة العامة
        test_results['overall_status'] = (
            test_results['freepik']['available'] or
            test_results['fluxai']['available']
        )

        return test_results

# إنشاء كائنات عامة للوصول
image_guard = ImageGuard()
ai_image_generator = AIImageGenerator()
