#!/usr/bin/env python3
"""
بوت تيليجرام للتفاعل مع المستخدمين وعرض الإشعارات
"""

import asyncio
import sys
import os
from datetime import datetime
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import BotConfig
from modules.database import db
from modules.video_approval_system import VideoApprovalSystem

class GamingNewsBotHandler:
    """معالج بوت تيليجرام لأخبار الألعاب"""
    
    def __init__(self):
        self.bot_token = BotConfig.TELEGRAM_BOT_TOKEN
        self.application = None
        self.approval_system = VideoApprovalSystem()
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /start"""
        welcome_message = """🎮 <b>مرحباً بك في وكيل أخبار الألعاب!</b>

🤖 أنا بوت ذكي أجمع وأنشر أحدث أخبار الألعاب من مصادر متنوعة

📋 <b>الأوامر المتاحة:</b>
/status - آخر التحديثات والإشعارات
/latest - آخر الأخبار المنشورة
/stats - إحصائيات النظام
/help - المساعدة

💡 <b>ميزات البوت:</b>
• تحليل فيديوهات YouTube تلقائياً
• استخراج الأخبار باستخدام الذكاء الاصطناعي
• نشر المقالات على المدونة والقناة
• إشعارات فورية عند معالجة محتوى جديد

🔗 <b>القناة:</b> {BotConfig.TELEGRAM_CHANNEL_URL}

أرسل أي رسالة للحصول على آخر التحديثات! ✨"""

        await update.message.reply_text(
            welcome_message.format(BotConfig=BotConfig),
            parse_mode='HTML',
            disable_web_page_preview=True
        )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /status"""
        try:
            # الحصول على آخر إشعار
            latest_notification = self.approval_system.get_latest_notification()
            
            # الحصول على آخر الإشعارات من قاعدة البيانات
            notifications = db.get_latest_notifications(3)
            
            if latest_notification:
                status_message = f"""📊 <b>حالة النظام</b>

🔄 <b>آخر نشاط:</b>
{latest_notification['message']}

⏰ <b>وقت آخر تحديث:</b> {latest_notification['timestamp']}
"""
            else:
                status_message = """📊 <b>حالة النظام</b>

🔄 <b>النظام يعمل بشكل طبيعي</b>
⏰ <b>لا توجد تحديثات حديثة</b>
"""
            
            if notifications:
                status_message += "\n📋 <b>آخر الأنشطة:</b>\n"
                for i, notif in enumerate(notifications, 1):
                    status_message += f"{i}. {notif['title'][:50]}...\n"
            
            status_message += f"\n💡 للمزيد من التفاصيل: /latest"
            
            await update.message.reply_text(status_message, parse_mode='HTML')
            
        except Exception as e:
            await update.message.reply_text(
                "❌ حدث خطأ في الحصول على الحالة. حاول مرة أخرى لاحقاً.",
                parse_mode='HTML'
            )
    
    async def latest_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /latest"""
        try:
            notifications = db.get_latest_notifications(5)
            
            if notifications:
                latest_message = "📰 <b>آخر الأخبار المعالجة:</b>\n\n"
                
                for i, notif in enumerate(notifications, 1):
                    duration_min = notif['duration'] // 60 if notif['duration'] else 0
                    latest_message += f"""<b>{i}. {notif['title']}</b>
📺 القناة: {notif['channel'] or 'غير محدد'}
⏱️ المدة: {duration_min} دقيقة
🔗 <a href="{notif['url']}">مشاهدة الفيديو</a>
📅 {notif['created_at'][:16]}

"""
                
                latest_message += f"🔗 <b>تابع القناة:</b> {BotConfig.TELEGRAM_CHANNEL_URL}"
                
            else:
                latest_message = """📭 <b>لا توجد أخبار حديثة</b>

🔄 النظام يعمل على جمع ومعالجة المحتوى الجديد
⏰ تحقق مرة أخرى لاحقاً

🔗 <b>تابع القناة:</b> {BotConfig.TELEGRAM_CHANNEL_URL}"""
            
            await update.message.reply_text(
                latest_message.format(BotConfig=BotConfig),
                parse_mode='HTML',
                disable_web_page_preview=True
            )
            
        except Exception as e:
            await update.message.reply_text(
                "❌ حدث خطأ في الحصول على آخر الأخبار. حاول مرة أخرى لاحقاً.",
                parse_mode='HTML'
            )
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /stats"""
        try:
            # الحصول على إحصائيات من قاعدة البيانات
            stats = db.get_stats_summary(7)  # آخر 7 أيام
            
            stats_message = f"""📊 <b>إحصائيات النظام (آخر 7 أيام)</b>

📰 <b>المقالات:</b>
• منشور: {stats.get('articles_published', 0)}
• معالج: {stats.get('articles_processed', 0)}

🎥 <b>الفيديوهات:</b>
• معالج: {stats.get('videos_processed', 0)}
• مقبول: {stats.get('videos_approved', 0)}

🔧 <b>الأداء:</b>
• معدل النجاح: {stats.get('success_rate', 0):.1f}%
• متوسط الجودة: {stats.get('avg_quality', 0):.1f}/100

⚡ <b>النظام نشط ويعمل 24/7</b>

🔗 <b>القناة:</b> {BotConfig.TELEGRAM_CHANNEL_URL}"""
            
            await update.message.reply_text(
                stats_message.format(BotConfig=BotConfig),
                parse_mode='HTML',
                disable_web_page_preview=True
            )
            
        except Exception as e:
            await update.message.reply_text(
                "❌ حدث خطأ في الحصول على الإحصائيات. حاول مرة أخرى لاحقاً.",
                parse_mode='HTML'
            )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /help"""
        help_message = """❓ <b>مساعدة وكيل أخبار الألعاب</b>

🤖 <b>ما يفعله البوت:</b>
• يراقب قنوات YouTube المتخصصة في الألعاب
• يستخرج النصوص من الفيديوهات باستخدام AI
• يحلل المحتوى ويستخرج الأخبار المهمة
• ينشئ مقالات باللغة العربية
• ينشر على المدونة وقناة تيليجرام

📋 <b>الأوامر:</b>
/start - رسالة الترحيب
/status - حالة النظام وآخر التحديثات
/latest - آخر الأخبار المعالجة
/stats - إحصائيات الأداء
/help - هذه المساعدة

💡 <b>نصائح:</b>
• أرسل أي رسالة للحصول على آخر التحديثات
• تابع القناة للحصول على الأخبار فور نشرها
• النظام يعمل تلقائياً 24/7

🔗 <b>القناة:</b> {BotConfig.TELEGRAM_CHANNEL_URL}
🌐 <b>المدونة:</b> قريباً...

❓ <b>مشاكل؟</b> أرسل رسالة وسنساعدك!"""

        await update.message.reply_text(
            help_message.format(BotConfig=BotConfig),
            parse_mode='HTML',
            disable_web_page_preview=True
        )
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل العامة"""
        # رد تلقائي بآخر التحديثات
        await self.status_command(update, context)
    
    def setup_handlers(self):
        """إعداد معالجات الأوامر"""
        if not self.application:
            return
        
        # إضافة معالجات الأوامر
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("latest", self.latest_command))
        self.application.add_handler(CommandHandler("stats", self.stats_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        
        # معالج الرسائل العامة
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
    
    async def start_bot(self):
        """بدء تشغيل البوت"""
        try:
            # إنشاء التطبيق
            self.application = Application.builder().token(self.bot_token).build()
            
            # إعداد المعالجات
            self.setup_handlers()
            
            print("🤖 بدء تشغيل بوت تيليجرام...")
            print(f"🔗 رابط البوت: https://t.me/{BotConfig.TELEGRAM_BOT_USERNAME.replace('@', '')}")
            
            # بدء التشغيل
            await self.application.run_polling(drop_pending_updates=True)
            
        except Exception as e:
            print(f"❌ فشل في تشغيل البوت: {e}")

async def main():
    """الدالة الرئيسية"""
    bot_handler = GamingNewsBotHandler()
    await bot_handler.start_bot()

if __name__ == "__main__":
    asyncio.run(main())
