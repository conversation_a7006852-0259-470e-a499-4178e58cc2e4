#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكامل نماذج الذكاء الاصطناعي المحلية مع الوكيل
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from typing import List, Dict, Optional
import subprocess
import time
from .logger import logger

class LocalAIIntegration:
    """تكامل نماذج AI المحلية"""
    
    def __init__(self):
        # إعدادات Ollama
        self.ollama_config = {
            'base_url': 'http://localhost:11434',
            'available_models': [],
            'preferred_model': 'llama2:7b',
            'status': 'checking'
        }
        
        # إعدادات GPT4All
        self.gpt4all_config = {
            'base_url': 'http://localhost:4891',
            'available_models': [],
            'status': 'checking'
        }
        
        # إعدادات LM Studio
        self.lmstudio_config = {
            'base_url': 'http://localhost:1234',
            'available_models': [],
            'status': 'checking'
        }
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0,
            'models_used': {}
        }
    
    async def initialize_local_ai(self):
        """تهيئة نماذج AI المحلية"""
        logger.info("🏠 تهيئة نماذج الذكاء الاصطناعي المحلية...")
        
        # فحص Ollama
        ollama_status = await self._check_ollama()
        if ollama_status:
            logger.info("✅ Ollama متاح ويعمل")
        else:
            logger.info("💡 Ollama غير متاح - يمكن تثبيته من https://ollama.ai")
        
        # فحص GPT4All
        gpt4all_status = await self._check_gpt4all()
        if gpt4all_status:
            logger.info("✅ GPT4All متاح ويعمل")
        else:
            logger.info("💡 GPT4All غير متاح - يمكن تثبيته من https://gpt4all.io")
        
        # فحص LM Studio
        lmstudio_status = await self._check_lmstudio()
        if lmstudio_status:
            logger.info("✅ LM Studio متاح ويعمل")
        else:
            logger.info("💡 LM Studio غير متاح - يمكن تثبيته من https://lmstudio.ai")
        
        return ollama_status or gpt4all_status or lmstudio_status
    
    async def _check_ollama(self):
        """فحص حالة Ollama"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.ollama_config['base_url']}/api/tags", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model['name'] for model in data.get('models', [])]
                        self.ollama_config['available_models'] = models
                        self.ollama_config['status'] = 'available'
                        
                        # اختيار أفضل نموذج متاح
                        preferred_models = ['llama2:7b', 'mistral:7b', 'qwen:7b', 'codellama:7b']
                        for model in preferred_models:
                            if model in models:
                                self.ollama_config['preferred_model'] = model
                                break
                        
                        logger.info(f"🤖 Ollama: {len(models)} نموذج متاح")
                        return True
        except Exception as e:
            logger.debug(f"Ollama غير متاح: {e}")
        
        self.ollama_config['status'] = 'unavailable'
        return False
    
    async def _check_gpt4all(self):
        """فحص حالة GPT4All"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.gpt4all_config['base_url']}/v1/models", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model['id'] for model in data.get('data', [])]
                        self.gpt4all_config['available_models'] = models
                        self.gpt4all_config['status'] = 'available'
                        logger.info(f"🤖 GPT4All: {len(models)} نموذج متاح")
                        return True
        except Exception as e:
            logger.debug(f"GPT4All غير متاح: {e}")
        
        self.gpt4all_config['status'] = 'unavailable'
        return False
    
    async def _check_lmstudio(self):
        """فحص حالة LM Studio"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.lmstudio_config['base_url']}/v1/models", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model['id'] for model in data.get('data', [])]
                        self.lmstudio_config['available_models'] = models
                        self.lmstudio_config['status'] = 'available'
                        logger.info(f"🤖 LM Studio: {len(models)} نموذج متاح")
                        return True
        except Exception as e:
            logger.debug(f"LM Studio غير متاح: {e}")
        
        self.lmstudio_config['status'] = 'unavailable'
        return False
    
    async def analyze_content_locally(self, content: str, analysis_type: str = 'quality') -> Dict:
        """تحليل المحتوى باستخدام نماذج محلية"""
        try:
            start_time = time.time()
            
            # تحضير البرومبت
            prompt = self._prepare_local_analysis_prompt(content, analysis_type)
            
            # محاولة النماذج بالترتيب
            if self.ollama_config['status'] == 'available':
                result = await self._query_ollama(prompt)
                if result:
                    response_time = time.time() - start_time
                    self._update_stats('ollama', True, response_time)
                    return self._parse_local_analysis(result, 'ollama')
            
            if self.gpt4all_config['status'] == 'available':
                result = await self._query_gpt4all(prompt)
                if result:
                    response_time = time.time() - start_time
                    self._update_stats('gpt4all', True, response_time)
                    return self._parse_local_analysis(result, 'gpt4all')
            
            if self.lmstudio_config['status'] == 'available':
                result = await self._query_lmstudio(prompt)
                if result:
                    response_time = time.time() - start_time
                    self._update_stats('lmstudio', True, response_time)
                    return self._parse_local_analysis(result, 'lmstudio')
            
            # إذا فشلت جميع النماذج المحلية
            self._update_stats('fallback', False, 0)
            return self._fallback_local_analysis(content)
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحليل المحلي: {e}")
            return self._fallback_local_analysis(content)
    
    async def _query_ollama(self, prompt: str) -> Optional[str]:
        """استعلام Ollama"""
        try:
            payload = {
                'model': self.ollama_config['preferred_model'],
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.3,
                    'top_p': 0.9,
                    'num_predict': 500
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_config['base_url']}/api/generate",
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('response', '')
                    else:
                        logger.warning(f"Ollama API خطأ: {response.status}")
                        return None
        except Exception as e:
            logger.warning(f"خطأ في Ollama: {e}")
            return None
    
    async def _query_gpt4all(self, prompt: str) -> Optional[str]:
        """استعلام GPT4All"""
        try:
            payload = {
                'model': self.gpt4all_config['available_models'][0] if self.gpt4all_config['available_models'] else 'gpt4all',
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': 500,
                'temperature': 0.3
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.gpt4all_config['base_url']}/v1/chat/completions",
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'choices' in data and data['choices']:
                            return data['choices'][0]['message']['content']
                    else:
                        logger.warning(f"GPT4All API خطأ: {response.status}")
                        return None
        except Exception as e:
            logger.warning(f"خطأ في GPT4All: {e}")
            return None
    
    async def _query_lmstudio(self, prompt: str) -> Optional[str]:
        """استعلام LM Studio"""
        try:
            payload = {
                'model': self.lmstudio_config['available_models'][0] if self.lmstudio_config['available_models'] else 'local-model',
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': 500,
                'temperature': 0.3
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.lmstudio_config['base_url']}/v1/chat/completions",
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'choices' in data and data['choices']:
                            return data['choices'][0]['message']['content']
                    else:
                        logger.warning(f"LM Studio API خطأ: {response.status}")
                        return None
        except Exception as e:
            logger.warning(f"خطأ في LM Studio: {e}")
            return None
    
    def _prepare_local_analysis_prompt(self, content: str, analysis_type: str) -> str:
        """تحضير برومبت للتحليل المحلي"""
        if analysis_type == 'quality':
            return f"""
Analyze this gaming content and rate it from 1-10 for:
1. Content Quality
2. Gaming Relevance  
3. Information Freshness
4. Writing Clarity

Content: {content[:600]}

Respond in this exact format:
Quality: X/10
Relevance: X/10
Freshness: X/10
Clarity: X/10
Summary: Brief summary in one sentence
"""
        else:
            return f"""
Analyze this gaming content briefly:

{content[:600]}

Provide a short analysis in 2-3 sentences.
"""
    
    def _parse_local_analysis(self, result: str, model_name: str) -> Dict:
        """تحليل نتيجة النموذج المحلي"""
        try:
            # استخراج النقاط من النص
            import re
            
            quality_match = re.search(r'Quality:\s*(\d+)', result, re.IGNORECASE)
            relevance_match = re.search(r'Relevance:\s*(\d+)', result, re.IGNORECASE)
            freshness_match = re.search(r'Freshness:\s*(\d+)', result, re.IGNORECASE)
            clarity_match = re.search(r'Clarity:\s*(\d+)', result, re.IGNORECASE)
            summary_match = re.search(r'Summary:\s*(.+)', result, re.IGNORECASE)
            
            return {
                'ai_quality_score': int(quality_match.group(1)) if quality_match else 7,
                'ai_relevance_score': int(relevance_match.group(1)) if relevance_match else 8,
                'ai_freshness_score': int(freshness_match.group(1)) if freshness_match else 7,
                'ai_clarity_score': int(clarity_match.group(1)) if clarity_match else 8,
                'ai_summary': summary_match.group(1).strip() if summary_match else result[:100],
                'ai_model_used': f'local_{model_name}',
                'ai_analysis_time': datetime.now().isoformat(),
                'ai_response_time': self.usage_stats.get('average_response_time', 0)
            }
        except Exception as e:
            logger.warning(f"خطأ في تحليل النتيجة المحلية: {e}")
            return self._fallback_local_analysis('')
    
    def _fallback_local_analysis(self, content: str) -> Dict:
        """تحليل احتياطي محلي"""
        # تحليل بسيط بناءً على خصائص المحتوى
        gaming_keywords = ['game', 'gaming', 'player', 'console', 'pc', 'mobile', 'esports', 'nintendo', 'playstation', 'xbox']
        content_lower = content.lower()
        
        keyword_count = sum(1 for keyword in gaming_keywords if keyword in content_lower)
        length_score = min(len(content) // 100, 10)
        
        return {
            'ai_quality_score': min(length_score + 2, 10),
            'ai_relevance_score': min(keyword_count * 2, 10),
            'ai_freshness_score': 7,
            'ai_clarity_score': 8,
            'ai_summary': 'تحليل تلقائي محلي بدون AI',
            'ai_model_used': 'local_fallback',
            'ai_analysis_time': datetime.now().isoformat(),
            'ai_response_time': 0.1
        }
    
    def _update_stats(self, model_name: str, success: bool, response_time: float):
        """تحديث إحصائيات الاستخدام"""
        self.usage_stats['total_requests'] += 1
        
        if success:
            self.usage_stats['successful_requests'] += 1
            # تحديث متوسط وقت الاستجابة
            current_avg = self.usage_stats['average_response_time']
            total_successful = self.usage_stats['successful_requests']
            self.usage_stats['average_response_time'] = ((current_avg * (total_successful - 1)) + response_time) / total_successful
        else:
            self.usage_stats['failed_requests'] += 1
        
        if model_name not in self.usage_stats['models_used']:
            self.usage_stats['models_used'][model_name] = {'success': 0, 'failed': 0}
        
        if success:
            self.usage_stats['models_used'][model_name]['success'] += 1
        else:
            self.usage_stats['models_used'][model_name]['failed'] += 1
    
    async def get_local_ai_status(self) -> Dict:
        """الحصول على حالة نماذج AI المحلية"""
        # إعادة فحص الحالة
        await self.initialize_local_ai()
        
        return {
            'ollama': self.ollama_config,
            'gpt4all': self.gpt4all_config,
            'lmstudio': self.lmstudio_config,
            'usage_stats': self.usage_stats,
            'total_available_models': (
                len(self.ollama_config['available_models']) +
                len(self.gpt4all_config['available_models']) +
                len(self.lmstudio_config['available_models'])
            ),
            'active_services': sum(1 for config in [self.ollama_config, self.gpt4all_config, self.lmstudio_config] 
                                 if config['status'] == 'available')
        }
    
    def install_ollama_models(self, models: List[str] = None):
        """تثبيت نماذج Ollama"""
        if models is None:
            models = ['llama2:7b', 'mistral:7b', 'qwen:7b']
        
        logger.info(f"🔄 تثبيت نماذج Ollama: {models}")
        
        for model in models:
            try:
                logger.info(f"📥 تحميل {model}...")
                result = subprocess.run(['ollama', 'pull', model], 
                                      capture_output=True, text=True, timeout=600)
                if result.returncode == 0:
                    logger.info(f"✅ تم تثبيت {model} بنجاح")
                else:
                    logger.error(f"❌ فشل تثبيت {model}: {result.stderr}")
            except subprocess.TimeoutExpired:
                logger.error(f"⏰ انتهت مهلة تثبيت {model}")
            except Exception as e:
                logger.error(f"❌ خطأ في تثبيت {model}: {e}")

# إنشاء مثيل عام
local_ai = LocalAIIntegration()
