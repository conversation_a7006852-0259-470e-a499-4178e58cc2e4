#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وكيل أخبار الألعاب - نسخة Hugging Face
تطبيق ويب لنشر أخبار الألعاب تلقائياً على Blogger
"""

import os
import sys
import gradio as gr
from datetime import datetime
import json
import logging

# إعداد المسارات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/huggingface_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

try:
    from modules.enhanced_web_interface import EnhancedWebInterface
    from modules.api_key_manager import APIKeyManager
    from modules.database import DatabaseManager
    from modules.logger import setup_logger
    from config.settings import SETTINGS
except ImportError as e:
    logger.error(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class HuggingFaceGameNewsAgent:
    """وكيل أخبار الألعاب لـ Hugging Face"""
    
    def __init__(self):
        self.web_interface = None
        self.api_manager = None
        self.db_manager = None
        self.setup_complete = False
        
    def initialize(self):
        """تهيئة الوكيل"""
        try:
            # إنشاء المجلدات المطلوبة
            os.makedirs('logs', exist_ok=True)
            os.makedirs('data', exist_ok=True)
            os.makedirs('cache', exist_ok=True)
            os.makedirs('images', exist_ok=True)
            
            # تهيئة قاعدة البيانات
            self.db_manager = DatabaseManager()
            
            # تهيئة مدير API
            self.api_manager = APIKeyManager()
            
            # تهيئة واجهة الويب
            self.web_interface = EnhancedWebInterface()
            
            self.setup_complete = True
            logger.info("تم تهيئة الوكيل بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة الوكيل: {e}")
            self.setup_complete = False
    
    def get_status(self):
        """الحصول على حالة النظام"""
        if not self.setup_complete:
            return "❌ النظام غير مهيأ"
        
        status = {
            "النظام": "✅ يعمل",
            "قاعدة البيانات": "✅ متصلة" if self.db_manager else "❌ غير متصلة",
            "مفاتيح API": "✅ محملة" if self.api_manager else "❌ غير محملة",
            "الوقت": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return "\n".join([f"{k}: {v}" for k, v in status.items()])
    
    def search_news(self, query, max_results=5):
        """البحث عن الأخبار"""
        if not self.setup_complete:
            return "خطأ: النظام غير مهيأ"
        
        try:
            # هنا يمكن إضافة منطق البحث الفعلي
            results = f"نتائج البحث عن: {query}\n"
            results += f"عدد النتائج المطلوبة: {max_results}\n"
            results += "🔍 جاري البحث في مصادر الأخبار...\n"
            results += "📰 تم العثور على أخبار جديدة\n"
            results += "✅ تم تحليل المحتوى بنجاح"
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في البحث: {e}")
            return f"خطأ في البحث: {str(e)}"
    
    def publish_article(self, title, content, category="عام"):
        """نشر مقال"""
        if not self.setup_complete:
            return "خطأ: النظام غير مهيأ"
        
        try:
            # هنا يمكن إضافة منطق النشر الفعلي
            result = f"📝 نشر المقال:\n"
            result += f"العنوان: {title}\n"
            result += f"الفئة: {category}\n"
            result += f"طول المحتوى: {len(content)} حرف\n"
            result += f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            result += "✅ تم النشر بنجاح"
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في النشر: {e}")
            return f"خطأ في النشر: {str(e)}"

# إنشاء مثيل الوكيل
agent = HuggingFaceGameNewsAgent()
agent.initialize()

def create_interface():
    """إنشاء واجهة Gradio"""
    
    with gr.Blocks(title="وكيل أخبار الألعاب", theme=gr.themes.Soft()) as interface:
        
        gr.Markdown("# 🎮 وكيل أخبار الألعاب")
        gr.Markdown("وكيل ذكي لنشر أخبار الألعاب تلقائياً على Blogger")
        
        with gr.Tab("📊 لوحة التحكم"):
            status_output = gr.Textbox(
                label="حالة النظام",
                value=agent.get_status(),
                interactive=False,
                lines=6
            )
            
            refresh_btn = gr.Button("🔄 تحديث الحالة")
            refresh_btn.click(
                fn=agent.get_status,
                outputs=status_output
            )
        
        with gr.Tab("🔍 البحث عن الأخبار"):
            with gr.Row():
                search_query = gr.Textbox(
                    label="كلمات البحث",
                    placeholder="أدخل كلمات البحث عن الأخبار..."
                )
                max_results = gr.Slider(
                    minimum=1,
                    maximum=20,
                    value=5,
                    step=1,
                    label="عدد النتائج"
                )
            
            search_btn = gr.Button("🔍 بحث")
            search_output = gr.Textbox(
                label="نتائج البحث",
                lines=10,
                interactive=False
            )
            
            search_btn.click(
                fn=agent.search_news,
                inputs=[search_query, max_results],
                outputs=search_output
            )
        
        with gr.Tab("📝 نشر المقالات"):
            article_title = gr.Textbox(
                label="عنوان المقال",
                placeholder="أدخل عنوان المقال..."
            )
            
            article_content = gr.Textbox(
                label="محتوى المقال",
                placeholder="أدخل محتوى المقال...",
                lines=10
            )
            
            article_category = gr.Dropdown(
                choices=["أخبار عامة", "مراجعات", "إطلاقات جديدة", "تحديثات", "بطولات"],
                value="أخبار عامة",
                label="فئة المقال"
            )
            
            publish_btn = gr.Button("📤 نشر المقال")
            publish_output = gr.Textbox(
                label="نتيجة النشر",
                lines=6,
                interactive=False
            )
            
            publish_btn.click(
                fn=agent.publish_article,
                inputs=[article_title, article_content, article_category],
                outputs=publish_output
            )
        
        with gr.Tab("ℹ️ معلومات"):
            gr.Markdown("""
            ## حول الوكيل
            
            هذا وكيل ذكي لنشر أخبار الألعاب تلقائياً على منصة Blogger.
            
            ### الميزات:
            - 🔍 البحث الذكي عن الأخبار
            - 📝 إنشاء المقالات تلقائياً
            - 🖼️ إدارة الصور
            - 📊 تحليل الأداء
            - 🌐 نشر متعدد المنصات
            
            ### التقنيات المستخدمة:
            - Python 3.8+
            - Gradio للواجهة
            - Google Blogger API
            - الذكاء الاصطناعي لمعالجة النصوص
            """)
    
    return interface

if __name__ == "__main__":
    # إنشاء وتشغيل الواجهة
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=True,
        show_error=True
    )
