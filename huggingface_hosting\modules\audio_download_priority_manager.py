# نظام الأولوية والتبديل التلقائي لطرق تحميل الصوت
import json
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from modules.logger import logger

class DownloadMethodStatus(Enum):
    """حالة طريقة التحميل"""
    ACTIVE = "active"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    DISABLED = "disabled"
    QUOTA_EXCEEDED = "quota_exceeded"
    UNAVAILABLE = "unavailable"

@dataclass
class DownloadMethodHealth:
    """صحة طريقة التحميل"""
    method_name: str
    status: DownloadMethodStatus
    success_rate: float  # معدل النجاح (0-1)
    avg_response_time: float  # متوسط وقت الاستجابة بالثواني
    last_success: Optional[datetime] = None
    last_error: Optional[datetime] = None
    error_count: int = 0
    success_count: int = 0
    total_requests: int = 0
    consecutive_failures: int = 0
    last_error_message: str = ""
    cost_per_use: float = 0.0

@dataclass
class DownloadMethodQuota:
    """حصة طريقة التحميل"""
    method_name: str
    monthly_limit: float  # الحد الشهري (بالوحدات أو التكلفة)
    monthly_usage: float  # الاستخدام الشهري
    daily_limit: float = 0  # الحد اليومي (اختياري)
    daily_usage: float = 0  # الاستخدام اليومي
    reset_date: Optional[datetime] = None

class AudioDownloadPriorityManager:
    """مدير الأولوية والتبديل التلقائي لطرق تحميل الصوت"""
    
    def __init__(self):
        self.health_data: Dict[str, DownloadMethodHealth] = {}
        self.usage_quotas: Dict[str, DownloadMethodQuota] = {}
        self.priority_rules: Dict[str, int] = {}
        self.fallback_chain: List[str] = []
        self.blacklist_duration = 300  # 5 دقائق للطرق المعطلة مؤقتاً
        self.data_file = "cache/audio_download_priority_data.json"
        
        self._load_data()
        self._initialize_default_priorities()
        
    def _initialize_default_priorities(self):
        """تهيئة الأولويات الافتراضية"""
        default_priorities = {
            "apify": 1,           # أعلى أولوية - جودة عالية وسرعة
            "youtube_dl": 2,      # الأولوية الثانية - مجاني بالكامل
            "yt_dlp": 3,          # البديل الثالث - محسن من youtube-dl
            "pytube": 4,          # البديل الرابع - الطريقة الحالية
            "youtube_api": 5      # البديل الأخير - الطريقة الحالية
        }
        
        # تحديث الأولويات فقط إذا لم تكن موجودة
        for method, priority in default_priorities.items():
            if method not in self.priority_rules:
                self.priority_rules[method] = priority
                
        # إنشاء سلسلة البدائل
        self.fallback_chain = sorted(self.priority_rules.keys(), key=lambda x: self.priority_rules[x])
        
    def _load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # تحميل بيانات الصحة
                for method_name, health_data in data.get("health", {}).items():
                    # تحويل التواريخ من string إلى datetime
                    if health_data.get("last_success"):
                        health_data["last_success"] = datetime.fromisoformat(health_data["last_success"])
                    if health_data.get("last_error"):
                        health_data["last_error"] = datetime.fromisoformat(health_data["last_error"])
                        
                    health_data["status"] = DownloadMethodStatus(health_data["status"])
                    self.health_data[method_name] = DownloadMethodHealth(**health_data)
                    
                # تحميل بيانات الاستخدام
                for method_name, quota_data in data.get("quotas", {}).items():
                    if quota_data.get("reset_date"):
                        quota_data["reset_date"] = datetime.fromisoformat(quota_data["reset_date"])
                    self.usage_quotas[method_name] = DownloadMethodQuota(**quota_data)
                    
                # تحميل قواعد الأولوية
                self.priority_rules.update(data.get("priorities", {}))
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل بيانات أولوية التحميل: {e}")
            
    def _save_data(self):
        """حفظ البيانات"""
        try:
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            
            # تحويل البيانات للحفظ
            health_data = {}
            for method_name, health in self.health_data.items():
                health_dict = asdict(health)
                # تحويل datetime إلى string
                if health_dict["last_success"]:
                    health_dict["last_success"] = health_dict["last_success"].isoformat()
                if health_dict["last_error"]:
                    health_dict["last_error"] = health_dict["last_error"].isoformat()
                health_dict["status"] = health_dict["status"].value
                health_data[method_name] = health_dict
                
            quota_data = {}
            for method_name, quota in self.usage_quotas.items():
                quota_dict = asdict(quota)
                if quota_dict["reset_date"]:
                    quota_dict["reset_date"] = quota_dict["reset_date"].isoformat()
                quota_data[method_name] = quota_dict
                
            data = {
                "health": health_data,
                "quotas": quota_data,
                "priorities": self.priority_rules,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ بيانات أولوية التحميل: {e}")
            
    def initialize_method(self, method_name: str, monthly_limit: float, 
                         daily_limit: float = 0, cost_per_use: float = 0.0):
        """تهيئة طريقة تحميل جديدة"""
        if method_name not in self.health_data:
            self.health_data[method_name] = DownloadMethodHealth(
                method_name=method_name,
                status=DownloadMethodStatus.ACTIVE,
                success_rate=1.0,
                avg_response_time=0.0,
                cost_per_use=cost_per_use
            )
            
        if method_name not in self.usage_quotas:
            self.usage_quotas[method_name] = DownloadMethodQuota(
                method_name=method_name,
                monthly_limit=monthly_limit,
                monthly_usage=0,
                daily_limit=daily_limit,
                daily_usage=0,
                reset_date=datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            )
            
        self._save_data()
        
    def record_download_result(self, method_name: str, success: bool, 
                             response_time: float, cost: float = 0,
                             error_message: str = ""):
        """تسجيل نتيجة تحميل"""
        now = datetime.now()
        
        # تحديث بيانات الصحة
        if method_name not in self.health_data:
            self.initialize_method(method_name, 999999)  # حد افتراضي عالي
            
        health = self.health_data[method_name]
        health.total_requests += 1
        
        if success:
            health.success_count += 1
            health.last_success = now
            health.consecutive_failures = 0
            health.status = DownloadMethodStatus.ACTIVE
        else:
            health.error_count += 1
            health.last_error = now
            health.consecutive_failures += 1
            health.last_error_message = error_message
            
            # تحديد حالة الطريقة بناءً على نوع الخطأ
            if "rate limit" in error_message.lower() or "quota" in error_message.lower():
                health.status = DownloadMethodStatus.RATE_LIMITED
            elif "unavailable" in error_message.lower() or "not found" in error_message.lower():
                health.status = DownloadMethodStatus.UNAVAILABLE
            elif health.consecutive_failures >= 3:
                health.status = DownloadMethodStatus.ERROR
                
        # حساب معدل النجاح ومتوسط وقت الاستجابة
        health.success_rate = health.success_count / health.total_requests
        
        # تحديث متوسط وقت الاستجابة (متوسط متحرك)
        if health.avg_response_time == 0:
            health.avg_response_time = response_time
        else:
            health.avg_response_time = (health.avg_response_time * 0.8) + (response_time * 0.2)
            
        # تحديث استخدام الحصة
        if cost > 0:
            self._update_usage(method_name, cost)
            
        self._save_data()
        
    def _update_usage(self, method_name: str, cost: float):
        """تحديث استخدام الحصة"""
        if method_name not in self.usage_quotas:
            return
            
        quota = self.usage_quotas[method_name]
        now = datetime.now()
        
        # فحص إعادة تعيين الحصة الشهرية
        if quota.reset_date and now >= quota.reset_date + timedelta(days=30):
            quota.monthly_usage = 0
            quota.daily_usage = 0
            quota.reset_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
        # فحص إعادة تعيين الحصة اليومية
        if quota.reset_date and now.date() > quota.reset_date.date():
            quota.daily_usage = 0
            
        # تحديث الاستخدام
        quota.monthly_usage += cost
        quota.daily_usage += cost
        
        # فحص تجاوز الحدود
        health = self.health_data[method_name]
        if quota.monthly_usage >= quota.monthly_limit:
            health.status = DownloadMethodStatus.QUOTA_EXCEEDED
        elif quota.daily_limit > 0 and quota.daily_usage >= quota.daily_limit:
            health.status = DownloadMethodStatus.QUOTA_EXCEEDED
            
    def get_best_method(self, required_quality: str = "medium") -> Optional[str]:
        """الحصول على أفضل طريقة تحميل متاحة"""
        available_methods = []
        
        for method_name in self.fallback_chain:
            if self._is_method_available(method_name):
                health = self.health_data.get(method_name)
                if health:
                    # حساب نقاط الجودة
                    quality_score = self._calculate_quality_score(health, method_name)
                    available_methods.append((method_name, quality_score))
                    
        if not available_methods:
            logger.warning("⚠️ لا توجد طرق تحميل متاحة")
            return None
            
        # ترتيب حسب نقاط الجودة
        available_methods.sort(key=lambda x: x[1], reverse=True)
        best_method = available_methods[0][0]
        
        logger.info(f"🎯 أفضل طريقة تحميل متاحة: {best_method}")
        return best_method
        
    def _is_method_available(self, method_name: str) -> bool:
        """فحص ما إذا كانت طريقة التحميل متاحة"""
        # فحص الصحة العامة
        health = self.health_data.get(method_name)
        if not health or health.status in [DownloadMethodStatus.DISABLED, DownloadMethodStatus.ERROR, DownloadMethodStatus.UNAVAILABLE]:
            return False
            
        # فحص الحصة
        quota = self.usage_quotas.get(method_name)
        if quota:
            if quota.monthly_usage >= quota.monthly_limit:
                return False
            if quota.daily_limit > 0 and quota.daily_usage >= quota.daily_limit:
                return False
                
        # فحص الفشل المتتالي
        if health.consecutive_failures >= 3:
            # فحص ما إذا كان الوقت كافي للمحاولة مرة أخرى
            if health.last_error:
                time_since_error = (datetime.now() - health.last_error).total_seconds()
                if time_since_error < self.blacklist_duration:
                    return False
                    
        return True
        
    def _calculate_quality_score(self, health: DownloadMethodHealth, method_name: str) -> float:
        """حساب نقاط جودة طريقة التحميل"""
        score = 0.0
        
        # الأولوية الأساسية (40%)
        priority = self.priority_rules.get(method_name, 99)
        priority_score = max(0, (10 - priority) / 10) * 0.4
        score += priority_score
        
        # معدل النجاح (30%)
        success_score = health.success_rate * 0.3
        score += success_score
        
        # سرعة الاستجابة (20%)
        if health.avg_response_time > 0:
            # كلما قل الوقت، كلما زادت النقاط
            speed_score = max(0, (300 - health.avg_response_time) / 300) * 0.2
            score += speed_score
        else:
            score += 0.2  # نقاط كاملة للطرق الجديدة
            
        # الاستقرار (10%)
        stability_score = max(0, (5 - health.consecutive_failures) / 5) * 0.1
        score += stability_score
        
        return score
        
    def get_download_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات طرق التحميل"""
        stats = {
            "total_methods": len(self.health_data),
            "active_methods": 0,
            "methods": {}
        }
        
        for method_name, health in self.health_data.items():
            quota = self.usage_quotas.get(method_name, DownloadMethodQuota(method_name, 0, 0))
            
            method_stats = {
                "status": health.status.value,
                "success_rate": round(health.success_rate * 100, 2),
                "avg_response_time": round(health.avg_response_time, 2),
                "total_requests": health.total_requests,
                "consecutive_failures": health.consecutive_failures,
                "monthly_usage": quota.monthly_usage,
                "monthly_limit": quota.monthly_limit,
                "usage_percentage": round((quota.monthly_usage / quota.monthly_limit) * 100, 2) if quota.monthly_limit > 0 else 0,
                "priority": self.priority_rules.get(method_name, 99),
                "quality_score": round(self._calculate_quality_score(health, method_name), 3),
                "cost_per_use": health.cost_per_use
            }
            
            if health.status == DownloadMethodStatus.ACTIVE:
                stats["active_methods"] += 1
                
            stats["methods"][method_name] = method_stats
            
        return stats
        
    def reset_method_health(self, method_name: str):
        """إعادة تعيين صحة طريقة معينة"""
        if method_name in self.health_data:
            health = self.health_data[method_name]
            health.status = DownloadMethodStatus.ACTIVE
            health.consecutive_failures = 0
            health.error_count = 0
            health.last_error = None
            health.last_error_message = ""
            self._save_data()
            logger.info(f"🔄 تم إعادة تعيين صحة طريقة التحميل {method_name}")


# إنشاء مثيل عام
audio_download_priority_manager = AudioDownloadPriorityManager()
