# محرك البحث المحلي - بديل مجاني لتوفير استهلاك API
import asyncio
import aiohttp
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import quote_plus, urljoin
import feedparser
from bs4 import BeautifulSoup

from .logger import logger
from .api_usage_manager import APIProvider, cache_result, get_cached_result

@dataclass
class LocalSearchResult:
    """نتيجة البحث المحلي"""
    title: str
    url: str
    content: str
    source: str
    published_date: Optional[datetime]
    relevance_score: float
    metadata: Dict[str, Any]

class LocalSearchEngine:
    """محرك البحث المحلي - بديل مجاني"""
    
    def __init__(self):
        # مصادر RSS للأخبار المجانية
        self.rss_sources = {
            'gaming': [
                'https://www.gamespot.com/feeds/news/',
                'https://www.ign.com/feeds/news',
                'https://www.polygon.com/rss/index.xml',
                'https://kotaku.com/rss',
                'https://www.pcgamer.com/rss/',
                'https://www.eurogamer.net/feed',
                'https://www.destructoid.com/feed/',
                'https://www.gamasutra.com/rss/news.xml'
            ],
            'tech': [
                'https://techcrunch.com/feed/',
                'https://www.theverge.com/rss/index.xml',
                'https://arstechnica.com/feed/',
                'https://www.wired.com/feed/rss'
            ]
        }
        
        # مواقع البحث المجانية
        self.search_sites = [
            'reddit.com/r/gaming',
            'reddit.com/r/Games',
            'reddit.com/r/pcgaming',
            'news.ycombinator.com',
            'github.com'
        ]
        
        # إعدادات البحث
        self.search_config = {
            'max_results_per_source': 10,
            'timeout': 15,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'enable_content_extraction': True,
            'min_content_length': 100,
            'max_content_length': 2000
        }
        
        # إحصائيات البحث المحلي
        self.local_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'rss_fetches': 0,
            'web_scrapes': 0,
            'cache_hits': 0,
            'average_response_time': 0.0
        }
        
        # تخزين مؤقت للمحتوى
        self.content_cache = {}
        self.rss_cache = {}
        
        logger.info("🏠 تم تهيئة محرك البحث المحلي")
    
    async def search(self, query: str, max_results: int = 10, search_type: str = "general") -> List[LocalSearchResult]:
        """البحث المحلي الرئيسي"""
        start_time = time.time()
        self.local_stats['total_searches'] += 1
        
        try:
            logger.info(f"🏠 بدء البحث المحلي: '{query}'")
            
            # فحص التخزين المؤقت أولاً
            cached_results = await get_cached_result(f"local_{query}", APIProvider.LOCAL)
            if cached_results:
                self.local_stats['cache_hits'] += 1
                logger.debug("💾 استخدام نتائج محفوظة من البحث المحلي")
                return [LocalSearchResult(**result) for result in cached_results.get('results', [])]
            
            all_results = []
            
            # 1. البحث في RSS feeds
            rss_results = await self._search_rss_feeds(query, max_results // 2)
            all_results.extend(rss_results)
            
            # 2. البحث في المواقع المجانية
            web_results = await self._search_free_websites(query, max_results // 2)
            all_results.extend(web_results)
            
            # 3. ترتيب النتائج حسب الصلة
            sorted_results = self._rank_results(all_results, query)
            
            # 4. تحديد النتائج النهائية
            final_results = sorted_results[:max_results]
            
            # 5. حفظ في التخزين المؤقت
            if final_results:
                cache_data = {
                    'results': [asdict(result) for result in final_results],
                    'query': query,
                    'timestamp': datetime.now().isoformat()
                }
                await cache_result(f"local_{query}", cache_data, APIProvider.LOCAL, search_type)
            
            # تحديث الإحصائيات
            execution_time = time.time() - start_time
            self._update_stats(len(final_results), execution_time)
            
            logger.info(f"🏠 البحث المحلي مكتمل: {len(final_results)} نتيجة في {execution_time:.2f}ث")
            
            return final_results
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث المحلي: {e}")
            return []
    
    async def _search_rss_feeds(self, query: str, max_results: int) -> List[LocalSearchResult]:
        """البحث في RSS feeds"""
        try:
            results = []
            query_keywords = query.lower().split()
            
            # جلب RSS feeds
            for category, feeds in self.rss_sources.items():
                for feed_url in feeds[:3]:  # أول 3 مصادر لكل فئة
                    try:
                        feed_results = await self._fetch_rss_feed(feed_url, query_keywords)
                        results.extend(feed_results)
                        
                        if len(results) >= max_results:
                            break
                            
                    except Exception as e:
                        logger.debug(f"فشل في جلب RSS من {feed_url}: {e}")
                        continue
                
                if len(results) >= max_results:
                    break
            
            self.local_stats['rss_fetches'] += 1
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث في RSS: {e}")
            return []
    
    async def _fetch_rss_feed(self, feed_url: str, keywords: List[str]) -> List[LocalSearchResult]:
        """جلب وتحليل RSS feed"""
        try:
            # فحص التخزين المؤقت للـ RSS
            cache_key = f"rss_{feed_url}"
            if cache_key in self.rss_cache:
                cached_data = self.rss_cache[cache_key]
                if datetime.now() - cached_data['timestamp'] < timedelta(minutes=30):
                    feed_data = cached_data['data']
                else:
                    del self.rss_cache[cache_key]
                    feed_data = None
            else:
                feed_data = None
            
            # جلب RSS إذا لم يكن محفوظاً
            if not feed_data:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    async with session.get(feed_url, headers={'User-Agent': self.search_config['user_agent']}) as response:
                        if response.status == 200:
                            content = await response.text()
                            feed_data = feedparser.parse(content)
                            
                            # حفظ في التخزين المؤقت
                            self.rss_cache[cache_key] = {
                                'data': feed_data,
                                'timestamp': datetime.now()
                            }
                        else:
                            return []
            
            # تحليل المقالات
            results = []
            for entry in feed_data.entries[:20]:  # أول 20 مقال
                try:
                    title = entry.get('title', '')
                    content = entry.get('summary', '') or entry.get('description', '')
                    
                    # فحص الصلة بالكلمات المفتاحية
                    relevance = self._calculate_relevance(title + ' ' + content, keywords)
                    
                    if relevance > 0.3:  # عتبة الصلة
                        # استخراج التاريخ
                        published_date = None
                        if hasattr(entry, 'published_parsed') and entry.published_parsed:
                            published_date = datetime(*entry.published_parsed[:6])
                        
                        result = LocalSearchResult(
                            title=title,
                            url=entry.get('link', ''),
                            content=self._clean_content(content),
                            source=feed_url.split('/')[2],  # استخراج اسم الموقع
                            published_date=published_date,
                            relevance_score=relevance,
                            metadata={
                                'source_type': 'rss',
                                'feed_url': feed_url,
                                'author': entry.get('author', ''),
                                'tags': entry.get('tags', [])
                            }
                        )
                        results.append(result)
                
                except Exception as e:
                    logger.debug(f"فشل في تحليل مقال RSS: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.debug(f"فشل في جلب RSS feed {feed_url}: {e}")
            return []
    
    async def _search_free_websites(self, query: str, max_results: int) -> List[LocalSearchResult]:
        """البحث في المواقع المجانية"""
        try:
            results = []
            
            # البحث في Reddit
            reddit_results = await self._search_reddit(query, max_results // 2)
            results.extend(reddit_results)
            
            # البحث في GitHub (للمشاريع والكود)
            if 'code' in query.lower() or 'github' in query.lower():
                github_results = await self._search_github(query, max_results // 4)
                results.extend(github_results)
            
            # البحث في Hacker News
            hn_results = await self._search_hackernews(query, max_results // 4)
            results.extend(hn_results)
            
            self.local_stats['web_scrapes'] += 1
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث في المواقع المجانية: {e}")
            return []
    
    async def _search_reddit(self, query: str, max_results: int) -> List[LocalSearchResult]:
        """البحث في Reddit"""
        try:
            results = []
            
            # استخدام Reddit JSON API (مجاني)
            search_url = f"https://www.reddit.com/r/gaming/search.json?q={quote_plus(query)}&sort=relevance&limit={max_results}"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(search_url, headers={'User-Agent': self.search_config['user_agent']}) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for post in data.get('data', {}).get('children', []):
                            try:
                                post_data = post.get('data', {})
                                
                                title = post_data.get('title', '')
                                content = post_data.get('selftext', '') or post_data.get('url', '')
                                
                                # حساب الصلة
                                relevance = self._calculate_relevance(title + ' ' + content, query.split())
                                
                                if relevance > 0.2:
                                    result = LocalSearchResult(
                                        title=title,
                                        url=f"https://reddit.com{post_data.get('permalink', '')}",
                                        content=self._clean_content(content),
                                        source='reddit.com',
                                        published_date=datetime.fromtimestamp(post_data.get('created_utc', 0)),
                                        relevance_score=relevance,
                                        metadata={
                                            'source_type': 'reddit',
                                            'subreddit': post_data.get('subreddit', ''),
                                            'score': post_data.get('score', 0),
                                            'num_comments': post_data.get('num_comments', 0)
                                        }
                                    )
                                    results.append(result)
                            
                            except Exception as e:
                                logger.debug(f"فشل في تحليل منشور Reddit: {e}")
                                continue
            
            return results
            
        except Exception as e:
            logger.debug(f"فشل في البحث في Reddit: {e}")
            return []
    
    async def _search_github(self, query: str, max_results: int) -> List[LocalSearchResult]:
        """البحث في GitHub"""
        try:
            results = []
            
            # استخدام GitHub Search API (مجاني مع حدود)
            search_url = f"https://api.github.com/search/repositories?q={quote_plus(query)}&sort=stars&per_page={max_results}"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(search_url, headers={'User-Agent': self.search_config['user_agent']}) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for repo in data.get('items', []):
                            try:
                                title = repo.get('full_name', '')
                                description = repo.get('description', '') or ''
                                
                                relevance = self._calculate_relevance(title + ' ' + description, query.split())
                                
                                if relevance > 0.2:
                                    result = LocalSearchResult(
                                        title=title,
                                        url=repo.get('html_url', ''),
                                        content=description,
                                        source='github.com',
                                        published_date=datetime.fromisoformat(repo.get('created_at', '').replace('Z', '+00:00')),
                                        relevance_score=relevance,
                                        metadata={
                                            'source_type': 'github',
                                            'stars': repo.get('stargazers_count', 0),
                                            'language': repo.get('language', ''),
                                            'forks': repo.get('forks_count', 0)
                                        }
                                    )
                                    results.append(result)
                            
                            except Exception as e:
                                logger.debug(f"فشل في تحليل مستودع GitHub: {e}")
                                continue
            
            return results
            
        except Exception as e:
            logger.debug(f"فشل في البحث في GitHub: {e}")
            return []
    
    async def _search_hackernews(self, query: str, max_results: int) -> List[LocalSearchResult]:
        """البحث في Hacker News"""
        try:
            results = []
            
            # استخدام Algolia HN Search API (مجاني)
            search_url = f"https://hn.algolia.com/api/v1/search?query={quote_plus(query)}&tags=story&hitsPerPage={max_results}"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(search_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for hit in data.get('hits', []):
                            try:
                                title = hit.get('title', '')
                                content = hit.get('story_text', '') or hit.get('url', '')
                                
                                relevance = self._calculate_relevance(title + ' ' + content, query.split())
                                
                                if relevance > 0.2:
                                    result = LocalSearchResult(
                                        title=title,
                                        url=hit.get('url', f"https://news.ycombinator.com/item?id={hit.get('objectID')}"),
                                        content=self._clean_content(content),
                                        source='news.ycombinator.com',
                                        published_date=datetime.fromisoformat(hit.get('created_at', '').replace('Z', '+00:00')),
                                        relevance_score=relevance,
                                        metadata={
                                            'source_type': 'hackernews',
                                            'points': hit.get('points', 0),
                                            'num_comments': hit.get('num_comments', 0),
                                            'author': hit.get('author', '')
                                        }
                                    )
                                    results.append(result)
                            
                            except Exception as e:
                                logger.debug(f"فشل في تحليل قصة Hacker News: {e}")
                                continue
            
            return results
            
        except Exception as e:
            logger.debug(f"فشل في البحث في Hacker News: {e}")
            return []
    
    def _calculate_relevance(self, text: str, keywords: List[str]) -> float:
        """حساب صلة النص بالكلمات المفتاحية"""
        if not text or not keywords:
            return 0.0
        
        text_lower = text.lower()
        total_score = 0.0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # نقاط للتطابق الدقيق
            if keyword_lower in text_lower:
                total_score += 1.0
            
            # نقاط للتطابق الجزئي
            for word in text_lower.split():
                if keyword_lower in word or word in keyword_lower:
                    total_score += 0.5
        
        # تطبيع النقاط
        max_possible_score = len(keywords)
        return min(total_score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0
    
    def _rank_results(self, results: List[LocalSearchResult], query: str) -> List[LocalSearchResult]:
        """ترتيب النتائج حسب الصلة والجودة"""
        def calculate_final_score(result: LocalSearchResult) -> float:
            score = result.relevance_score
            
            # زيادة النقاط للمحتوى الحديث
            if result.published_date:
                days_old = (datetime.now() - result.published_date).days
                if days_old <= 1:
                    score += 0.3
                elif days_old <= 7:
                    score += 0.2
                elif days_old <= 30:
                    score += 0.1
            
            # زيادة النقاط للمصادر الموثوقة
            trusted_sources = ['gamespot.com', 'ign.com', 'polygon.com', 'github.com']
            if any(source in result.source for source in trusted_sources):
                score += 0.2
            
            # زيادة النقاط للمحتوى الطويل والمفيد
            if len(result.content) > 200:
                score += 0.1
            
            return score
        
        # ترتيب حسب النقاط النهائية
        return sorted(results, key=calculate_final_score, reverse=True)
    
    def _clean_content(self, content: str) -> str:
        """تنظيف المحتوى"""
        if not content:
            return ""
        
        # إزالة HTML tags
        content = BeautifulSoup(content, 'html.parser').get_text()
        
        # إزالة المسافات الزائدة
        content = re.sub(r'\s+', ' ', content).strip()
        
        # تحديد الطول
        max_length = self.search_config['max_content_length']
        if len(content) > max_length:
            content = content[:max_length] + "..."
        
        return content
    
    def _update_stats(self, results_count: int, execution_time: float):
        """تحديث الإحصائيات"""
        if results_count > 0:
            self.local_stats['successful_searches'] += 1
        
        # تحديث متوسط وقت الاستجابة
        total_searches = self.local_stats['total_searches']
        current_avg = self.local_stats['average_response_time']
        
        self.local_stats['average_response_time'] = (
            (current_avg * (total_searches - 1) + execution_time) / total_searches
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات البحث المحلي"""
        return {
            'local_search_stats': self.local_stats.copy(),
            'cache_size': len(self.content_cache),
            'rss_cache_size': len(self.rss_cache),
            'success_rate': (
                self.local_stats['successful_searches'] / 
                max(self.local_stats['total_searches'], 1)
            ),
            'sources_available': {
                'rss_feeds': sum(len(feeds) for feeds in self.rss_sources.values()),
                'search_sites': len(self.search_sites)
            }
        }
    
    async def cleanup_cache(self):
        """تنظيف التخزين المؤقت"""
        try:
            # تنظيف RSS cache القديم
            cutoff_time = datetime.now() - timedelta(hours=1)
            expired_keys = [
                key for key, data in self.rss_cache.items()
                if data['timestamp'] < cutoff_time
            ]
            
            for key in expired_keys:
                del self.rss_cache[key]
            
            logger.debug(f"🧹 تم تنظيف {len(expired_keys)} مدخل من RSS cache")
            
        except Exception as e:
            logger.error(f"❌ فشل في تنظيف التخزين المؤقت: {e}")

# إنشاء مثيل عام
local_search_engine = LocalSearchEngine()
