# نظام Microsoft Clarity المتقدم للتحليلات
import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from .logger import logger

class MicrosoftClarityAnalyzer:
    """محلل Microsoft Clarity المتقدم"""
    
    def __init__(self, clarity_id: str = 'sedxi61jhb'):
        self.clarity_id = clarity_id
        self.base_url = "https://www.clarity.ms/api"
        self.session = None
        
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    def generate_clarity_script(self, website_url: str = "") -> str:
        """توليد كود Microsoft Clarity للموقع"""
        script = f'''
<!-- Microsoft Clarity Analytics -->
<script type="text/javascript">
    (function(c,l,a,r,i,t,y){{
        c[a]=c[a]||function(){{(c[a].q=c[a].q||[]).push(arguments)}};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    }})(window, document, "clarity", "script", "{self.clarity_id}");
</script>

<!-- تتبع أحداث مخصصة للألعاب -->
<script type="text/javascript">
    // تتبع قراءة المقالات
    function trackArticleRead(articleTitle, gameCategory) {{
        if (typeof clarity !== 'undefined') {{
            clarity('set', 'article_read', articleTitle);
            clarity('set', 'game_category', gameCategory);
            clarity('event', 'article_completed');
        }}
    }}
    
    // تتبع البحث عن الألعاب
    function trackGameSearch(searchTerm) {{
        if (typeof clarity !== 'undefined') {{
            clarity('set', 'search_term', searchTerm);
            clarity('event', 'game_search');
        }}
    }}
    
    // تتبع مشاهدة الفيديوهات
    function trackVideoView(videoTitle, gameName) {{
        if (typeof clarity !== 'undefined') {{
            clarity('set', 'video_title', videoTitle);
            clarity('set', 'game_name', gameName);
            clarity('event', 'video_viewed');
        }}
    }}
    
    // تتبع التفاعل مع المراجعات
    function trackReviewInteraction(reviewType, rating) {{
        if (typeof clarity !== 'undefined') {{
            clarity('set', 'review_type', reviewType);
            clarity('set', 'user_rating', rating);
            clarity('event', 'review_interaction');
        }}
    }}
    
    // تتبع تلقائي لسلوك المستخدم
    document.addEventListener('DOMContentLoaded', function() {{
        // تتبع الوقت المقضي في الصفحة
        let startTime = Date.now();
        
        window.addEventListener('beforeunload', function() {{
            let timeSpent = Math.round((Date.now() - startTime) / 1000);
            if (typeof clarity !== 'undefined') {{
                clarity('set', 'time_spent_seconds', timeSpent);
                clarity('event', 'page_exit');
            }}
        }});
        
        // تتبع التمرير
        let maxScroll = 0;
        window.addEventListener('scroll', function() {{
            let scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > maxScroll) {{
                maxScroll = scrollPercent;
                if (typeof clarity !== 'undefined') {{
                    clarity('set', 'max_scroll_percent', maxScroll);
                }}
            }}
        }});
        
        // تتبع النقرات على الروابط الخارجية
        document.querySelectorAll('a[href^="http"]').forEach(function(link) {{
            link.addEventListener('click', function() {{
                if (typeof clarity !== 'undefined') {{
                    clarity('set', 'external_link', this.href);
                    clarity('event', 'external_link_click');
                }}
            }});
        }});
    }});
</script>
'''
        return script
    
    async def get_clarity_insights(self, days: int = 7) -> Dict:
        """الحصول على رؤى Microsoft Clarity"""
        try:
            # محاكاة بيانات Clarity (في التطبيق الحقيقي، استخدم Clarity API)
            insights = await self._simulate_clarity_data(days)
            
            # تحليل البيانات
            analyzed_insights = self._analyze_clarity_data(insights)
            
            return {
                'period_days': days,
                'clarity_id': self.clarity_id,
                'raw_data': insights,
                'analysis': analyzed_insights,
                'recommendations': self._generate_clarity_recommendations(analyzed_insights),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("❌ فشل في الحصول على رؤى Clarity", e)
            return {}
    
    async def _simulate_clarity_data(self, days: int) -> Dict:
        """محاكاة بيانات Microsoft Clarity"""
        import random
        
        # محاكاة بيانات الجلسات
        total_sessions = random.randint(1000, 5000) * days
        
        return {
            'sessions': {
                'total': total_sessions,
                'unique_users': int(total_sessions * 0.7),
                'returning_users': int(total_sessions * 0.3),
                'avg_session_duration': random.randint(120, 300),  # ثواني
                'bounce_rate': round(random.uniform(0.3, 0.6), 2)
            },
            'page_views': {
                'total': total_sessions * random.randint(2, 4),
                'pages_per_session': round(random.uniform(2.1, 3.8), 1),
                'most_viewed_pages': [
                    {'url': '/latest-news', 'views': random.randint(500, 1500)},
                    {'url': '/game-reviews', 'views': random.randint(400, 1200)},
                    {'url': '/minecraft-news', 'views': random.randint(300, 1000)},
                    {'url': '/fortnite-updates', 'views': random.randint(250, 800)}
                ]
            },
            'user_behavior': {
                'scroll_depth': {
                    '25%': random.randint(80, 95),
                    '50%': random.randint(60, 80),
                    '75%': random.randint(40, 60),
                    '100%': random.randint(20, 40)
                },
                'click_patterns': {
                    'navigation_clicks': random.randint(2000, 5000),
                    'content_clicks': random.randint(1500, 4000),
                    'external_links': random.randint(200, 800),
                    'social_shares': random.randint(100, 500)
                },
                'search_behavior': {
                    'internal_searches': random.randint(300, 1000),
                    'top_search_terms': [
                        'minecraft updates',
                        'fortnite news',
                        'gaming reviews',
                        'new games 2025'
                    ]
                }
            },
            'device_analytics': {
                'mobile': round(random.uniform(0.6, 0.8), 2),
                'desktop': round(random.uniform(0.15, 0.3), 2),
                'tablet': round(random.uniform(0.05, 0.15), 2)
            },
            'geographic_data': {
                'top_countries': [
                    {'country': 'Saudi Arabia', 'percentage': random.randint(25, 40)},
                    {'country': 'UAE', 'percentage': random.randint(15, 25)},
                    {'country': 'Egypt', 'percentage': random.randint(10, 20)},
                    {'country': 'Jordan', 'percentage': random.randint(5, 15)}
                ]
            },
            'performance_metrics': {
                'page_load_time': round(random.uniform(1.5, 4.0), 1),
                'time_to_interactive': round(random.uniform(2.0, 5.0), 1),
                'rage_clicks': random.randint(50, 200),
                'dead_clicks': random.randint(30, 150)
            }
        }
    
    def _analyze_clarity_data(self, data: Dict) -> Dict:
        """تحليل بيانات Clarity"""
        try:
            sessions = data.get('sessions', {})
            behavior = data.get('user_behavior', {})
            performance = data.get('performance_metrics', {})
            
            analysis = {
                'user_engagement': self._analyze_engagement(sessions, behavior),
                'content_performance': self._analyze_content_performance(data),
                'technical_issues': self._analyze_technical_issues(performance),
                'user_journey': self._analyze_user_journey(behavior),
                'conversion_insights': self._analyze_conversions(data)
            }
            
            return analysis
            
        except Exception as e:
            logger.error("❌ فشل في تحليل بيانات Clarity", e)
            return {}
    
    def _analyze_engagement(self, sessions: Dict, behavior: Dict) -> Dict:
        """تحليل مستوى التفاعل"""
        avg_duration = sessions.get('avg_session_duration', 0)
        bounce_rate = sessions.get('bounce_rate', 0)
        scroll_depth = behavior.get('scroll_depth', {})
        
        # تحديد مستوى التفاعل
        if avg_duration > 240 and bounce_rate < 0.4:
            engagement_level = 'ممتاز'
        elif avg_duration > 180 and bounce_rate < 0.5:
            engagement_level = 'جيد جداً'
        elif avg_duration > 120 and bounce_rate < 0.6:
            engagement_level = 'جيد'
        else:
            engagement_level = 'يحتاج تحسين'
        
        return {
            'level': engagement_level,
            'avg_session_duration': avg_duration,
            'bounce_rate_percentage': bounce_rate * 100,
            'deep_scroll_rate': scroll_depth.get('75%', 0),
            'engagement_score': self._calculate_engagement_score(avg_duration, bounce_rate, scroll_depth)
        }
    
    def _calculate_engagement_score(self, duration: int, bounce_rate: float, scroll_depth: Dict) -> float:
        """حساب نقاط التفاعل"""
        # نقاط المدة (0-40)
        duration_score = min(40, (duration / 300) * 40)
        
        # نقاط معدل الارتداد (0-30)
        bounce_score = max(0, 30 - (bounce_rate * 50))
        
        # نقاط عمق التمرير (0-30)
        scroll_score = (scroll_depth.get('75%', 0) / 100) * 30
        
        total_score = duration_score + bounce_score + scroll_score
        return round(total_score, 1)
    
    def _analyze_content_performance(self, data: Dict) -> Dict:
        """تحليل أداء المحتوى"""
        page_views = data.get('page_views', {})
        most_viewed = page_views.get('most_viewed_pages', [])
        
        # تحديد أفضل المحتوى
        top_content = sorted(most_viewed, key=lambda x: x['views'], reverse=True)[:3]
        
        return {
            'top_performing_content': top_content,
            'pages_per_session': page_views.get('pages_per_session', 0),
            'content_engagement_rate': self._calculate_content_engagement(page_views),
            'content_categories_performance': self._analyze_content_categories(most_viewed)
        }
    
    def _calculate_content_engagement(self, page_views: Dict) -> float:
        """حساب معدل تفاعل المحتوى"""
        pages_per_session = page_views.get('pages_per_session', 0)
        
        if pages_per_session >= 3.5:
            return 90.0
        elif pages_per_session >= 2.5:
            return 75.0
        elif pages_per_session >= 2.0:
            return 60.0
        else:
            return 40.0
    
    def _analyze_content_categories(self, pages: List[Dict]) -> Dict:
        """تحليل فئات المحتوى"""
        categories = {
            'news': 0,
            'reviews': 0,
            'guides': 0,
            'updates': 0
        }
        
        for page in pages:
            url = page['url'].lower()
            views = page['views']
            
            if 'news' in url:
                categories['news'] += views
            elif 'review' in url:
                categories['reviews'] += views
            elif 'guide' in url:
                categories['guides'] += views
            elif 'update' in url:
                categories['updates'] += views
        
        # تحديد الفئة الأكثر شعبية
        top_category = max(categories, key=categories.get)
        
        return {
            'category_performance': categories,
            'top_category': top_category,
            'category_distribution': self._calculate_category_distribution(categories)
        }
    
    def _calculate_category_distribution(self, categories: Dict) -> Dict:
        """حساب توزيع الفئات"""
        total_views = sum(categories.values())
        if total_views == 0:
            return categories
        
        return {cat: round((views / total_views) * 100, 1) for cat, views in categories.items()}
    
    def _analyze_technical_issues(self, performance: Dict) -> Dict:
        """تحليل المشاكل التقنية"""
        load_time = performance.get('page_load_time', 0)
        rage_clicks = performance.get('rage_clicks', 0)
        dead_clicks = performance.get('dead_clicks', 0)
        
        issues = []
        severity_score = 0
        
        # فحص سرعة التحميل
        if load_time > 3.0:
            issues.append('سرعة تحميل بطيئة')
            severity_score += 30
        
        # فحص النقرات الغاضبة
        if rage_clicks > 100:
            issues.append('نقرات غاضبة عالية')
            severity_score += 25
        
        # فحص النقرات الميتة
        if dead_clicks > 100:
            issues.append('نقرات ميتة عالية')
            severity_score += 20
        
        return {
            'issues_detected': issues,
            'severity_score': severity_score,
            'performance_grade': self._get_performance_grade(severity_score),
            'load_time': load_time,
            'user_frustration_indicators': {
                'rage_clicks': rage_clicks,
                'dead_clicks': dead_clicks
            }
        }
    
    def _get_performance_grade(self, severity_score: int) -> str:
        """تحديد درجة الأداء"""
        if severity_score <= 20:
            return 'A - ممتاز'
        elif severity_score <= 40:
            return 'B - جيد'
        elif severity_score <= 60:
            return 'C - مقبول'
        else:
            return 'D - يحتاج تحسين عاجل'
    
    def _analyze_user_journey(self, behavior: Dict) -> Dict:
        """تحليل رحلة المستخدم"""
        search_data = behavior.get('search_behavior', {})
        click_patterns = behavior.get('click_patterns', {})
        
        return {
            'search_engagement': search_data.get('internal_searches', 0),
            'navigation_efficiency': self._calculate_navigation_efficiency(click_patterns),
            'content_discovery_rate': self._calculate_discovery_rate(click_patterns),
            'user_flow_quality': self._assess_user_flow(behavior)
        }
    
    def _calculate_navigation_efficiency(self, clicks: Dict) -> float:
        """حساب كفاءة التنقل"""
        nav_clicks = clicks.get('navigation_clicks', 0)
        content_clicks = clicks.get('content_clicks', 0)
        total_clicks = nav_clicks + content_clicks
        
        if total_clicks == 0:
            return 0.0
        
        # كلما زادت نسبة النقرات على المحتوى، كانت الكفاءة أفضل
        efficiency = (content_clicks / total_clicks) * 100
        return round(efficiency, 1)
    
    def _calculate_discovery_rate(self, clicks: Dict) -> float:
        """حساب معدل اكتشاف المحتوى"""
        content_clicks = clicks.get('content_clicks', 0)
        external_clicks = clicks.get('external_links', 0)
        
        if content_clicks == 0:
            return 0.0
        
        # معدل البقاء في الموقع مقابل الخروج
        discovery_rate = (content_clicks / (content_clicks + external_clicks)) * 100
        return round(discovery_rate, 1)
    
    def _assess_user_flow(self, behavior: Dict) -> str:
        """تقييم تدفق المستخدم"""
        scroll_depth = behavior.get('scroll_depth', {})
        deep_scroll = scroll_depth.get('75%', 0)
        
        if deep_scroll > 60:
            return 'ممتاز - المستخدمون يتفاعلون بعمق'
        elif deep_scroll > 40:
            return 'جيد - تفاعل متوسط'
        else:
            return 'يحتاج تحسين - تفاعل سطحي'
    
    def _analyze_conversions(self, data: Dict) -> Dict:
        """تحليل التحويلات"""
        behavior = data.get('user_behavior', {})
        sessions = data.get('sessions', {})
        
        # محاكاة معدلات التحويل
        social_shares = behavior.get('click_patterns', {}).get('social_shares', 0)
        total_sessions = sessions.get('total', 1)
        
        conversion_rate = (social_shares / total_sessions) * 100
        
        return {
            'social_sharing_rate': round(conversion_rate, 2),
            'engagement_conversion': self._calculate_engagement_conversion(behavior),
            'content_conversion_funnel': self._analyze_conversion_funnel(data)
        }
    
    def _calculate_engagement_conversion(self, behavior: Dict) -> Dict:
        """حساب تحويل التفاعل"""
        clicks = behavior.get('click_patterns', {})
        
        return {
            'click_to_share_rate': round((clicks.get('social_shares', 0) / max(1, clicks.get('content_clicks', 1))) * 100, 2),
            'internal_search_rate': round((behavior.get('search_behavior', {}).get('internal_searches', 0) / max(1, clicks.get('navigation_clicks', 1))) * 100, 2)
        }
    
    def _analyze_conversion_funnel(self, data: Dict) -> List[Dict]:
        """تحليل قمع التحويل"""
        sessions = data.get('sessions', {})
        behavior = data.get('user_behavior', {})
        
        total_sessions = sessions.get('total', 1)
        
        funnel_steps = [
            {
                'step': 'زيارة الموقع',
                'users': total_sessions,
                'conversion_rate': 100.0
            },
            {
                'step': 'تصفح المحتوى',
                'users': int(total_sessions * 0.8),
                'conversion_rate': 80.0
            },
            {
                'step': 'قراءة مقال كامل',
                'users': int(total_sessions * 0.5),
                'conversion_rate': 50.0
            },
            {
                'step': 'تفاعل (مشاركة/تعليق)',
                'users': behavior.get('click_patterns', {}).get('social_shares', 0),
                'conversion_rate': round((behavior.get('click_patterns', {}).get('social_shares', 0) / total_sessions) * 100, 1)
            }
        ]
        
        return funnel_steps
    
    def _generate_clarity_recommendations(self, analysis: Dict) -> List[Dict]:
        """توليد توصيات بناءً على تحليل Clarity"""
        recommendations = []
        
        # توصيات التفاعل
        engagement = analysis.get('user_engagement', {})
        if engagement.get('engagement_score', 0) < 60:
            recommendations.append({
                'category': 'تحسين التفاعل',
                'priority': 'عالية',
                'action': 'تحسين جودة المحتوى وسرعة التحميل',
                'expected_impact': 'زيادة وقت البقاء بنسبة 25%'
            })
        
        # توصيات المحتوى
        content = analysis.get('content_performance', {})
        if content.get('content_engagement_rate', 0) < 70:
            recommendations.append({
                'category': 'تحسين المحتوى',
                'priority': 'متوسطة',
                'action': 'إنشاء محتوى أكثر تفاعلية وروابط داخلية',
                'expected_impact': 'زيادة الصفحات لكل جلسة بنسبة 30%'
            })
        
        # توصيات تقنية
        technical = analysis.get('technical_issues', {})
        if technical.get('severity_score', 0) > 40:
            recommendations.append({
                'category': 'تحسين تقني',
                'priority': 'عالية',
                'action': 'تحسين سرعة التحميل وإصلاح مشاكل التفاعل',
                'expected_impact': 'تقليل معدل الارتداد بنسبة 20%'
            })
        
        # توصيات رحلة المستخدم
        journey = analysis.get('user_journey', {})
        if journey.get('navigation_efficiency', 0) < 60:
            recommendations.append({
                'category': 'تحسين التنقل',
                'priority': 'متوسطة',
                'action': 'تحسين قائمة التنقل وتنظيم المحتوى',
                'expected_impact': 'تحسين تجربة المستخدم بنسبة 35%'
            })
        
        return recommendations

# إنشاء مثيل عام لمحلل Clarity
clarity_analyzer = MicrosoftClarityAnalyzer()
