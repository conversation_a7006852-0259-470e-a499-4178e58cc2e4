#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطية البسيط
"""

import os
import shutil
import sqlite3
import json
from datetime import datetime
from typing import Dict, List

class SimpleBackupSystem:
    """نظام نسخ احتياطية بسيط"""
    
    def __init__(self):
        self.backup_dir = "backups"
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(f"{self.backup_dir}/daily", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/weekly", exist_ok=True)
    
    def create_database_backup(self) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"articles_backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, "daily", backup_filename)
            
            # نسخ قاعدة البيانات
            if os.path.exists("data/articles.db"):
                shutil.copy2("data/articles.db", backup_path)
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
                
                # حفظ معلومات النسخة الاحتياطية
                self._save_backup_info(backup_filename, "database")
                
                return True
            else:
                print("⚠️ قاعدة البيانات غير موجودة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def create_config_backup(self) -> bool:
        """إنشاء نسخة احتياطية من ملفات التكوين"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"config_backup_{timestamp}.json"
            backup_path = os.path.join(self.backup_dir, "daily", backup_filename)
            
            config_data = {}
            
            # نسخ ملفات التكوين المهمة
            config_files = [
                "config/settings.py",
                ".env",
                "config/api_config.py"
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data[config_file] = f.read()
            
            # حفظ النسخة الاحتياطية
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم إنشاء نسخة احتياطية للتكوين: {backup_filename}")
            self._save_backup_info(backup_filename, "config")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نسخ التكوين: {e}")
            return False
    
    def _save_backup_info(self, filename: str, backup_type: str):
        """حفظ معلومات النسخة الاحتياطية"""
        try:
            info_file = os.path.join(self.backup_dir, "backup_log.json")
            
            backup_info = {
                'filename': filename,
                'type': backup_type,
                'created_at': datetime.now().isoformat(),
                'size': os.path.getsize(os.path.join(self.backup_dir, "daily", filename))
            }
            
            # قراءة السجل الحالي
            backup_log = []
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_log = json.load(f)
            
            # إضافة النسخة الجديدة
            backup_log.append(backup_info)
            
            # الاحتفاظ بآخر 50 نسخة فقط
            backup_log = backup_log[-50:]
            
            # حفظ السجل المحدث
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_log, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️ خطأ في حفظ معلومات النسخة الاحتياطية: {e}")
    
    def cleanup_old_backups(self, days: int = 7):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for backup_dir in ["daily", "weekly"]:
                backup_path = os.path.join(self.backup_dir, backup_dir)
                
                if os.path.exists(backup_path):
                    for filename in os.listdir(backup_path):
                        file_path = os.path.join(backup_path, filename)
                        
                        # فحص تاريخ الملف
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        
                        if file_time < cutoff_date:
                            os.remove(file_path)
                            print(f"🗑️ تم حذف النسخة القديمة: {filename}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تنظيف النسخ القديمة: {e}")
            return False
    
    def create_full_backup(self) -> bool:
        """إنشاء نسخة احتياطية كاملة"""
        print("💾 إنشاء نسخة احتياطية كاملة...")
        
        database_success = self.create_database_backup()
        config_success = self.create_config_backup()
        
        if database_success and config_success:
            print("✅ تم إنشاء النسخة الاحتياطية الكاملة بنجاح")
            return True
        else:
            print("⚠️ تم إنشاء نسخة احتياطية جزئية")
            return False

# إنشاء مثيل عام لنظام النسخ الاحتياطية
simple_backup = SimpleBackupSystem()
