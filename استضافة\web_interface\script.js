// إعدادات عامة
const API_BASE_URL = 'http://localhost:5000/api';
let agentStatus = 'offline';
let updateInterval;
let performanceChart;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    startStatusUpdates();
    initializeChart();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('🚀 تهيئة لوحة تحكم وكيل أخبار الألعاب');
    loadSettings();
    updateStatus();
    loadContent();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار التحكم الرئيسية
    document.getElementById('toggleAgent').addEventListener('click', toggleAgent);
    document.getElementById('startCycle').addEventListener('click', startCycle);
    document.getElementById('pauseAgent').addEventListener('click', pauseAgent);
    document.getElementById('stopAgent').addEventListener('click', stopAgent);
    document.getElementById('viewLogs').addEventListener('click', viewLogs);

    // أزرار التحديث
    document.getElementById('refreshContent').addEventListener('click', loadContent);

    // أزرار الإعدادات
    document.getElementById('saveSettings').addEventListener('click', saveSettings);

    // التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            switchTab(this.dataset.tab);
        });
    });

    // إغلاق النافذة المنبثقة
    document.getElementById('closeLogsModal').addEventListener('click', closeLogs);

    // إغلاق النافذة عند النقر خارجها
    document.getElementById('logsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLogs();
        }
    });
}

// تبديل حالة الوكيل
async function toggleAgent() {
    const btn = document.getElementById('toggleAgent');
    const icon = btn.querySelector('i');
    const text = btn.querySelector('span') || btn;

    try {
        btn.disabled = true;

        if (agentStatus === 'offline') {
            // تشغيل الوكيل
            icon.className = 'fas fa-spinner fa-spin';
            text.textContent = 'جاري التشغيل...';

            const response = await fetch(`${API_BASE_URL}/agent/start`, {
                method: 'POST'
            });

            if (response.ok) {
                agentStatus = 'online';
                icon.className = 'fas fa-stop';
                text.textContent = 'إيقاف الوكيل';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-danger');
                showNotification('تم تشغيل الوكيل بنجاح', 'success');
            } else {
                throw new Error('فشل في تشغيل الوكيل');
            }
        } else {
            // إيقاف الوكيل
            icon.className = 'fas fa-spinner fa-spin';
            text.textContent = 'جاري الإيقاف...';

            const response = await fetch(`${API_BASE_URL}/agent/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                agentStatus = 'offline';
                icon.className = 'fas fa-play';
                text.textContent = 'تشغيل الوكيل';
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-primary');
                showNotification('تم إيقاف الوكيل بنجاح', 'success');
            } else {
                throw new Error('فشل في إيقاف الوكيل');
            }
        }
    } catch (error) {
        console.error('خطأ في تبديل حالة الوكيل:', error);
        showNotification('خطأ في تبديل حالة الوكيل: ' + error.message, 'error');

        // إعادة تعيين الزر
        if (agentStatus === 'offline') {
            icon.className = 'fas fa-play';
            text.textContent = 'تشغيل الوكيل';
        } else {
            icon.className = 'fas fa-stop';
            text.textContent = 'إيقاف الوكيل';
        }
    } finally {
        btn.disabled = false;
    }
}

// بدء دورة جديدة
async function startCycle() {
    try {
        const response = await fetch(`${API_BASE_URL}/agent/start-cycle`, {
            method: 'POST'
        });

        if (response.ok) {
            showNotification('تم بدء دورة جديدة', 'success');
            updateActivity('بدء دورة جديدة للبحث عن المحتوى');
        } else {
            throw new Error('فشل في بدء الدورة');
        }
    } catch (error) {
        console.error('خطأ في بدء الدورة:', error);
        showNotification('خطأ في بدء الدورة: ' + error.message, 'error');
    }
}

// إيقاف مؤقت
async function pauseAgent() {
    try {
        const response = await fetch(`${API_BASE_URL}/agent/pause`, {
            method: 'POST'
        });

        if (response.ok) {
            showNotification('تم إيقاف الوكيل مؤقتاً', 'warning');
            updateActivity('تم إيقاف الوكيل مؤقتاً');
        } else {
            throw new Error('فشل في الإيقاف المؤقت');
        }
    } catch (error) {
        console.error('خطأ في الإيقاف المؤقت:', error);
        showNotification('خطأ في الإيقاف المؤقت: ' + error.message, 'error');
    }
}

// إيقاف كامل
async function stopAgent() {
    if (confirm('هل أنت متأكد من إيقاف الوكيل نهائياً؟')) {
        try {
            const response = await fetch(`${API_BASE_URL}/agent/stop`, {
                method: 'POST'
            });

            if (response.ok) {
                agentStatus = 'offline';
                updateStatusDisplay();
                showNotification('تم إيقاف الوكيل نهائياً', 'success');
                updateActivity('تم إيقاف الوكيل نهائياً');
            } else {
                throw new Error('فشل في الإيقاف');
            }
        } catch (error) {
            console.error('خطأ في الإيقاف:', error);
            showNotification('خطأ في الإيقاف: ' + error.message, 'error');
        }
    }
}

// عرض السجلات
async function viewLogs() {
    const modal = document.getElementById('logsModal');
    const container = document.getElementById('logsContainer');

    modal.classList.add('active');
    container.innerHTML = '<div class="loading">جاري تحميل السجلات...</div>';

    try {
        const response = await fetch(`${API_BASE_URL}/logs`);
        if (response.ok) {
            const logs = await response.text();
            container.innerHTML = `<pre>${logs}</pre>`;
        } else {
            throw new Error('فشل في تحميل السجلات');
        }
    } catch (error) {
        console.error('خطأ في تحميل السجلات:', error);
        container.innerHTML = `<div class="error">خطأ في تحميل السجلات: ${error.message}</div>`;
    }
}

// إغلاق نافذة السجلات
function closeLogs() {
    document.getElementById('logsModal').classList.remove('active');
}

// تحديث الحالة
async function updateStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/status`);
        if (response.ok) {
            const status = await response.json();
            updateStatusDisplay(status);
            updateStats(status.stats);
            if (status.activity) {
                updateActivity(status.activity);
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث الحالة:', error);
        // في حالة عدم توفر الخادم، نعرض حالة غير متصل
        updateStatusDisplay({ status: 'offline' });
    }
}

// تحديث عرض الحالة
function updateStatusDisplay(status = null) {
    const statusElement = document.getElementById('agentStatus');
    const dot = statusElement.querySelector('.status-dot');
    const text = statusElement.querySelector('.status-text');
    const toggleBtn = document.getElementById('toggleAgent');

    if (status) {
        agentStatus = status.status;
    }

    // تحديث النقطة والنص
    dot.className = 'status-dot';
    switch (agentStatus) {
        case 'online':
            dot.classList.add('online');
            text.textContent = 'متصل ويعمل';
            toggleBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف الوكيل';
            toggleBtn.className = 'btn btn-danger';
            break;
        case 'processing':
            dot.classList.add('processing');
            text.textContent = 'يعالج المحتوى';
            toggleBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف الوكيل';
            toggleBtn.className = 'btn btn-danger';
            break;
        case 'paused':
            dot.classList.add('processing');
            text.textContent = 'متوقف مؤقتاً';
            toggleBtn.innerHTML = '<i class="fas fa-play"></i> تشغيل الوكيل';
            toggleBtn.className = 'btn btn-primary';
            break;
        default:
            dot.classList.add('offline');
            text.textContent = 'غير متصل';
            toggleBtn.innerHTML = '<i class="fas fa-play"></i> تشغيل الوكيل';
            toggleBtn.className = 'btn btn-primary';
    }
}

// تحديث الإحصائيات
function updateStats(stats) {
    if (!stats) return;

    document.getElementById('articlesPublished').textContent = stats.articlesPublished || 0;
    document.getElementById('contentProcessed').textContent = stats.contentProcessed || 0;
    document.getElementById('successRate').textContent = (stats.successRate || 0) + '%';
    document.getElementById('uptime').textContent = formatUptime(stats.uptime || 0);
}

// تنسيق وقت التشغيل
function formatUptime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// تحديث سجل النشاط
function updateActivity(activity) {
    const log = document.getElementById('activityLog');
    const time = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const item = document.createElement('div');
    item.className = 'activity-item';
    item.innerHTML = `
        <span class="activity-time">${time}</span>
        <span class="activity-text">${activity}</span>
    `;

    log.insertBefore(item, log.firstChild);

    // الاحتفاظ بآخر 10 عناصر فقط
    while (log.children.length > 10) {
        log.removeChild(log.lastChild);
    }
}

// بدء تحديثات الحالة التلقائية
function startStatusUpdates() {
    updateInterval = setInterval(updateStatus, 5000); // كل 5 ثوان
}

// تحميل المحتوى
async function loadContent() {
    const container = document.getElementById('contentList');
    container.innerHTML = '<div class="loading">جاري تحميل المحتوى...</div>';

    try {
        const response = await fetch(`${API_BASE_URL}/content`);
        if (response.ok) {
            const content = await response.json();
            displayContent(content);
        } else {
            throw new Error('فشل في تحميل المحتوى');
        }
    } catch (error) {
        console.error('خطأ في تحميل المحتوى:', error);
        container.innerHTML = `<div class="error">خطأ في تحميل المحتوى: ${error.message}</div>`;
    }
}

// عرض المحتوى
function displayContent(content) {
    const container = document.getElementById('contentList');

    if (!content || content.length === 0) {
        container.innerHTML = '<div class="no-content">لا يوجد محتوى منشور بعد</div>';
        return;
    }

    const html = content.map(item => `
        <div class="content-item">
            <div class="content-title">${item.title}</div>
            <div class="content-meta">
                <span>تاريخ النشر: ${new Date(item.published_date).toLocaleDateString('ar-SA')}</span>
                <span>المصدر: ${item.source || 'غير محدد'}</span>
            </div>
            ${item.blogger_url ? `<a href="${item.blogger_url}" target="_blank" class="btn btn-info btn-sm">عرض المقال</a>` : ''}
        </div>
    `).join('');

    container.innerHTML = html;
}

// تبديل التبويبات
function switchTab(tabName) {
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // إضافة الفئة النشطة للتبويب المحدد
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // تحميل محتوى التبويب حسب الحاجة
    switch (tabName) {
        case 'content':
            loadContent();
            break;
        case 'approval':
            loadPendingApprovals();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

// تحميل الموافقات المعلقة
async function loadPendingApprovals() {
    const container = document.getElementById('approvalList');
    container.innerHTML = '<div class="loading">جاري تحميل الموافقات المعلقة...</div>';

    try {
        const response = await fetch(`${API_BASE_URL}/approvals/pending`);
        if (response.ok) {
            const approvals = await response.json();
            displayPendingApprovals(approvals);
        } else {
            throw new Error('فشل في تحميل الموافقات');
        }
    } catch (error) {
        console.error('خطأ في تحميل الموافقات:', error);
        container.innerHTML = '<div class="no-pending">لا يوجد محتوى في انتظار الموافقة</div>';
    }
}

// عرض الموافقات المعلقة
function displayPendingApprovals(approvals) {
    const container = document.getElementById('approvalList');

    if (!approvals || approvals.length === 0) {
        container.innerHTML = '<div class="no-pending">لا يوجد محتوى في انتظار الموافقة</div>';
        return;
    }

    const html = approvals.map(item => `
        <div class="approval-item">
            <div class="approval-content">
                <h4>${item.title}</h4>
                <p>${item.description}</p>
                <div class="approval-meta">
                    <span>النوع: ${item.type}</span>
                    <span>المصدر: ${item.source}</span>
                </div>
            </div>
            <div class="approval-actions">
                <button class="btn btn-success" onclick="approveContent('${item.id}')">
                    <i class="fas fa-check"></i> موافقة
                </button>
                <button class="btn btn-danger" onclick="rejectContent('${item.id}')">
                    <i class="fas fa-times"></i> رفض
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// الموافقة على المحتوى
async function approveContent(id) {
    try {
        const response = await fetch(`${API_BASE_URL}/approvals/${id}/approve`, {
            method: 'POST'
        });

        if (response.ok) {
            showNotification('تمت الموافقة على المحتوى', 'success');
            loadPendingApprovals(); // إعادة تحميل القائمة
        } else {
            throw new Error('فشل في الموافقة');
        }
    } catch (error) {
        console.error('خطأ في الموافقة:', error);
        showNotification('خطأ في الموافقة: ' + error.message, 'error');
    }
}

// رفض المحتوى
async function rejectContent(id) {
    try {
        const response = await fetch(`${API_BASE_URL}/approvals/${id}/reject`, {
            method: 'POST'
        });

        if (response.ok) {
            showNotification('تم رفض المحتوى', 'warning');
            loadPendingApprovals(); // إعادة تحميل القائمة
        } else {
            throw new Error('فشل في الرفض');
        }
    } catch (error) {
        console.error('خطأ في الرفض:', error);
        showNotification('خطأ في الرفض: ' + error.message, 'error');
    }
}

// تحميل الإعدادات
function loadSettings() {
    // تحميل الإعدادات من localStorage أو من الخادم
    const settings = JSON.parse(localStorage.getItem('agentSettings')) || {};

    document.getElementById('searchInterval').value = settings.searchInterval || 2;
    document.getElementById('articlesPerCycle').value = settings.articlesPerCycle || 5;
    document.getElementById('preferredDialect').value = settings.preferredDialect || 'standard';
    document.getElementById('autoApproval').checked = settings.autoApproval || false;
}

// حفظ الإعدادات
async function saveSettings() {
    const settings = {
        searchInterval: parseInt(document.getElementById('searchInterval').value),
        articlesPerCycle: parseInt(document.getElementById('articlesPerCycle').value),
        preferredDialect: document.getElementById('preferredDialect').value,
        autoApproval: document.getElementById('autoApproval').checked
    };

    try {
        const response = await fetch(`${API_BASE_URL}/settings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });

        if (response.ok) {
            localStorage.setItem('agentSettings', JSON.stringify(settings));
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } else {
            throw new Error('فشل في حفظ الإعدادات');
        }
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        showNotification('خطأ في حفظ الإعدادات: ' + error.message, 'error');
    }
}

// تحميل التحليلات
async function loadAnalytics() {
    try {
        const response = await fetch(`${API_BASE_URL}/analytics`);
        if (response.ok) {
            const analytics = await response.json();
            updateAnalytics(analytics);
            updateChart(analytics.chartData);
        } else {
            throw new Error('فشل في تحميل التحليلات');
        }
    } catch (error) {
        console.error('خطأ في تحميل التحليلات:', error);
    }
}

// تحديث التحليلات
function updateAnalytics(analytics) {
    document.getElementById('totalArticles').textContent = analytics.totalArticles || 0;
    document.getElementById('dailyRate').textContent = analytics.dailyRate || 0;
    document.getElementById('topSource').textContent = analytics.topSource || '--';
}

// تهيئة الرسم البياني
function initializeChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'المقالات المنشورة',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: true
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// تحديث الرسم البياني
function updateChart(data) {
    if (performanceChart && data) {
        performanceChart.data.labels = data.labels;
        performanceChart.data.datasets[0].data = data.values;
        performanceChart.update();
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const container = document.getElementById('notifications');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    container.appendChild(notification);

    // إزالة الإشعار بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});