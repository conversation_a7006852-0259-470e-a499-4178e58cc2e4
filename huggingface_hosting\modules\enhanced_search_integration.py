# تكامل نظام البحث المحسن
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import traceback

from .logger import logger
from .smart_search_manager import smart_search_manager, SearchRequest, SearchPriority
from .rate_limit_manager import rate_limit_manager
from .search_analytics import search_analytics, SearchAnalytics
from .advanced_cache_system import advanced_cache

class EnhancedSearchIntegration:
    """تكامل نظام البحث المحسن"""
    
    def __init__(self):
        self.enabled = True
        self.fallback_enabled = True
        
        # إعدادات التكامل - Tavily كأولوية أولى
        self.settings = {
            'max_retries': 3,
            'retry_delay': 2.0,
            'quality_threshold': 60.0,
            'cost_threshold_daily': 10.0,
            'emergency_mode_threshold': 5,  # عدد الفشل المتتالي قبل تفعيل وضع الطوارئ
            'tavily_first_priority': True,  # Tavily له الأولوية الأولى دائماً
            'default_priority': 'free'  # يبدأ بـ Tavily (مجاني وقوي)
        }
        
        # تتبع الحالة
        self.state = {
            'consecutive_failures': 0,
            'emergency_mode': False,
            'last_successful_search': time.time(),
            'daily_cost': 0.0
        }
        
        logger.info("🔗 تم تهيئة تكامل نظام البحث المحسن")
    
    async def enhanced_search(self, 
                            query: str, 
                            max_results: int = 10,
                            search_type: str = "gaming_news",
                            priority: str = "auto") -> List[Dict]:
        """البحث المحسن الرئيسي - Tavily كأولوية أولى"""
        
        search_start_time = time.time()
        
        try:
            logger.info(f"🔍 بدء البحث المحسن (Tavily أولاً): '{query}' ({max_results} نتائج)")
            
            # 1. تحديد الأولوية التلقائية
            search_priority = self._determine_priority(priority, query)
            
            # 2. إنشاء طلب البحث
            search_request = SearchRequest(
                query=query,
                max_results=max_results,
                priority=search_priority,
                search_type=search_type,
                cache_duration=self._calculate_cache_duration(search_type)
            )
            
            # 3. فحص حدود التكلفة
            if not await self._check_cost_limits():
                logger.warning("⚠️ تم تجاوز حدود التكلفة اليومية - استخدام البحث المجاني فقط")
                search_request.priority = SearchPriority.FREE
            
            # 4. تنفيذ البحث مع إعادة المحاولة
            results = await self._execute_search_with_retry(search_request)
            
            # 5. تحليل جودة النتائج
            analyzed_results = await self._analyze_results_quality(results, query)
            
            # 6. تسجيل التحليلات
            await self._record_search_analytics(
                search_request, analyzed_results, search_start_time, True
            )
            
            # 7. تحديث الحالة
            self._update_search_state(True)
            
            logger.info(f"✅ البحث المحسن اكتمل: {len(analyzed_results)} نتيجة عالية الجودة")
            return analyzed_results
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث المحسن: {e}")
            
            # تسجيل الفشل
            await self._record_search_analytics(
                SearchRequest(query=query, max_results=max_results), 
                [], search_start_time, False, str(e)
            )
            
            # تحديث الحالة
            self._update_search_state(False)
            
            # محاولة البحث الاحتياطي
            if self.fallback_enabled:
                return await self._fallback_search(query, max_results)
            
            return []
    
    def _determine_priority(self, priority: str, query: str) -> SearchPriority:
        """تحديد أولوية البحث - Tavily (FREE) كافتراضي"""
        try:
            # أولوية محددة مسبقاً
            if priority != "auto":
                priority_map = {
                    "free": SearchPriority.FREE,        # Tavily (أولوية أولى)
                    "low_cost": SearchPriority.LOW_COST, # Google (بديل)
                    "premium": SearchPriority.PREMIUM,   # SerpAPI (متقدم)
                    "emergency": SearchPriority.EMERGENCY
                }
                return priority_map.get(priority, SearchPriority.FREE)

            # تحديد تلقائي بناءً على عوامل مختلفة - Tavily أولاً

            # وضع الطوارئ
            if self.state['emergency_mode']:
                return SearchPriority.EMERGENCY

            # استعلامات مهمة جداً (تحتاج SerpAPI)
            critical_keywords = [
                'breaking news', 'urgent update', 'exclusive reveal',
                'major announcement', 'industry breaking'
            ]

            if any(keyword in query.lower() for keyword in critical_keywords):
                return SearchPriority.PREMIUM

            # استعلامات مهمة (تحتاج Google كبديل)
            important_keywords = [
                'announcement', 'release', 'update', 'new', 'latest'
            ]

            if any(keyword in query.lower() for keyword in important_keywords):
                return SearchPriority.LOW_COST

            # افتراضي - Tavily (مجاني وقوي)
            return SearchPriority.FREE

        except Exception as e:
            logger.error(f"❌ فشل في تحديد الأولوية: {e}")
            return SearchPriority.FREE  # Tavily كافتراضي حتى في حالة الخطأ
    
    def _calculate_cache_duration(self, search_type: str) -> int:
        """حساب مدة التخزين المؤقت"""
        cache_durations = {
            'gaming_news': 1800,    # 30 دقيقة للأخبار
            'general': 3600,        # ساعة واحدة للبحث العام
            'trending': 900,        # 15 دقيقة للمحتوى الرائج
            'historical': 7200      # ساعتين للمحتوى التاريخي
        }
        
        return cache_durations.get(search_type, 3600)
    
    async def _check_cost_limits(self) -> bool:
        """فحص حدود التكلفة"""
        try:
            # الحصول على إحصائيات اليوم
            stats = search_analytics.get_performance_metrics(24)
            
            if stats.total_cost >= self.settings['cost_threshold_daily']:
                logger.warning(f"⚠️ تم تجاوز حد التكلفة اليومي: ${stats.total_cost:.2f}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص حدود التكلفة: {e}")
            return True  # السماح بالمتابعة في حالة الخطأ
    
    async def _execute_search_with_retry(self, request: SearchRequest) -> List[Dict]:
        """تنفيذ البحث مع إعادة المحاولة"""
        last_error = None
        
        for attempt in range(self.settings['max_retries']):
            try:
                logger.debug(f"🔄 محاولة البحث {attempt + 1}/{self.settings['max_retries']}")
                
                # تنفيذ البحث
                results = await smart_search_manager.search(request)
                
                if results:
                    return results
                
                # لا توجد نتائج - تجربة أولوية أعلى
                if attempt < self.settings['max_retries'] - 1:
                    if request.priority.value < SearchPriority.PREMIUM.value:
                        request.priority = SearchPriority(request.priority.value + 1)
                        logger.info(f"📈 رفع الأولوية إلى {request.priority.name}")
                
            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ فشل في المحاولة {attempt + 1}: {e}")
                
                if attempt < self.settings['max_retries'] - 1:
                    await asyncio.sleep(self.settings['retry_delay'] * (attempt + 1))
        
        # جميع المحاولات فشلت
        if last_error:
            raise last_error
        else:
            raise Exception("لم يتم العثور على نتائج بعد جميع المحاولات")
    
    async def _analyze_results_quality(self, results: List[Dict], query: str) -> List[Dict]:
        """تحليل جودة النتائج وتصفيتها"""
        try:
            if not results:
                return []
            
            high_quality_results = []
            
            for result in results:
                # حساب نقاط الجودة الإجمالية
                quality_score = result.get('quality_score', 0)
                relevance_score = result.get('relevance_score', 0)
                
                # نقاط إضافية للمصادر الموثوقة
                trusted_bonus = self._calculate_trust_bonus(result.get('source', ''))
                
                # النقاط الإجمالية
                total_score = (quality_score * 0.5) + (relevance_score * 0.4) + (trusted_bonus * 0.1)
                
                # تحديث النقاط في النتيجة
                result['total_quality_score'] = total_score
                
                # فلترة النتائج عالية الجودة
                if total_score >= self.settings['quality_threshold']:
                    high_quality_results.append(result)
            
            # ترتيب النتائج حسب الجودة
            high_quality_results.sort(
                key=lambda x: x.get('total_quality_score', 0), 
                reverse=True
            )
            
            logger.info(f"📊 تم فلترة {len(high_quality_results)} نتيجة عالية الجودة من {len(results)}")
            return high_quality_results
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل جودة النتائج: {e}")
            return results  # إرجاع النتائج الأصلية في حالة الخطأ
    
    def _calculate_trust_bonus(self, source: str) -> float:
        """حساب نقاط إضافية للمصادر الموثوقة"""
        trusted_sources = {
            'ign.com': 10.0,
            'gamespot.com': 9.0,
            'polygon.com': 8.0,
            'kotaku.com': 7.0,
            'pcgamer.com': 8.0,
            'eurogamer.net': 8.0,
            'destructoid.com': 6.0,
            'gamasutra.com': 9.0,
            'rockpapershotgun.com': 7.0
        }
        
        source_lower = source.lower()
        for trusted_source, bonus in trusted_sources.items():
            if trusted_source in source_lower:
                return bonus
        
        return 0.0
    
    async def _record_search_analytics(self, 
                                     request: SearchRequest, 
                                     results: List[Dict], 
                                     start_time: float,
                                     success: bool,
                                     error_message: str = ""):
        """تسجيل تحليلات البحث"""
        try:
            response_time = time.time() - start_time
            
            # حساب متوسط جودة النتائج
            avg_quality = 0.0
            if results:
                quality_scores = [r.get('total_quality_score', 0) for r in results]
                avg_quality = sum(quality_scores) / len(quality_scores)
            
            # تقدير التكلفة (بناءً على الأولوية ومحرك البحث)
            estimated_cost = self._estimate_search_cost(request, len(results))
            
            # إنشاء سجل التحليلات
            analytics = SearchAnalytics(
                query=request.query,
                timestamp=start_time,
                search_engine="enhanced_search",
                results_count=len(results),
                response_time=response_time,
                cache_hit=any(r.get('cached', False) for r in results),
                quality_score=avg_quality,
                cost=estimated_cost,
                success=success,
                error_message=error_message
            )
            
            # تسجيل التحليلات
            search_analytics.record_search(analytics)
            
        except Exception as e:
            logger.error(f"❌ فشل في تسجيل التحليلات: {e}")
    
    def _estimate_search_cost(self, request: SearchRequest, results_count: int) -> float:
        """تقدير تكلفة البحث"""
        base_costs = {
            SearchPriority.FREE: 0.0,
            SearchPriority.LOW_COST: 0.005,
            SearchPriority.PREMIUM: 0.02,
            SearchPriority.EMERGENCY: 0.05
        }
        
        base_cost = base_costs.get(request.priority, 0.0)
        
        # تكلفة إضافية بناءً على عدد النتائج
        result_cost = results_count * 0.001
        
        return base_cost + result_cost
    
    def _update_search_state(self, success: bool):
        """تحديث حالة البحث"""
        if success:
            self.state['consecutive_failures'] = 0
            self.state['last_successful_search'] = time.time()
            
            # إلغاء وضع الطوارئ إذا كان مفعلاً
            if self.state['emergency_mode']:
                self.state['emergency_mode'] = False
                logger.info("✅ تم إلغاء وضع الطوارئ - البحث يعمل بشكل طبيعي")
        else:
            self.state['consecutive_failures'] += 1
            
            # تفعيل وضع الطوارئ
            if self.state['consecutive_failures'] >= self.settings['emergency_mode_threshold']:
                if not self.state['emergency_mode']:
                    self.state['emergency_mode'] = True
                    logger.warning("🚨 تم تفعيل وضع الطوارئ - فشل متكرر في البحث")
    
    async def _fallback_search(self, query: str, max_results: int) -> List[Dict]:
        """البحث الاحتياطي"""
        try:
            logger.info("🔄 تفعيل البحث الاحتياطي...")
            
            # استخدام البحث التقليدي كاحتياطي
            from .content_scraper import ContentScraper
            
            scraper = ContentScraper()
            fallback_results = scraper.search_and_extract_articles(query, max_results)
            
            if fallback_results:
                logger.info(f"✅ البحث الاحتياطي نجح: {len(fallback_results)} نتيجة")
                return fallback_results
            
            return []
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الاحتياطي: {e}")
            return []
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام"""
        try:
            # إحصائيات النظام
            cache_stats = advanced_cache.get_stats()
            rate_limit_stats = rate_limit_manager.get_overall_stats()
            search_stats = search_analytics.get_current_session_stats()
            
            return {
                'enabled': self.enabled,
                'emergency_mode': self.state['emergency_mode'],
                'consecutive_failures': self.state['consecutive_failures'],
                'last_successful_search': datetime.fromtimestamp(
                    self.state['last_successful_search']
                ).strftime('%Y-%m-%d %H:%M:%S'),
                'cache_stats': cache_stats,
                'rate_limit_stats': rate_limit_stats,
                'search_stats': search_stats
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع حالة النظام: {e}")
            return {'error': str(e)}

# إنشاء مثيل عام
enhanced_search = EnhancedSearchIntegration()
