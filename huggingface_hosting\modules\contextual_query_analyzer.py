# محلل الاستعلامات السياقي المتقدم
import asyncio
import re
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import aiohttp

from .logger import logger
from .fallback_ai_manager import fallback_ai_manager, AIModelType
from .database import db

class QueryIntent(Enum):
    """أنواع نوايا الاستعلام"""
    FIND_NEWS = "find_news"                    # البحث عن أخبار
    FIND_REVIEWS = "find_reviews"              # البحث عن مراجعات
    FIND_GUIDES = "find_guides"                # البحث عن أدلة
    FIND_UPDATES = "find_updates"              # البحث عن تحديثات
    FIND_RELEASES = "find_releases"            # البحث عن إصدارات
    FIND_COMPARISONS = "find_comparisons"      # البحث عن مقارنات
    FIND_TUTORIALS = "find_tutorials"          # البحث عن دروس
    FIND_ANALYSIS = "find_analysis"            # البحث عن تحليلات
    GENERAL_SEARCH = "general_search"          # بحث عام

class QueryComplexity(Enum):
    """مستوى تعقيد الاستعلام"""
    SIMPLE = "simple"           # بسيط - كلمة أو كلمتين
    MODERATE = "moderate"       # متوسط - جملة قصيرة
    COMPLEX = "complex"         # معقد - جملة طويلة أو متعددة المفاهيم
    VERY_COMPLEX = "very_complex"  # معقد جداً - استعلام متعدد الأجزاء

@dataclass
class QueryEntity:
    """كيان في الاستعلام"""
    text: str
    entity_type: str  # game, company, platform, genre, person
    confidence: float
    context: str

@dataclass
class QueryAnalysis:
    """تحليل شامل للاستعلام"""
    original_query: str
    cleaned_query: str
    intent: QueryIntent
    complexity: QueryComplexity
    entities: List[QueryEntity]
    keywords: List[str]
    temporal_context: Optional[str]  # today, this week, 2025, etc.
    geographic_context: Optional[str]  # global, US, Japan, etc.
    sentiment: str  # positive, negative, neutral
    urgency_level: int  # 1-5
    specificity_score: float  # 0-1
    sub_queries: List[str]
    enhanced_queries: List[str]
    metadata: Dict[str, Any]

class ContextualQueryAnalyzer:
    """محلل الاستعلامات السياقي المتقدم"""
    
    def __init__(self):
        # قواميس الكيانات المعروفة
        self.gaming_entities = self._load_gaming_entities()
        
        # أنماط التعبيرات النمطية
        self.patterns = self._compile_patterns()
        
        # إحصائيات التحليل
        self.analysis_stats = {
            'total_analyses': 0,
            'intent_distribution': {},
            'complexity_distribution': {},
            'entity_extraction_success': 0,
            'ai_enhancement_usage': 0
        }
        
        logger.info("🔍 تم تهيئة محلل الاستعلامات السياقي")
    
    def _load_gaming_entities(self) -> Dict[str, List[str]]:
        """تحميل قوائم الكيانات المتعلقة بالألعاب"""
        return {
            'companies': [
                'sony', 'microsoft', 'nintendo', 'valve', 'epic games', 'activision',
                'ubisoft', 'ea', 'electronic arts', 'blizzard', 'riot games', 'square enix',
                'capcom', 'konami', 'sega', 'bandai namco', 'take-two', 'rockstar',
                'cd projekt', 'bethesda', 'id software', 'bungie', 'respawn'
            ],
            'platforms': [
                'ps5', 'playstation 5', 'xbox series x', 'xbox series s', 'nintendo switch',
                'pc', 'steam', 'epic games store', 'mobile', 'ios', 'android',
                'ps4', 'playstation 4', 'xbox one', 'steam deck'
            ],
            'genres': [
                'rpg', 'fps', 'moba', 'battle royale', 'racing', 'sports', 'fighting',
                'platformer', 'puzzle', 'strategy', 'rts', 'turn-based', 'action',
                'adventure', 'simulation', 'sandbox', 'survival', 'horror', 'mmo'
            ],
            'games': [
                'fortnite', 'minecraft', 'call of duty', 'fifa', 'gta', 'cyberpunk',
                'the witcher', 'assassins creed', 'halo', 'god of war', 'spider-man',
                'zelda', 'mario', 'pokemon', 'overwatch', 'league of legends'
            ]
        }
    
    def _compile_patterns(self) -> Dict[str, re.Pattern]:
        """تجميع أنماط التعبيرات النمطية"""
        return {
            'temporal': re.compile(r'\b(today|yesterday|this week|this month|2025|january|february|march|april|may|june|july|august|september|october|november|december|latest|recent|new)\b', re.IGNORECASE),
            'urgency': re.compile(r'\b(breaking|urgent|just|now|immediately|asap|quick)\b', re.IGNORECASE),
            'comparison': re.compile(r'\b(vs|versus|compare|comparison|better|best|worst|difference)\b', re.IGNORECASE),
            'review': re.compile(r'\b(review|rating|score|opinion|thoughts|analysis|critique)\b', re.IGNORECASE),
            'guide': re.compile(r'\b(guide|tutorial|how to|walkthrough|tips|tricks|help)\b', re.IGNORECASE),
            'news': re.compile(r'\b(news|announcement|revealed|announced|confirmed|rumor|leak)\b', re.IGNORECASE),
            'release': re.compile(r'\b(release|launch|coming|upcoming|date|when)\b', re.IGNORECASE)
        }
    
    async def analyze_query(self, query: str, use_ai_enhancement: bool = True) -> QueryAnalysis:
        """تحليل شامل للاستعلام"""
        start_time = time.time()
        self.analysis_stats['total_analyses'] += 1
        
        try:
            # 1. تنظيف الاستعلام
            cleaned_query = self._clean_query(query)
            
            # 2. تحديد النية
            intent = self._detect_intent(cleaned_query)
            
            # 3. تحديد مستوى التعقيد
            complexity = self._assess_complexity(cleaned_query)
            
            # 4. استخراج الكيانات
            entities = self._extract_entities(cleaned_query)
            
            # 5. استخراج الكلمات المفتاحية
            keywords = self._extract_keywords(cleaned_query)
            
            # 6. تحليل السياق الزمني
            temporal_context = self._extract_temporal_context(cleaned_query)
            
            # 7. تحليل السياق الجغرافي
            geographic_context = self._extract_geographic_context(cleaned_query)
            
            # 8. تحليل المشاعر
            sentiment = self._analyze_sentiment(cleaned_query)
            
            # 9. تحديد مستوى الإلحاح
            urgency_level = self._assess_urgency(cleaned_query)
            
            # 10. حساب نقاط التحديد
            specificity_score = self._calculate_specificity(cleaned_query, entities)
            
            # 11. توليد استعلامات فرعية
            sub_queries = self._generate_sub_queries(cleaned_query, intent, entities)
            
            # 12. تحسين الاستعلامات باستخدام AI (اختياري)
            enhanced_queries = []
            if use_ai_enhancement:
                enhanced_queries = await self._ai_enhance_queries(cleaned_query, intent, entities)
                if enhanced_queries:
                    self.analysis_stats['ai_enhancement_usage'] += 1
            
            # إنشاء التحليل النهائي
            analysis = QueryAnalysis(
                original_query=query,
                cleaned_query=cleaned_query,
                intent=intent,
                complexity=complexity,
                entities=entities,
                keywords=keywords,
                temporal_context=temporal_context,
                geographic_context=geographic_context,
                sentiment=sentiment,
                urgency_level=urgency_level,
                specificity_score=specificity_score,
                sub_queries=sub_queries,
                enhanced_queries=enhanced_queries,
                metadata={
                    'analysis_time': time.time() - start_time,
                    'ai_enhanced': bool(enhanced_queries),
                    'entity_count': len(entities),
                    'keyword_count': len(keywords)
                }
            )
            
            # تحديث الإحصائيات
            self._update_stats(analysis)
            
            logger.info(f"🔍 تحليل الاستعلام مكتمل: {intent.value} | {complexity.value} | {len(entities)} كيان")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل الاستعلام: {e}")
            # إرجاع تحليل أساسي
            return self._create_basic_analysis(query)
    
    def _clean_query(self, query: str) -> str:
        """تنظيف الاستعلام"""
        # إزالة الرموز الخاصة غير المرغوبة
        cleaned = re.sub(r'[^\w\s\-\']', ' ', query)
        
        # إزالة المسافات الزائدة
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # تحويل للأحرف الصغيرة
        cleaned = cleaned.lower()
        
        return cleaned
    
    def _detect_intent(self, query: str) -> QueryIntent:
        """تحديد نية الاستعلام"""
        query_lower = query.lower()
        
        # فحص الأنماط المختلفة
        if self.patterns['news'].search(query_lower):
            return QueryIntent.FIND_NEWS
        elif self.patterns['review'].search(query_lower):
            return QueryIntent.FIND_REVIEWS
        elif self.patterns['guide'].search(query_lower):
            return QueryIntent.FIND_GUIDES
        elif self.patterns['release'].search(query_lower):
            return QueryIntent.FIND_RELEASES
        elif self.patterns['comparison'].search(query_lower):
            return QueryIntent.FIND_COMPARISONS
        elif 'update' in query_lower or 'patch' in query_lower:
            return QueryIntent.FIND_UPDATES
        elif 'tutorial' in query_lower or 'how to' in query_lower:
            return QueryIntent.FIND_TUTORIALS
        elif 'analysis' in query_lower or 'deep dive' in query_lower:
            return QueryIntent.FIND_ANALYSIS
        else:
            return QueryIntent.GENERAL_SEARCH
    
    def _assess_complexity(self, query: str) -> QueryComplexity:
        """تقييم مستوى تعقيد الاستعلام"""
        word_count = len(query.split())
        
        if word_count <= 2:
            return QueryComplexity.SIMPLE
        elif word_count <= 5:
            return QueryComplexity.MODERATE
        elif word_count <= 10:
            return QueryComplexity.COMPLEX
        else:
            return QueryComplexity.VERY_COMPLEX

    def _extract_entities(self, query: str) -> List[QueryEntity]:
        """استخراج الكيانات من الاستعلام"""
        entities = []
        query_lower = query.lower()

        # البحث عن الشركات
        for company in self.gaming_entities['companies']:
            if company in query_lower:
                confidence = 0.9 if len(company.split()) > 1 else 0.7
                entities.append(QueryEntity(
                    text=company,
                    entity_type='company',
                    confidence=confidence,
                    context=f"gaming company mentioned in query"
                ))

        # البحث عن المنصات
        for platform in self.gaming_entities['platforms']:
            if platform in query_lower:
                confidence = 0.9
                entities.append(QueryEntity(
                    text=platform,
                    entity_type='platform',
                    confidence=confidence,
                    context=f"gaming platform mentioned in query"
                ))

        # البحث عن الأنواع
        for genre in self.gaming_entities['genres']:
            if genre in query_lower:
                confidence = 0.8
                entities.append(QueryEntity(
                    text=genre,
                    entity_type='genre',
                    confidence=confidence,
                    context=f"game genre mentioned in query"
                ))

        # البحث عن الألعاب
        for game in self.gaming_entities['games']:
            if game in query_lower:
                confidence = 0.95
                entities.append(QueryEntity(
                    text=game,
                    entity_type='game',
                    confidence=confidence,
                    context=f"specific game mentioned in query"
                ))

        # إزالة التكرار
        unique_entities = []
        seen_texts = set()
        for entity in entities:
            if entity.text not in seen_texts:
                unique_entities.append(entity)
                seen_texts.add(entity.text)

        if unique_entities:
            self.analysis_stats['entity_extraction_success'] += 1

        return unique_entities

    def _extract_keywords(self, query: str) -> List[str]:
        """استخراج الكلمات المفتاحية المهمة"""
        # كلمات الإيقاف
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        }

        # تقسيم وتنظيف
        words = query.lower().split()
        keywords = []

        for word in words:
            # إزالة علامات الترقيم
            clean_word = re.sub(r'[^\w]', '', word)

            # تجاهل كلمات الإيقاف والكلمات القصيرة
            if clean_word and len(clean_word) > 2 and clean_word not in stop_words:
                keywords.append(clean_word)

        # إضافة كلمات مفتاحية مركبة
        compound_keywords = self._extract_compound_keywords(query)
        keywords.extend(compound_keywords)

        return list(set(keywords))  # إزالة التكرار

    def _extract_compound_keywords(self, query: str) -> List[str]:
        """استخراج الكلمات المفتاحية المركبة"""
        compound_keywords = []
        query_lower = query.lower()

        # عبارات مركبة شائعة في الألعاب
        gaming_phrases = [
            'battle royale', 'first person shooter', 'role playing game',
            'real time strategy', 'massively multiplayer', 'virtual reality',
            'augmented reality', 'early access', 'open world', 'single player',
            'multiplayer', 'co-op', 'competitive gaming', 'esports tournament',
            'game engine', 'indie game', 'aaa game', 'free to play',
            'pay to win', 'season pass', 'downloadable content', 'expansion pack'
        ]

        for phrase in gaming_phrases:
            if phrase in query_lower:
                compound_keywords.append(phrase.replace(' ', '_'))

        return compound_keywords

    def _extract_temporal_context(self, query: str) -> Optional[str]:
        """استخراج السياق الزمني"""
        temporal_match = self.patterns['temporal'].search(query)
        if temporal_match:
            return temporal_match.group(0).lower()
        return None

    def _extract_geographic_context(self, query: str) -> Optional[str]:
        """استخراج السياق الجغرافي"""
        geographic_terms = {
            'global': ['global', 'worldwide', 'international'],
            'us': ['us', 'usa', 'america', 'american'],
            'europe': ['europe', 'european', 'eu'],
            'asia': ['asia', 'asian', 'japan', 'japanese', 'korea', 'korean', 'china', 'chinese'],
            'uk': ['uk', 'britain', 'british', 'england']
        }

        query_lower = query.lower()
        for region, terms in geographic_terms.items():
            if any(term in query_lower for term in terms):
                return region

        return None

    def _analyze_sentiment(self, query: str) -> str:
        """تحليل المشاعر في الاستعلام"""
        positive_words = ['best', 'great', 'amazing', 'awesome', 'excellent', 'good', 'love', 'like']
        negative_words = ['worst', 'bad', 'terrible', 'awful', 'hate', 'dislike', 'problem', 'issue']

        query_lower = query.lower()

        positive_count = sum(1 for word in positive_words if word in query_lower)
        negative_count = sum(1 for word in negative_words if word in query_lower)

        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    def _assess_urgency(self, query: str) -> int:
        """تقييم مستوى الإلحاح (1-5)"""
        urgency_indicators = {
            5: ['breaking', 'urgent', 'immediately', 'asap', 'now'],
            4: ['quick', 'fast', 'soon', 'today'],
            3: ['this week', 'recent', 'latest'],
            2: ['new', 'current'],
            1: []  # افتراضي
        }

        query_lower = query.lower()

        for level, indicators in urgency_indicators.items():
            if any(indicator in query_lower for indicator in indicators):
                return level

        return 1  # مستوى إلحاح منخفض افتراضي

    def _calculate_specificity(self, query: str, entities: List[QueryEntity]) -> float:
        """حساب نقاط التحديد (0-1)"""
        specificity = 0.0

        # نقاط للكيانات المحددة
        specificity += len(entities) * 0.2

        # نقاط لطول الاستعلام
        word_count = len(query.split())
        if word_count > 3:
            specificity += 0.2
        if word_count > 6:
            specificity += 0.2

        # نقاط للأرقام والتواريخ
        if re.search(r'\d+', query):
            specificity += 0.2

        # نقاط للعلامات التجارية المحددة
        specific_terms = ['version', 'edition', 'dlc', 'expansion', 'update', 'patch']
        if any(term in query.lower() for term in specific_terms):
            specificity += 0.2

        return min(specificity, 1.0)

    def _generate_sub_queries(self, query: str, intent: QueryIntent, entities: List[QueryEntity]) -> List[str]:
        """توليد استعلامات فرعية"""
        sub_queries = []

        # استعلامات بناءً على النية
        if intent == QueryIntent.FIND_NEWS:
            sub_queries.extend([
                f"{query} news",
                f"{query} announcement",
                f"{query} breaking news",
                f"latest {query}"
            ])
        elif intent == QueryIntent.FIND_REVIEWS:
            sub_queries.extend([
                f"{query} review",
                f"{query} rating",
                f"{query} score",
                f"{query} opinion"
            ])
        elif intent == QueryIntent.FIND_GUIDES:
            sub_queries.extend([
                f"{query} guide",
                f"{query} tutorial",
                f"how to {query}",
                f"{query} tips"
            ])

        # استعلامات بناءً على الكيانات
        for entity in entities:
            if entity.entity_type == 'game':
                sub_queries.extend([
                    f"{entity.text} news",
                    f"{entity.text} update",
                    f"{entity.text} review"
                ])
            elif entity.entity_type == 'company':
                sub_queries.extend([
                    f"{entity.text} announcement",
                    f"{entity.text} games",
                    f"{entity.text} news"
                ])

        # إزالة التكرار والاستعلام الأصلي
        unique_sub_queries = []
        for sub_query in sub_queries:
            if sub_query != query and sub_query not in unique_sub_queries:
                unique_sub_queries.append(sub_query)

        return unique_sub_queries[:10]  # أفضل 10 استعلامات فرعية

    async def _ai_enhance_queries(self, query: str, intent: QueryIntent, entities: List[QueryEntity]) -> List[str]:
        """تحسين الاستعلامات باستخدام الذكاء الاصطناعي"""
        try:
            # إعداد السياق للذكاء الاصطناعي
            context = {
                'original_query': query,
                'intent': intent.value,
                'entities': [{'text': e.text, 'type': e.entity_type} for e in entities],
                'task': 'enhance_gaming_search_queries'
            }

            # طلب تحسين من النماذج الاحتياطية
            enhancement_request = {
                'query': query,
                'context': 'gaming_news_search',
                'intent': intent.value,
                'max_alternatives': 5
            }

            # استخدام النموذج الأفضل المتاح
            enhanced_result = await fallback_ai_manager.enhance_search_query(enhancement_request)

            if enhanced_result and 'enhanced_queries' in enhanced_result:
                return enhanced_result['enhanced_queries'][:5]  # أفضل 5 استعلامات

        except Exception as e:
            logger.error(f"❌ فشل في تحسين الاستعلامات بالذكاء الاصطناعي: {e}")

        # إرجاع استعلامات محسنة يدوياً كخطة بديلة
        return self._manual_query_enhancement(query, intent, entities)

    def _manual_query_enhancement(self, query: str, intent: QueryIntent, entities: List[QueryEntity]) -> List[str]:
        """تحسين الاستعلامات يدوياً كخطة بديلة"""
        enhanced_queries = []

        # إضافة كلمات سياقية
        if intent == QueryIntent.FIND_NEWS:
            enhanced_queries.extend([
                f"{query} gaming news 2025",
                f"{query} latest announcement",
                f"{query} breaking gaming news"
            ])
        elif intent == QueryIntent.FIND_REVIEWS:
            enhanced_queries.extend([
                f"{query} game review score",
                f"{query} gaming review analysis",
                f"{query} player review opinion"
            ])

        # إضافة استعلامات بناءً على الكيانات
        for entity in entities[:2]:  # أهم كيانين
            enhanced_queries.append(f"{entity.text} {query}")
            enhanced_queries.append(f"{query} {entity.text}")

        # إضافة استعلامات زمنية
        current_year = datetime.now().year
        enhanced_queries.extend([
            f"{query} {current_year}",
            f"{query} latest",
            f"{query} recent"
        ])

        return enhanced_queries[:5]

    def _update_stats(self, analysis: QueryAnalysis):
        """تحديث إحصائيات التحليل"""
        # تحديث توزيع النوايا
        intent_key = analysis.intent.value
        if intent_key not in self.analysis_stats['intent_distribution']:
            self.analysis_stats['intent_distribution'][intent_key] = 0
        self.analysis_stats['intent_distribution'][intent_key] += 1

        # تحديث توزيع التعقيد
        complexity_key = analysis.complexity.value
        if complexity_key not in self.analysis_stats['complexity_distribution']:
            self.analysis_stats['complexity_distribution'][complexity_key] = 0
        self.analysis_stats['complexity_distribution'][complexity_key] += 1

    def _create_basic_analysis(self, query: str) -> QueryAnalysis:
        """إنشاء تحليل أساسي في حالة الفشل"""
        return QueryAnalysis(
            original_query=query,
            cleaned_query=query.lower().strip(),
            intent=QueryIntent.GENERAL_SEARCH,
            complexity=QueryComplexity.SIMPLE,
            entities=[],
            keywords=query.split(),
            temporal_context=None,
            geographic_context=None,
            sentiment='neutral',
            urgency_level=1,
            specificity_score=0.5,
            sub_queries=[],
            enhanced_queries=[],
            metadata={'analysis_time': 0, 'ai_enhanced': False, 'entity_count': 0, 'keyword_count': len(query.split())}
        )

    def get_analysis_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحليل"""
        return {
            'total_analyses': self.analysis_stats['total_analyses'],
            'intent_distribution': dict(self.analysis_stats['intent_distribution']),
            'complexity_distribution': dict(self.analysis_stats['complexity_distribution']),
            'entity_extraction_success_rate': (
                self.analysis_stats['entity_extraction_success'] /
                max(self.analysis_stats['total_analyses'], 1)
            ),
            'ai_enhancement_usage_rate': (
                self.analysis_stats['ai_enhancement_usage'] /
                max(self.analysis_stats['total_analyses'], 1)
            )
        }

    async def batch_analyze_queries(self, queries: List[str]) -> List[QueryAnalysis]:
        """تحليل مجموعة من الاستعلامات بالتوازي"""
        try:
            # تنفيذ التحليل بالتوازي
            tasks = [self.analyze_query(query, use_ai_enhancement=False) for query in queries]
            analyses = await asyncio.gather(*tasks, return_exceptions=True)

            # فلترة النتائج الناجحة
            successful_analyses = []
            for analysis in analyses:
                if isinstance(analysis, QueryAnalysis):
                    successful_analyses.append(analysis)
                else:
                    logger.error(f"❌ فشل في تحليل أحد الاستعلامات: {analysis}")

            logger.info(f"📊 تم تحليل {len(successful_analyses)} من {len(queries)} استعلام بنجاح")
            return successful_analyses

        except Exception as e:
            logger.error(f"❌ فشل في التحليل المجمع: {e}")
            return []

    def suggest_query_improvements(self, analysis: QueryAnalysis) -> List[str]:
        """اقتراح تحسينات للاستعلام"""
        suggestions = []

        # اقتراحات بناءً على التعقيد
        if analysis.complexity == QueryComplexity.SIMPLE:
            suggestions.append("جرب إضافة كلمات مفتاحية أكثر تحديداً")
            suggestions.append("أضف السياق الزمني (مثل: 2025، latest)")

        # اقتراحات بناءً على النية
        if analysis.intent == QueryIntent.GENERAL_SEARCH:
            suggestions.append("حدد نوع المحتوى المطلوب (news، review، guide)")

        # اقتراحات بناءً على الكيانات
        if not analysis.entities:
            suggestions.append("أضف أسماء ألعاب أو شركات محددة")

        # اقتراحات بناءً على التحديد
        if analysis.specificity_score < 0.5:
            suggestions.append("كن أكثر تحديداً في وصف ما تبحث عنه")

        return suggestions

    def export_analysis_report(self) -> Dict[str, Any]:
        """تصدير تقرير شامل عن التحليلات"""
        stats = self.get_analysis_stats()

        # تحليل الاتجاهات
        most_common_intent = max(stats['intent_distribution'], key=stats['intent_distribution'].get) if stats['intent_distribution'] else 'unknown'
        most_common_complexity = max(stats['complexity_distribution'], key=stats['complexity_distribution'].get) if stats['complexity_distribution'] else 'unknown'

        report = {
            'summary': {
                'total_analyses': stats['total_analyses'],
                'most_common_intent': most_common_intent,
                'most_common_complexity': most_common_complexity,
                'entity_extraction_success_rate': f"{stats['entity_extraction_success_rate']:.2%}",
                'ai_enhancement_usage_rate': f"{stats['ai_enhancement_usage_rate']:.2%}"
            },
            'detailed_stats': stats,
            'recommendations': [
                "استخدم استعلامات أكثر تحديداً لنتائج أفضل",
                "أضف السياق الزمني للحصول على أحدث المعلومات",
                "حدد نوع المحتوى المطلوب (أخبار، مراجعات، أدلة)"
            ],
            'generated_at': datetime.now().isoformat()
        }

        return report

# إنشاء مثيل عام
contextual_query_analyzer = ContextualQueryAnalyzer()
