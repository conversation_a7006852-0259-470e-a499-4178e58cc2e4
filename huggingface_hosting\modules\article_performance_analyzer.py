#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تحليل أداء المقالات التلقائي
يقوم بمراجعة المقالات المنشورة وتقييم أدائها (CTR, Impressions, مدة القراءة, المشاركات)
"""

import asyncio
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import statistics
from dataclasses import dataclass

from .logger import logger
from .database import db

@dataclass
class ArticlePerformanceMetrics:
    """مقاييس أداء المقال"""
    article_id: int
    title: str
    published_date: datetime
    views: int = 0
    clicks: int = 0
    impressions: int = 0
    ctr: float = 0.0  # Click Through Rate
    avg_read_time: float = 0.0  # متوسط وقت القراءة بالثواني
    bounce_rate: float = 0.0  # معدل الارتداد
    social_shares: int = 0
    comments_count: int = 0
    engagement_score: float = 0.0
    seo_score: float = 0.0
    performance_grade: str = "C"  # A, B, C, D, F

class ArticlePerformanceAnalyzer:
    """محلل أداء المقالات التلقائي"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_performance_tables()
        
        # معايير تقييم الأداء
        self.performance_thresholds = {
            'excellent': {'ctr': 5.0, 'avg_read_time': 120, 'bounce_rate': 30, 'engagement_score': 80},
            'good': {'ctr': 3.0, 'avg_read_time': 90, 'bounce_rate': 50, 'engagement_score': 60},
            'average': {'ctr': 2.0, 'avg_read_time': 60, 'bounce_rate': 70, 'engagement_score': 40},
            'poor': {'ctr': 1.0, 'avg_read_time': 30, 'bounce_rate': 85, 'engagement_score': 20}
        }
    
    def _init_performance_tables(self):
        """إنشاء جداول تحليل الأداء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول مقاييس أداء المقالات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS article_performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        measurement_date DATE NOT NULL,
                        views INTEGER DEFAULT 0,
                        clicks INTEGER DEFAULT 0,
                        impressions INTEGER DEFAULT 0,
                        ctr REAL DEFAULT 0.0,
                        avg_read_time REAL DEFAULT 0.0,
                        bounce_rate REAL DEFAULT 0.0,
                        social_shares INTEGER DEFAULT 0,
                        comments_count INTEGER DEFAULT 0,
                        engagement_score REAL DEFAULT 0.0,
                        seo_score REAL DEFAULT 0.0,
                        performance_grade TEXT DEFAULT 'C',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول تحليل الأداء اليومي
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_performance_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        analysis_date DATE UNIQUE NOT NULL,
                        total_articles_analyzed INTEGER DEFAULT 0,
                        avg_performance_score REAL DEFAULT 0.0,
                        top_performing_articles TEXT,
                        poor_performing_articles TEXT,
                        improvement_recommendations TEXT,
                        trends_identified TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول توصيات التحسين
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS improvement_recommendations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        recommendation_type TEXT NOT NULL,
                        current_value REAL,
                        target_value REAL,
                        recommendation_text TEXT,
                        priority_level TEXT DEFAULT 'medium',
                        status TEXT DEFAULT 'pending',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        applied_at TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول تحليل الأداء بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول تحليل الأداء", e)
    
    async def analyze_all_articles_performance(self) -> Dict:
        """تحليل أداء جميع المقالات"""
        try:
            logger.info("📊 بدء تحليل أداء جميع المقالات...")
            
            # الحصول على المقالات المنشورة في آخر 30 يوم
            articles = self._get_recent_articles(30)
            
            if not articles:
                logger.warning("⚠️ لا توجد مقالات للتحليل")
                return {}
            
            analysis_results = []
            
            for article in articles:
                try:
                    # تحليل أداء المقال الفردي
                    performance = await self._analyze_single_article_performance(article)
                    if performance:
                        analysis_results.append(performance)
                        
                        # حفظ النتائج في قاعدة البيانات
                        self._save_performance_metrics(performance)
                        
                        # إنشاء توصيات التحسين
                        recommendations = self._generate_improvement_recommendations(performance)
                        if recommendations:
                            self._save_improvement_recommendations(article['id'], recommendations)
                    
                except Exception as e:
                    logger.error(f"❌ فشل في تحليل المقال {article.get('title', 'غير محدد')}", e)
                    continue
            
            # تحليل الاتجاهات العامة
            overall_analysis = self._analyze_overall_trends(analysis_results)
            
            # حفظ التحليل اليومي
            self._save_daily_analysis(overall_analysis, analysis_results)
            
            logger.info(f"✅ تم تحليل {len(analysis_results)} مقال بنجاح")
            
            return {
                'total_analyzed': len(analysis_results),
                'overall_analysis': overall_analysis,
                'individual_results': analysis_results,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("❌ فشل في تحليل أداء المقالات", e)
            return {}
    
    async def _analyze_single_article_performance(self, article: Dict) -> Optional[ArticlePerformanceMetrics]:
        """تحليل أداء مقال واحد"""
        try:
            article_id = article['id']
            title = article['title']
            published_date = datetime.fromisoformat(article['published_at']) if article.get('published_at') else datetime.now()
            
            # محاكاة بيانات الأداء (في التطبيق الحقيقي، ستأتي من Google Analytics أو مصادر أخرى)
            performance_data = await self._fetch_article_analytics(article_id, article.get('blogger_url'))
            
            # حساب المقاييس
            metrics = ArticlePerformanceMetrics(
                article_id=article_id,
                title=title,
                published_date=published_date,
                views=performance_data.get('views', 0),
                clicks=performance_data.get('clicks', 0),
                impressions=performance_data.get('impressions', 0),
                ctr=performance_data.get('ctr', 0.0),
                avg_read_time=performance_data.get('avg_read_time', 0.0),
                bounce_rate=performance_data.get('bounce_rate', 0.0),
                social_shares=performance_data.get('social_shares', 0),
                comments_count=performance_data.get('comments_count', 0)
            )
            
            # حساب نقاط التفاعل
            metrics.engagement_score = self._calculate_engagement_score(metrics)
            
            # حساب نقاط SEO
            metrics.seo_score = self._calculate_seo_score(article, metrics)
            
            # تحديد درجة الأداء
            metrics.performance_grade = self._determine_performance_grade(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ فشل في تحليل أداء المقال {article.get('title', 'غير محدد')}", e)
            return None
    
    async def _fetch_article_analytics(self, article_id: int, article_url: str = None) -> Dict:
        """جلب بيانات التحليلات للمقال"""
        try:
            # محاكاة بيانات التحليلات (في التطبيق الحقيقي، ستتصل بـ Google Analytics API)
            import random
            
            # توليد بيانات واقعية نسبياً
            base_views = random.randint(50, 500)
            
            analytics_data = {
                'views': base_views,
                'clicks': random.randint(int(base_views * 0.1), int(base_views * 0.3)),
                'impressions': random.randint(base_views, base_views * 3),
                'avg_read_time': random.uniform(30, 180),  # 30 ثانية إلى 3 دقائق
                'bounce_rate': random.uniform(30, 80),  # 30% إلى 80%
                'social_shares': random.randint(0, 20),
                'comments_count': random.randint(0, 10)
            }
            
            # حساب CTR
            if analytics_data['impressions'] > 0:
                analytics_data['ctr'] = (analytics_data['clicks'] / analytics_data['impressions']) * 100
            else:
                analytics_data['ctr'] = 0.0
            
            return analytics_data
            
        except Exception as e:
            logger.error(f"❌ فشل في جلب بيانات التحليلات للمقال {article_id}", e)
            return {}
    
    def _calculate_engagement_score(self, metrics: ArticlePerformanceMetrics) -> float:
        """حساب نقاط التفاعل"""
        try:
            score = 0.0
            
            # نقاط CTR (30%)
            if metrics.ctr >= 5.0:
                score += 30
            elif metrics.ctr >= 3.0:
                score += 25
            elif metrics.ctr >= 2.0:
                score += 20
            elif metrics.ctr >= 1.0:
                score += 15
            else:
                score += 10
            
            # نقاط وقت القراءة (25%)
            if metrics.avg_read_time >= 120:
                score += 25
            elif metrics.avg_read_time >= 90:
                score += 20
            elif metrics.avg_read_time >= 60:
                score += 15
            elif metrics.avg_read_time >= 30:
                score += 10
            else:
                score += 5
            
            # نقاط معدل الارتداد (20%)
            if metrics.bounce_rate <= 30:
                score += 20
            elif metrics.bounce_rate <= 50:
                score += 15
            elif metrics.bounce_rate <= 70:
                score += 10
            else:
                score += 5
            
            # نقاط المشاركات الاجتماعية (15%)
            if metrics.social_shares >= 10:
                score += 15
            elif metrics.social_shares >= 5:
                score += 12
            elif metrics.social_shares >= 2:
                score += 8
            elif metrics.social_shares >= 1:
                score += 5
            
            # نقاط التعليقات (10%)
            if metrics.comments_count >= 5:
                score += 10
            elif metrics.comments_count >= 2:
                score += 7
            elif metrics.comments_count >= 1:
                score += 5
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error("❌ فشل في حساب نقاط التفاعل", e)
            return 0.0
    
    def _calculate_seo_score(self, article: Dict, metrics: ArticlePerformanceMetrics) -> float:
        """حساب نقاط SEO"""
        try:
            score = 0.0
            
            # تحليل العنوان
            title = article.get('title', '')
            if 30 <= len(title) <= 60:
                score += 20
            elif len(title) > 0:
                score += 10
            
            # تحليل المحتوى
            content = article.get('content', '') or ''
            if len(content) >= 500:
                score += 20
            elif len(content) >= 300:
                score += 15
            elif len(content) >= 100:
                score += 10
            
            # تحليل الكلمات المفتاحية
            keywords = article.get('keywords', []) or []
            if isinstance(keywords, str):
                keywords = keywords.split(',') if keywords else []

            if len(keywords) >= 5:
                score += 15
            elif len(keywords) >= 3:
                score += 10
            elif len(keywords) >= 1:
                score += 5
            
            # تحليل الأداء
            if metrics.views >= 100:
                score += 15
            elif metrics.views >= 50:
                score += 10
            elif metrics.views >= 10:
                score += 5
            
            # تحليل CTR
            if metrics.ctr >= 3.0:
                score += 15
            elif metrics.ctr >= 2.0:
                score += 10
            elif metrics.ctr >= 1.0:
                score += 5
            
            # تحليل الصور
            if article.get('image_urls'):
                score += 15
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error("❌ فشل في حساب نقاط SEO", e)
            return 0.0

    def _determine_performance_grade(self, metrics: ArticlePerformanceMetrics) -> str:
        """تحديد درجة الأداء"""
        try:
            # حساب النقاط الإجمالية
            total_score = (metrics.engagement_score + metrics.seo_score) / 2

            if total_score >= 90:
                return "A+"
            elif total_score >= 85:
                return "A"
            elif total_score >= 80:
                return "A-"
            elif total_score >= 75:
                return "B+"
            elif total_score >= 70:
                return "B"
            elif total_score >= 65:
                return "B-"
            elif total_score >= 60:
                return "C+"
            elif total_score >= 55:
                return "C"
            elif total_score >= 50:
                return "C-"
            elif total_score >= 45:
                return "D+"
            elif total_score >= 40:
                return "D"
            else:
                return "F"

        except Exception as e:
            logger.error("❌ فشل في تحديد درجة الأداء", e)
            return "C"

    def _generate_improvement_recommendations(self, metrics: ArticlePerformanceMetrics) -> List[Dict]:
        """إنشاء توصيات التحسين"""
        try:
            recommendations = []

            # توصيات CTR
            if metrics.ctr < 2.0:
                recommendations.append({
                    'type': 'ctr_improvement',
                    'current_value': metrics.ctr,
                    'target_value': 3.0,
                    'text': 'تحسين العنوان لزيادة معدل النقر. استخدم كلمات جذابة ومثيرة للاهتمام.',
                    'priority': 'high' if metrics.ctr < 1.0 else 'medium'
                })

            # توصيات وقت القراءة
            if metrics.avg_read_time < 60:
                recommendations.append({
                    'type': 'read_time_improvement',
                    'current_value': metrics.avg_read_time,
                    'target_value': 90.0,
                    'text': 'تحسين جودة المحتوى لزيادة وقت القراءة. أضف معلومات قيمة ومفيدة.',
                    'priority': 'high' if metrics.avg_read_time < 30 else 'medium'
                })

            # توصيات معدل الارتداد
            if metrics.bounce_rate > 70:
                recommendations.append({
                    'type': 'bounce_rate_improvement',
                    'current_value': metrics.bounce_rate,
                    'target_value': 50.0,
                    'text': 'تقليل معدل الارتداد بتحسين تجربة المستخدم وإضافة روابط داخلية.',
                    'priority': 'high' if metrics.bounce_rate > 85 else 'medium'
                })

            # توصيات المشاركات الاجتماعية
            if metrics.social_shares < 2:
                recommendations.append({
                    'type': 'social_shares_improvement',
                    'current_value': metrics.social_shares,
                    'target_value': 5.0,
                    'text': 'تشجيع المشاركات الاجتماعية بإضافة أزرار المشاركة وكتابة محتوى قابل للمشاركة.',
                    'priority': 'medium'
                })

            # توصيات SEO
            if metrics.seo_score < 70:
                recommendations.append({
                    'type': 'seo_improvement',
                    'current_value': metrics.seo_score,
                    'target_value': 80.0,
                    'text': 'تحسين SEO بإضافة كلمات مفتاحية مناسبة وتحسين بنية المحتوى.',
                    'priority': 'high' if metrics.seo_score < 50 else 'medium'
                })

            return recommendations

        except Exception as e:
            logger.error("❌ فشل في إنشاء توصيات التحسين", e)
            return []

    def _get_recent_articles(self, days: int = 30) -> List[Dict]:
        """الحصول على المقالات الحديثة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, title, content, keywords, blogger_url, published_at, view_count, engagement_score
                    FROM published_articles
                    WHERE published_at >= datetime('now', '-{} days')
                    ORDER BY published_at DESC
                '''.format(days))

                articles = []
                for row in cursor.fetchall():
                    articles.append({
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'keywords': row[3],
                        'blogger_url': row[4],
                        'published_at': row[5],
                        'view_count': row[6],
                        'engagement_score': row[7]
                    })

                return articles

        except Exception as e:
            logger.error("❌ فشل في الحصول على المقالات الحديثة", e)
            return []

    def _save_performance_metrics(self, metrics: ArticlePerformanceMetrics):
        """حفظ مقاييس الأداء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO article_performance_metrics
                    (article_id, measurement_date, views, clicks, impressions, ctr,
                     avg_read_time, bounce_rate, social_shares, comments_count,
                     engagement_score, seo_score, performance_grade)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metrics.article_id,
                    datetime.now().date(),
                    metrics.views,
                    metrics.clicks,
                    metrics.impressions,
                    metrics.ctr,
                    metrics.avg_read_time,
                    metrics.bounce_rate,
                    metrics.social_shares,
                    metrics.comments_count,
                    metrics.engagement_score,
                    metrics.seo_score,
                    metrics.performance_grade
                ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ مقاييس الأداء", e)

    def _save_improvement_recommendations(self, article_id: int, recommendations: List[Dict]):
        """حفظ توصيات التحسين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for rec in recommendations:
                    cursor.execute('''
                        INSERT INTO improvement_recommendations
                        (article_id, recommendation_type, current_value, target_value,
                         recommendation_text, priority_level)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        article_id,
                        rec['type'],
                        rec['current_value'],
                        rec['target_value'],
                        rec['text'],
                        rec['priority']
                    ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ توصيات التحسين", e)

    def _analyze_overall_trends(self, analysis_results: List[ArticlePerformanceMetrics]) -> Dict:
        """تحليل الاتجاهات العامة"""
        try:
            if not analysis_results:
                return {}

            # حساب المتوسطات
            avg_ctr = statistics.mean([r.ctr for r in analysis_results])
            avg_read_time = statistics.mean([r.avg_read_time for r in analysis_results])
            avg_bounce_rate = statistics.mean([r.bounce_rate for r in analysis_results])
            avg_engagement = statistics.mean([r.engagement_score for r in analysis_results])
            avg_seo = statistics.mean([r.seo_score for r in analysis_results])

            # تحديد أفضل وأسوأ المقالات
            top_articles = sorted(analysis_results, key=lambda x: x.engagement_score, reverse=True)[:5]
            poor_articles = sorted(analysis_results, key=lambda x: x.engagement_score)[:5]

            # تحديد الاتجاهات
            trends = []
            if avg_ctr < 2.0:
                trends.append("معدل النقر منخفض عموماً - يحتاج تحسين العناوين")
            if avg_read_time < 60:
                trends.append("وقت القراءة قصير - يحتاج تحسين جودة المحتوى")
            if avg_bounce_rate > 70:
                trends.append("معدل الارتداد مرتفع - يحتاج تحسين تجربة المستخدم")

            return {
                'total_articles': len(analysis_results),
                'averages': {
                    'ctr': round(avg_ctr, 2),
                    'read_time': round(avg_read_time, 2),
                    'bounce_rate': round(avg_bounce_rate, 2),
                    'engagement_score': round(avg_engagement, 2),
                    'seo_score': round(avg_seo, 2)
                },
                'top_performing': [{'id': a.article_id, 'title': a.title, 'score': a.engagement_score} for a in top_articles],
                'poor_performing': [{'id': a.article_id, 'title': a.title, 'score': a.engagement_score} for a in poor_articles],
                'trends_identified': trends,
                'overall_health': self._calculate_overall_health(avg_engagement, avg_seo)
            }

        except Exception as e:
            logger.error("❌ فشل في تحليل الاتجاهات العامة", e)
            return {}

    def _calculate_overall_health(self, avg_engagement: float, avg_seo: float) -> str:
        """حساب الصحة العامة للموقع"""
        overall_score = (avg_engagement + avg_seo) / 2

        if overall_score >= 80:
            return "ممتاز"
        elif overall_score >= 70:
            return "جيد جداً"
        elif overall_score >= 60:
            return "جيد"
        elif overall_score >= 50:
            return "متوسط"
        else:
            return "يحتاج تحسين"

    def _save_daily_analysis(self, overall_analysis: Dict, individual_results: List[ArticlePerformanceMetrics]):
        """حفظ التحليل اليومي"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO daily_performance_analysis
                    (analysis_date, total_articles_analyzed, avg_performance_score,
                     top_performing_articles, poor_performing_articles,
                     improvement_recommendations, trends_identified)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    datetime.now().date(),
                    overall_analysis.get('total_articles', 0),
                    overall_analysis.get('averages', {}).get('engagement_score', 0),
                    json.dumps(overall_analysis.get('top_performing', [])),
                    json.dumps(overall_analysis.get('poor_performing', [])),
                    json.dumps([]),  # سيتم ملؤها لاحقاً
                    json.dumps(overall_analysis.get('trends_identified', []))
                ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ التحليل اليومي", e)

# إنشاء مثيل عام لمحلل الأداء
article_performance_analyzer = ArticlePerformanceAnalyzer()
