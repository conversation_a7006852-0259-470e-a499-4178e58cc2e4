# مدير البحث الذكي المحسن مع البدائل المتعددة
import asyncio
import time
import json
import hashlib
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import os

from .logger import logger
from .serpapi_search import serpapi_search
from .tavily_search import tavily_search
try:
    from .google_search_manager import google_search_manager
except ImportError:
    google_search_manager = None
from .advanced_web_scraper import advanced_web_scraper
from .database import db
from .api_key_manager import ApiKeyManager
from config.settings import BotConfig

class SearchPriority(Enum):
    """أولويات البحث"""
    FREE = 1        # مصادر مجانية
    LOW_COST = 2    # مصادر منخفضة التكلفة
    PREMIUM = 3     # مصادر مدفوعة متقدمة
    EMERGENCY = 4   # حالات الطوارئ

@dataclass
class SearchRequest:
    """طلب بحث"""
    query: str
    max_results: int = 10
    priority: SearchPriority = SearchPriority.FREE
    search_type: str = "general"  # general, news, academic, etc.
    filters: Dict = None
    cache_duration: int = 3600  # ساعة واحدة افتراضياً
    
    def __post_init__(self):
        if self.filters is None:
            self.filters = {}

@dataclass
class SearchResult:
    """نتيجة بحث"""
    title: str
    url: str
    content: str
    summary: str
    source: str
    relevance_score: float
    quality_score: float
    timestamp: datetime
    search_engine: str
    cached: bool = False
    
class SmartSearchManager:
    """مدير البحث الذكي المحسن"""
    
    def __init__(self):
        self.cache_db_path = "cache/smart_search_cache.db"
        self.usage_stats = {
            'total_searches': 0,
            'cache_hits': 0,
            'api_calls': 0,
            'cost_saved': 0.0,
            'engines_used': {}
        }
        
        # إعدادات التخزين المؤقت
        self.cache_settings = {
            'max_cache_size': 10000,  # عدد النتائج المخزنة
            'default_ttl': 3600,     # ساعة واحدة
            'cleanup_interval': 86400 # يوم واحد
        }
        
        # إعدادات معدل الطلبات - Tavily له الأولوية الأولى
        self.rate_limits = {
            'tavily': {'calls_per_minute': 15, 'daily_limit': 800},    # أولوية عالية
            'google': {'calls_per_minute': 50, 'daily_limit': 5000},   # بديل
            'serpapi': {'calls_per_minute': 10, 'daily_limit': 500}    # للحالات المتقدمة
        }
        
        # تتبع الاستخدام
        self.usage_tracker = {}
        
        # تهيئة قاعدة بيانات التخزين المؤقت
        self._init_cache_db()
        
        logger.info("🧠 تم تهيئة مدير البحث الذكي المحسن")
    
    def _init_cache_db(self):
        """تهيئة قاعدة بيانات التخزين المؤقت"""
        try:
            os.makedirs(os.path.dirname(self.cache_db_path), exist_ok=True)
            
            with sqlite3.connect(self.cache_db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS search_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        query_hash TEXT UNIQUE NOT NULL,
                        query TEXT NOT NULL,
                        results TEXT NOT NULL,
                        timestamp REAL NOT NULL,
                        ttl INTEGER NOT NULL,
                        access_count INTEGER DEFAULT 1,
                        last_accessed REAL NOT NULL
                    )
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_query_hash ON search_cache(query_hash)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp ON search_cache(timestamp)
                ''')
                
            logger.info("✅ تم تهيئة قاعدة بيانات التخزين المؤقت")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة بيانات التخزين المؤقت: {e}")
    
    def _generate_cache_key(self, request: SearchRequest) -> str:
        """إنشاء مفتاح تخزين مؤقت"""
        cache_data = {
            'query': request.query.lower().strip(),
            'max_results': request.max_results,
            'search_type': request.search_type,
            'filters': request.filters
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    async def search(self, request: SearchRequest) -> List[SearchResult]:
        """البحث الذكي الرئيسي"""
        try:
            self.usage_stats['total_searches'] += 1
            start_time = time.time()
            
            logger.info(f"🔍 بدء البحث الذكي: '{request.query}' (أولوية: {request.priority.name})")
            
            # 1. فحص التخزين المؤقت أولاً
            cached_results = await self._get_cached_results(request)
            if cached_results:
                self.usage_stats['cache_hits'] += 1
                logger.info(f"📦 استخدام نتائج مخزنة مؤقتاً ({len(cached_results)} نتيجة)")
                return cached_results
            
            # 2. البحث التدريجي حسب الأولوية
            results = await self._progressive_search(request)
            
            # 3. تحليل وترتيب النتائج
            analyzed_results = await self._analyze_and_rank_results(results, request.query)
            
            # 4. حفظ في التخزين المؤقت
            if analyzed_results:
                await self._cache_results(request, analyzed_results)
            
            # 5. تحديث الإحصائيات
            search_time = time.time() - start_time
            logger.info(f"✅ اكتمل البحث في {search_time:.2f} ثانية - {len(analyzed_results)} نتيجة")
            
            return analyzed_results
            
        except Exception as e:
            logger.error(f"❌ فشل في البحث الذكي: {e}")
            return []
    
    async def _get_cached_results(self, request: SearchRequest) -> Optional[List[SearchResult]]:
        """الحصول على النتائج المخزنة مؤقتاً"""
        try:
            cache_key = self._generate_cache_key(request)
            current_time = time.time()
            
            with sqlite3.connect(self.cache_db_path) as conn:
                cursor = conn.execute('''
                    SELECT results, timestamp, ttl, access_count 
                    FROM search_cache 
                    WHERE query_hash = ?
                ''', (cache_key,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                results_json, timestamp, ttl, access_count = row
                
                # فحص انتهاء الصلاحية
                if current_time - timestamp > ttl:
                    # حذف النتائج المنتهية الصلاحية
                    conn.execute('DELETE FROM search_cache WHERE query_hash = ?', (cache_key,))
                    return None
                
                # تحديث عداد الوصول
                conn.execute('''
                    UPDATE search_cache 
                    SET access_count = access_count + 1, last_accessed = ?
                    WHERE query_hash = ?
                ''', (current_time, cache_key))
                
                # تحويل JSON إلى كائنات SearchResult
                results_data = json.loads(results_json)
                results = []
                
                for result_data in results_data:
                    result = SearchResult(
                        title=result_data['title'],
                        url=result_data['url'],
                        content=result_data['content'],
                        summary=result_data['summary'],
                        source=result_data['source'],
                        relevance_score=result_data['relevance_score'],
                        quality_score=result_data['quality_score'],
                        timestamp=datetime.fromisoformat(result_data['timestamp']),
                        search_engine=result_data['search_engine'],
                        cached=True
                    )
                    results.append(result)
                
                return results
                
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على النتائج المخزنة: {e}")
            return None
    
    async def _cache_results(self, request: SearchRequest, results: List[SearchResult]):
        """حفظ النتائج في التخزين المؤقت"""
        try:
            cache_key = self._generate_cache_key(request)
            current_time = time.time()
            
            # تحويل النتائج إلى JSON
            results_data = []
            for result in results:
                result_data = {
                    'title': result.title,
                    'url': result.url,
                    'content': result.content,
                    'summary': result.summary,
                    'source': result.source,
                    'relevance_score': result.relevance_score,
                    'quality_score': result.quality_score,
                    'timestamp': result.timestamp.isoformat(),
                    'search_engine': result.search_engine
                }
                results_data.append(result_data)
            
            results_json = json.dumps(results_data, ensure_ascii=False)
            
            with sqlite3.connect(self.cache_db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO search_cache 
                    (query_hash, query, results, timestamp, ttl, last_accessed)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (cache_key, request.query, results_json, current_time, 
                      request.cache_duration, current_time))
                
            logger.debug(f"💾 تم حفظ {len(results)} نتيجة في التخزين المؤقت")
            
        except Exception as e:
            logger.error(f"❌ فشل في حفظ النتائج في التخزين المؤقت: {e}")

    async def _progressive_search(self, request: SearchRequest) -> List[Dict]:
        """البحث التدريجي حسب الأولوية - Tavily أولاً، ثم Google كبديل"""
        results = []

        try:
            # المرحلة 1: Tavily (الأولوية الأولى - الأفضل للبحث العميق)
            if request.priority.value >= SearchPriority.FREE.value:
                tavily_results = await self._search_tavily_primary(request)
                results.extend(tavily_results)
                logger.info(f"🔍 Tavily (أساسي): {len(tavily_results)} نتيجة")

            # فحص ما إذا كانت النتائج كافية من Tavily
            if len(results) >= request.max_results:
                return results[:request.max_results]

            # المرحلة 2: المصادر المجانية (إذا لم تكف نتائج Tavily)
            if len(results) < request.max_results // 2:  # إذا كانت النتائج قليلة
                free_results = await self._search_free_sources(request)
                results.extend(free_results)
                logger.info(f"🆓 المصادر المجانية: {len(free_results)} نتيجة")

            # المرحلة 3: Google Search (كخيار بديل)
            if request.priority.value >= SearchPriority.LOW_COST.value and len(results) < request.max_results:
                if await self._can_use_api('google'):
                    google_results = await self._search_google_fallback(request)
                    results.extend(google_results)
                    logger.info(f"🔄 Google (بديل): {len(google_results)} نتيجة")

            # المرحلة 4: SerpAPI (للحالات المتقدمة)
            if request.priority.value >= SearchPriority.PREMIUM.value and len(results) < request.max_results:
                serpapi_results = await self._search_serpapi_premium(request)
                results.extend(serpapi_results)
                logger.info(f"⭐ SerpAPI (متقدم): {len(serpapi_results)} نتيجة")

            # المرحلة 5: حالات الطوارئ (جميع المصادر)
            if request.priority.value >= SearchPriority.EMERGENCY.value and len(results) < 3:
                emergency_results = await self._search_emergency_sources(request)
                results.extend(emergency_results)
                logger.info(f"🚨 مصادر الطوارئ: {len(emergency_results)} نتيجة")

            return results[:request.max_results]

        except Exception as e:
            logger.error(f"❌ فشل في البحث التدريجي: {e}")
            return results

    async def _search_free_sources(self, request: SearchRequest) -> List[Dict]:
        """البحث في المصادر المجانية"""
        results = []

        try:
            # البحث في المواقع المباشرة
            if advanced_web_scraper:
                web_results = await advanced_web_scraper.comprehensive_gaming_search(
                    request.query,
                    max_results=request.max_results // 2
                )
                results.extend(web_results)

            # البحث في RSS feeds
            # يمكن إضافة مصادر RSS مجانية هنا

            return results

        except Exception as e:
            logger.error(f"❌ فشل في البحث المجاني: {e}")
            return []

    async def _search_tavily_primary(self, request: SearchRequest) -> List[Dict]:
        """البحث الأساسي باستخدام Tavily (الأولوية الأولى)"""
        try:
            if not await self._can_use_api('tavily') or not tavily_search.enabled:
                logger.warning("⚠️ Tavily غير متاح، الانتقال للبديل")
                return []

            self.usage_stats['api_calls'] += 1
            self.usage_stats['engines_used']['tavily'] = self.usage_stats['engines_used'].get('tavily', 0) + 1

            # استخدام Tavily للبحث العميق المتقدم
            results = await tavily_search.search(
                query=f"{request.query} gaming news",
                search_depth="advanced",
                max_results=request.max_results,
                include_answer=True,
                include_raw_content=True
            )

            logger.info(f"🔍 Tavily (أساسي): تم العثور على {len(results or [])} نتيجة")
            return results or []

        except Exception as e:
            logger.error(f"❌ فشل في البحث الأساسي عبر Tavily: {e}")
            return []

    async def _search_google_fallback(self, request: SearchRequest) -> List[Dict]:
        """البحث البديل باستخدام Google Search API"""
        try:
            if not google_search_manager or not google_search_manager.enabled:
                logger.warning("⚠️ Google Search غير متاح")
                return []

            self.usage_stats['api_calls'] += 1
            self.usage_stats['engines_used']['google'] = self.usage_stats['engines_used'].get('google', 0) + 1

            # استخدام Google كبديل عندما لا تكفي نتائج Tavily
            results = await google_search_manager.search(
                request.query,
                num_results=min(request.max_results, 8)  # حد أقصى 8 نتائج من Google
            )

            logger.info(f"🔄 Google (بديل): تم العثور على {len(results or [])} نتيجة")
            return results or []

        except Exception as e:
            logger.error(f"❌ فشل في البحث البديل عبر Google: {e}")
            return []

    async def _search_serpapi_premium(self, request: SearchRequest) -> List[Dict]:
        """البحث المتقدم باستخدام SerpAPI"""
        try:
            if not await self._can_use_api('serpapi') or not serpapi_search.enabled:
                return []

            self.usage_stats['api_calls'] += 1
            self.usage_stats['engines_used']['serpapi'] = self.usage_stats['engines_used'].get('serpapi', 0) + 1

            # استخدام SerpAPI للحالات المتقدمة فقط
            results = await serpapi_search.search(
                query=f"{request.query} gaming news",
                num_results=min(request.max_results // 2, 5),
                tbm='nws' if request.search_type == 'news' else None
            )

            # تأخير لاحترام حدود المعدل
            await asyncio.sleep(3)

            logger.info(f"⭐ SerpAPI (متقدم): تم العثور على {len(results or [])} نتيجة")
            return results or []

        except Exception as e:
            logger.error(f"❌ فشل في البحث المتقدم عبر SerpAPI: {e}")
            return []

    async def _search_emergency_sources(self, request: SearchRequest) -> List[Dict]:
        """البحث في مصادر الطوارئ - Tavily أولاً حتى في الطوارئ"""
        results = []

        try:
            # استخدام جميع المصادر المتاحة بدون قيود - لكن بالترتيب الصحيح
            logger.warning("🚨 تفعيل وضع الطوارئ للبحث")

            # ترتيب الأولوية: Tavily → Google → SerpAPI
            emergency_apis = [
                ('tavily', tavily_search),
                ('google', google_search_manager),
                ('serpapi', serpapi_search)
            ]

            for api_name, api_service in emergency_apis:
                try:
                    if api_name == 'tavily' and api_service and api_service.enabled:
                        api_results = await api_service.search(
                            query=request.query,
                            max_results=8,
                            search_depth="basic"  # بحث أساسي في الطوارئ
                        )
                        if api_results:
                            results.extend(api_results)
                            logger.info(f"🚨 Tavily (طوارئ): {len(api_results)} نتيجة")

                    elif api_name == 'google' and api_service and api_service.enabled:
                        api_results = await api_service.search(request.query, num_results=6)
                        if api_results:
                            results.extend(api_results)
                            logger.info(f"🚨 Google (طوارئ): {len(api_results)} نتيجة")

                    elif api_name == 'serpapi' and api_service and api_service.enabled:
                        api_results = await api_service.search(request.query, num_results=5)
                        if api_results:
                            results.extend(api_results)
                            logger.info(f"🚨 SerpAPI (طوارئ): {len(api_results)} نتيجة")

                    # إذا حصلنا على نتائج كافية، توقف
                    if len(results) >= 5:
                        break

                except Exception as e:
                    logger.debug(f"فشل في {api_name} (طوارئ): {e}")
                    continue

            return results

        except Exception as e:
            logger.error(f"❌ فشل في بحث الطوارئ: {e}")
            return []

    async def _can_use_api(self, api_name: str) -> bool:
        """فحص ما إذا كان يمكن استخدام API"""
        try:
            current_time = time.time()

            # تهيئة تتبع الاستخدام إذا لم يكن موجوداً
            if api_name not in self.usage_tracker:
                self.usage_tracker[api_name] = {
                    'calls_today': 0,
                    'calls_this_minute': 0,
                    'last_call_time': 0,
                    'last_reset_day': current_time,
                    'last_reset_minute': current_time
                }

            tracker = self.usage_tracker[api_name]
            limits = self.rate_limits.get(api_name, {'calls_per_minute': 10, 'daily_limit': 100})

            # إعادة تعيين العدادات اليومية
            if current_time - tracker['last_reset_day'] >= 86400:  # 24 ساعة
                tracker['calls_today'] = 0
                tracker['last_reset_day'] = current_time

            # إعادة تعيين العدادات الدقيقة
            if current_time - tracker['last_reset_minute'] >= 60:  # دقيقة واحدة
                tracker['calls_this_minute'] = 0
                tracker['last_reset_minute'] = current_time

            # فحص الحدود
            if tracker['calls_today'] >= limits['daily_limit']:
                logger.warning(f"⚠️ تم تجاوز الحد اليومي لـ {api_name}")
                return False

            if tracker['calls_this_minute'] >= limits['calls_per_minute']:
                logger.warning(f"⚠️ تم تجاوز حد الدقيقة لـ {api_name}")
                return False

            # فحص التأخير المطلوب بين الطلبات
            min_delay = 60 / limits['calls_per_minute']  # الحد الأدنى للتأخير
            if current_time - tracker['last_call_time'] < min_delay:
                wait_time = min_delay - (current_time - tracker['last_call_time'])
                logger.debug(f"⏰ انتظار {wait_time:.2f} ثانية لـ {api_name}")
                await asyncio.sleep(wait_time)

            # تحديث العدادات
            tracker['calls_today'] += 1
            tracker['calls_this_minute'] += 1
            tracker['last_call_time'] = time.time()

            return True

        except Exception as e:
            logger.error(f"❌ فشل في فحص حدود API {api_name}: {e}")
            return False

    async def _analyze_and_rank_results(self, results: List[Dict], query: str) -> List[SearchResult]:
        """تحليل وترتيب النتائج حسب الجودة والصلة"""
        try:
            analyzed_results = []
            query_words = query.lower().split()

            for result in results:
                try:
                    # تحويل النتيجة إلى SearchResult
                    search_result = self._convert_to_search_result(result)

                    # حساب نقاط الصلة
                    relevance_score = self._calculate_relevance_score(search_result, query_words)

                    # حساب نقاط الجودة
                    quality_score = self._calculate_quality_score(search_result)

                    # تحديث النقاط
                    search_result.relevance_score = relevance_score
                    search_result.quality_score = quality_score

                    analyzed_results.append(search_result)

                except Exception as e:
                    logger.debug(f"فشل في تحليل نتيجة: {e}")
                    continue

            # ترتيب النتائج حسب النقاط المجمعة
            analyzed_results.sort(
                key=lambda x: (x.quality_score * 0.6 + x.relevance_score * 0.4),
                reverse=True
            )

            logger.info(f"📊 تم تحليل وترتيب {len(analyzed_results)} نتيجة")
            return analyzed_results

        except Exception as e:
            logger.error(f"❌ فشل في تحليل النتائج: {e}")
            return []

    def _convert_to_search_result(self, result: Dict) -> SearchResult:
        """تحويل نتيجة خام إلى SearchResult"""
        return SearchResult(
            title=result.get('title', ''),
            url=result.get('url', result.get('link', '')),
            content=result.get('content', ''),
            summary=result.get('summary', result.get('snippet', '')),
            source=result.get('source', 'Unknown'),
            relevance_score=0.0,
            quality_score=0.0,
            timestamp=datetime.now(),
            search_engine=result.get('search_engine', 'Unknown'),
            cached=False
        )

    def _calculate_relevance_score(self, result: SearchResult, query_words: List[str]) -> float:
        """حساب نقاط الصلة"""
        try:
            score = 0.0
            title_lower = result.title.lower()
            content_lower = result.content.lower()
            summary_lower = result.summary.lower()

            for word in query_words:
                # نقاط العنوان (أهمية عالية)
                if word in title_lower:
                    score += 10.0

                # نقاط الملخص (أهمية متوسطة)
                if word in summary_lower:
                    score += 5.0

                # نقاط المحتوى (أهمية منخفضة)
                if word in content_lower:
                    score += 2.0

            # تطبيع النقاط (0-100)
            max_possible_score = len(query_words) * 17.0  # 10+5+2 لكل كلمة
            if max_possible_score > 0:
                score = min(100.0, (score / max_possible_score) * 100.0)

            return score

        except Exception as e:
            logger.debug(f"فشل في حساب نقاط الصلة: {e}")
            return 0.0

    def _calculate_quality_score(self, result: SearchResult) -> float:
        """حساب نقاط الجودة"""
        try:
            score = 0.0

            # طول العنوان (عناوين متوسطة الطول أفضل)
            title_length = len(result.title)
            if 20 <= title_length <= 100:
                score += 20.0
            elif 10 <= title_length <= 150:
                score += 10.0

            # طول المحتوى
            content_length = len(result.content)
            if content_length > 500:
                score += 30.0
            elif content_length > 200:
                score += 20.0
            elif content_length > 100:
                score += 10.0

            # جودة المصدر
            trusted_sources = [
                'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
                'pcgamer.com', 'eurogamer.net', 'destructoid.com'
            ]

            if any(source in result.url.lower() for source in trusted_sources):
                score += 25.0

            # وجود تاريخ حديث
            if result.timestamp and (datetime.now() - result.timestamp).days <= 7:
                score += 15.0

            # جودة الملخص
            if len(result.summary) > 50:
                score += 10.0

            return min(100.0, score)

        except Exception as e:
            logger.debug(f"فشل في حساب نقاط الجودة: {e}")
            return 0.0

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        try:
            # حساب معدل نجاح التخزين المؤقت
            cache_hit_rate = 0.0
            if self.usage_stats['total_searches'] > 0:
                cache_hit_rate = (self.usage_stats['cache_hits'] / self.usage_stats['total_searches']) * 100

            # حساب التوفير في التكلفة (تقديري)
            estimated_cost_per_api_call = 0.01  # سنت واحد لكل طلب
            cost_saved = self.usage_stats['cache_hits'] * estimated_cost_per_api_call

            return {
                'total_searches': self.usage_stats['total_searches'],
                'cache_hits': self.usage_stats['cache_hits'],
                'cache_hit_rate': round(cache_hit_rate, 2),
                'api_calls': self.usage_stats['api_calls'],
                'cost_saved_usd': round(cost_saved, 4),
                'engines_used': self.usage_stats['engines_used'],
                'usage_tracker': self.usage_tracker
            }

        except Exception as e:
            logger.error(f"❌ فشل في جمع الإحصائيات: {e}")
            return {}

    async def cleanup_cache(self):
        """تنظيف التخزين المؤقت"""
        try:
            current_time = time.time()

            with sqlite3.connect(self.cache_db_path) as conn:
                # حذف النتائج المنتهية الصلاحية
                cursor = conn.execute('''
                    DELETE FROM search_cache
                    WHERE timestamp + ttl < ?
                ''', (current_time,))

                deleted_expired = cursor.rowcount

                # حذف النتائج الأقل استخداماً إذا تجاوز الحد الأقصى
                cursor = conn.execute('SELECT COUNT(*) FROM search_cache')
                total_count = cursor.fetchone()[0]

                if total_count > self.cache_settings['max_cache_size']:
                    excess_count = total_count - self.cache_settings['max_cache_size']
                    conn.execute('''
                        DELETE FROM search_cache
                        WHERE id IN (
                            SELECT id FROM search_cache
                            ORDER BY access_count ASC, last_accessed ASC
                            LIMIT ?
                        )
                    ''', (excess_count,))

                    deleted_excess = cursor.rowcount
                    logger.info(f"🧹 تم حذف {deleted_excess} نتيجة زائدة من التخزين المؤقت")

                if deleted_expired > 0:
                    logger.info(f"🧹 تم حذف {deleted_expired} نتيجة منتهية الصلاحية")

        except Exception as e:
            logger.error(f"❌ فشل في تنظيف التخزين المؤقت: {e}")

    async def reset_usage_stats(self):
        """إعادة تعيين إحصائيات الاستخدام"""
        self.usage_stats = {
            'total_searches': 0,
            'cache_hits': 0,
            'api_calls': 0,
            'cost_saved': 0.0,
            'engines_used': {}
        }
        self.usage_tracker = {}
        logger.info("🔄 تم إعادة تعيين إحصائيات الاستخدام")

    # ===== محركات البحث الجديدة =====

    async def _search_with_scraperapi(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث باستخدام ScraperAPI"""
        try:
            if not hasattr(BotConfig, 'SCRAPERAPI_KEYS') or not any(BotConfig.SCRAPERAPI_KEYS):
                return []

            api_key = next((k for k in BotConfig.SCRAPERAPI_KEYS if k), None)
            if not api_key:
                return []

            # استخدام ScraperAPI للبحث في Google
            search_url = f"https://api.scraperapi.com/?api_key={api_key}&url=https://www.google.com/search?q={query}+gaming+news&num={max_results}"

            async with aiohttp.ClientSession() as session:
                async with session.get(search_url) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        # معالجة HTML وتحويله إلى نتائج منظمة
                        results = self._parse_google_html(html_content, query)
                        logger.info(f"✅ ScraperAPI: {len(results)} نتيجة")
                        return results
                    else:
                        logger.warning(f"⚠️ ScraperAPI فشل: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في ScraperAPI: {e}")
            return []

    async def _search_with_zyte(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث باستخدام Zyte (Scrapinghub)"""
        try:
            if not hasattr(BotConfig, 'ZYTE_API_KEYS') or not any(BotConfig.ZYTE_API_KEYS):
                return []

            api_key = next((k for k in BotConfig.ZYTE_API_KEYS if k), None)
            if not api_key:
                return []

            # استخدام Zyte API للاستخراج المتقدم
            zyte_url = "https://api.zyte.com/v1/extract"

            payload = {
                "url": f"https://www.google.com/search?q={query}+gaming+news&num={max_results}",
                "httpResponseBody": True,
                "httpResponseHeaders": True
            }

            headers = {
                "Authorization": f"Basic {api_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(zyte_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        # معالجة البيانات المستخرجة
                        results = self._parse_zyte_response(data, query)
                        logger.info(f"✅ Zyte: {len(results)} نتيجة")
                        return results
                    else:
                        logger.warning(f"⚠️ Zyte فشل: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في Zyte: {e}")
            return []

    async def _search_with_contextualweb(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث باستخدام ContextualWeb Search API"""
        try:
            if not hasattr(BotConfig, 'CONTEXTUALWEB_KEYS') or not any(BotConfig.CONTEXTUALWEB_KEYS):
                return []

            api_key = next((k for k in BotConfig.CONTEXTUALWEB_KEYS if k), None)
            if not api_key:
                return []

            # استخدام ContextualWeb API
            api_url = "https://contextualwebsearch-websearch-v1.p.rapidapi.com/api/search/NewsSearchAPI"

            headers = {
                "X-RapidAPI-Key": api_key,
                "X-RapidAPI-Host": "contextualwebsearch-websearch-v1.p.rapidapi.com"
            }

            params = {
                "q": f"{query} gaming news",
                "pageNumber": 1,
                "pageSize": max_results,
                "autoCorrect": True,
                "safeSearch": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._parse_contextualweb_response(data, query)
                        logger.info(f"✅ ContextualWeb: {len(results)} نتيجة")
                        return results
                    else:
                        logger.warning(f"⚠️ ContextualWeb فشل: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في ContextualWeb: {e}")
            return []

    async def _search_with_serper_dev(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث باستخدام Serper.dev"""
        try:
            if not hasattr(BotConfig, 'SERPER_DEV_KEYS') or not any(BotConfig.SERPER_DEV_KEYS):
                return []

            api_key = next((k for k in BotConfig.SERPER_DEV_KEYS if k), None)
            if not api_key:
                return []

            # استخدام Serper.dev API
            api_url = "https://google.serper.dev/search"

            headers = {
                "X-API-KEY": api_key,
                "Content-Type": "application/json"
            }

            payload = {
                "q": f"{query} gaming news",
                "num": max_results,
                "gl": "us",
                "hl": "en"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(api_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._parse_serper_response(data, query)
                        logger.info(f"✅ Serper.dev: {len(results)} نتيجة")
                        return results
                    else:
                        logger.warning(f"⚠️ Serper.dev فشل: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في Serper.dev: {e}")
            return []

    async def _search_with_google_custom(self, query: str, max_results: int = 10) -> List[Dict]:
        """البحث باستخدام Google Custom Search JSON API"""
        try:
            if not hasattr(BotConfig, 'GOOGLE_CUSTOM_SEARCH_KEYS') or not any(BotConfig.GOOGLE_CUSTOM_SEARCH_KEYS):
                return []

            api_key = next((k for k in BotConfig.GOOGLE_CUSTOM_SEARCH_KEYS if k), None)
            engine_id = getattr(BotConfig, 'GOOGLE_CUSTOM_SEARCH_ENGINE_ID', '')

            if not api_key or not engine_id:
                return []

            # استخدام Google Custom Search JSON API
            api_url = "https://www.googleapis.com/customsearch/v1"

            params = {
                "key": api_key,
                "cx": engine_id,
                "q": f"{query} gaming news",
                "num": min(max_results, 10),  # Google Custom Search يدعم حتى 10 نتائج
                "safe": "off",
                "lr": "lang_en"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._parse_google_custom_response(data, query)
                        logger.info(f"✅ Google Custom Search: {len(results)} نتيجة")
                        return results
                    else:
                        logger.warning(f"⚠️ Google Custom Search فشل: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"❌ خطأ في Google Custom Search: {e}")
            return []

    # ===== دوال معالجة الاستجابات =====

    def _parse_google_html(self, html_content: str, query: str) -> List[Dict]:
        """معالجة HTML من Google Search"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            results = []

            # البحث عن نتائج Google
            search_results = soup.find_all('div', class_='g')

            for result in search_results[:10]:  # أفضل 10 نتائج
                try:
                    title_elem = result.find('h3')
                    link_elem = result.find('a')
                    snippet_elem = result.find('span', class_='st') or result.find('div', class_='s')

                    if title_elem and link_elem:
                        title = title_elem.get_text(strip=True)
                        url = link_elem.get('href', '')
                        snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''

                        if url.startswith('/url?q='):
                            url = url.split('/url?q=')[1].split('&')[0]

                        results.append({
                            'title': title,
                            'url': url,
                            'content': snippet,
                            'summary': snippet[:200] + "..." if len(snippet) > 200 else snippet,
                            'source': 'ScraperAPI-Google',
                            'search_engine': 'ScraperAPI',
                            'published_date': datetime.now(),
                            'relevance_score': 7.0,
                            'search_keyword': query
                        })
                except Exception as e:
                    continue

            return results
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة HTML من Google: {e}")
            return []

    def _parse_zyte_response(self, data: Dict, query: str) -> List[Dict]:
        """معالجة استجابة Zyte"""
        try:
            results = []

            if 'httpResponseBody' in data:
                html_content = data['httpResponseBody']
                # استخدام نفس معالج Google HTML
                return self._parse_google_html(html_content, query)

            return results
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة Zyte: {e}")
            return []

    def _parse_contextualweb_response(self, data: Dict, query: str) -> List[Dict]:
        """معالجة استجابة ContextualWeb"""
        try:
            results = []

            if 'value' in data:
                for item in data['value']:
                    try:
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('url', ''),
                            'content': item.get('body', ''),
                            'summary': item.get('snippet', ''),
                            'source': 'ContextualWeb',
                            'search_engine': 'ContextualWeb',
                            'published_date': item.get('datePublished', datetime.now()),
                            'relevance_score': 6.5,
                            'search_keyword': query,
                            'image_url': item.get('image', {}).get('url', '') if item.get('image') else ''
                        })
                    except Exception as e:
                        continue

            return results
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة ContextualWeb: {e}")
            return []

    def _parse_serper_response(self, data: Dict, query: str) -> List[Dict]:
        """معالجة استجابة Serper.dev"""
        try:
            results = []

            if 'organic' in data:
                for item in data['organic']:
                    try:
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'content': item.get('snippet', ''),
                            'summary': item.get('snippet', ''),
                            'source': 'Serper.dev',
                            'search_engine': 'Serper.dev',
                            'published_date': datetime.now(),
                            'relevance_score': 8.0,
                            'search_keyword': query,
                            'position': item.get('position', 0)
                        })
                    except Exception as e:
                        continue

            return results
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة Serper.dev: {e}")
            return []

    def _parse_google_custom_response(self, data: Dict, query: str) -> List[Dict]:
        """معالجة استجابة Google Custom Search"""
        try:
            results = []

            if 'items' in data:
                for item in data['items']:
                    try:
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'content': item.get('snippet', ''),
                            'summary': item.get('snippet', ''),
                            'source': 'Google Custom Search',
                            'search_engine': 'Google Custom Search',
                            'published_date': datetime.now(),
                            'relevance_score': 6.0,
                            'search_keyword': query,
                            'display_link': item.get('displayLink', '')
                        })
                    except Exception as e:
                        continue

            return results
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة استجابة Google Custom Search: {e}")
            return []

# إنشاء مثيل عام
smart_search_manager = SmartSearchManager()
