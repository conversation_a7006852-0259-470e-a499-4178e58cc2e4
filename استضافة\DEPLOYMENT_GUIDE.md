# دليل نشر وكيل أخبار الألعاب على Hugging Face

## خطوات النشر

### 1. إعداد حساب Hugging Face
- قم بإنشاء حساب على [Hugging Face](https://huggingface.co/)
- احصل على Access Token من إعدادات الحساب

### 2. إنشاء Space جديد
1. اذهب إلى [Hugging Face Spaces](https://huggingface.co/spaces)
2. انقر على "Create new Space"
3. اختر اسماً للمشروع (مثل: `gaming-news-agent`)
4. اختر "Gradio" كـ SDK
5. اختر "Public" أو "Private" حسب الحاجة

### 3. رفع الملفات
قم برفع جميع الملفات من مجلد "استضافة" إلى Space:

```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/gaming-news-agent
cd gaming-news-agent
# انسخ جميع الملفات من مجلد استضافة
cp -r استضافة/* .
git add .
git commit -m "Initial commit: Gaming News Agent"
git push
```

### 4. إعداد متغيرات البيئة
في إعدادات Space، أضف المتغيرات التالية:

#### مفاتيح API المطلوبة:
- `OPENAI_API_KEY`: مفتاح OpenAI
- `ANTHROPIC_API_KEY`: مفتاح Anthropic
- `GOOGLE_API_KEY`: مفتاح Google للبحث
- `BLOGGER_CLIENT_ID`: معرف عميل Blogger
- `BLOGGER_CLIENT_SECRET`: سر عميل Blogger
- `TELEGRAM_BOT_TOKEN`: رمز بوت Telegram (اختياري)

#### إعدادات إضافية:
- `ENVIRONMENT`: `production`
- `DEBUG`: `false`
- `LOG_LEVEL`: `INFO`

### 5. إعداد ملفات التكوين

#### تحديث `config/api_config.py`:
```python
import os

# مفاتيح API من متغيرات البيئة
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
BLOGGER_CLIENT_ID = os.getenv('BLOGGER_CLIENT_ID')
BLOGGER_CLIENT_SECRET = os.getenv('BLOGGER_CLIENT_SECRET')
```

#### تحديث `config/settings.py`:
```python
import os

SETTINGS = {
    'environment': os.getenv('ENVIRONMENT', 'development'),
    'debug': os.getenv('DEBUG', 'false').lower() == 'true',
    'log_level': os.getenv('LOG_LEVEL', 'INFO'),
    'host': '0.0.0.0',
    'port': 7860
}
```

### 6. اختبار التطبيق
1. انتظر حتى يكتمل البناء
2. افتح رابط Space
3. تحقق من عمل جميع الوظائف
4. اختبر البحث والنشر

### 7. مراقبة الأداء
- راقب سجلات التطبيق في Hugging Face
- تحقق من استخدام الموارد
- راقب أي أخطاء أو تحذيرات

## نصائح مهمة

### الأمان:
- لا تضع مفاتيح API في الكود مباشرة
- استخدم متغيرات البيئة دائماً
- تأكد من أن Space خاص إذا كان يحتوي على بيانات حساسة

### الأداء:
- استخدم التخزين المؤقت للبيانات المتكررة
- قم بتحسين استعلامات قاعدة البيانات
- راقب استخدام الذاكرة والمعالج

### الصيانة:
- قم بتحديث التبعيات بانتظام
- راجع السجلات دورياً
- اختبر الوظائف الجديدة قبل النشر

## استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في تثبيت التبعيات:
```bash
# تحقق من ملف requirements_huggingface.txt
# تأكد من توافق الإصدارات
```

#### خطأ في الاتصال بـ API:
```bash
# تحقق من صحة مفاتيح API
# تأكد من تعيين متغيرات البيئة
```

#### مشاكل في قاعدة البيانات:
```bash
# تحقق من أذونات الملفات
# تأكد من إنشاء مجلد data
```

## الدعم
للحصول على المساعدة:
1. راجع سجلات Hugging Face
2. تحقق من وثائق Gradio
3. راجع وثائق APIs المستخدمة
4. اتصل بفريق الدعم إذا لزم الأمر
