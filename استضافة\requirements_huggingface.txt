# متطلبات Hugging Face Spaces
gradio>=4.0.0
fastapi>=0.100.0
uvicorn>=0.20.0

# المكتبات الأساسية
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
python-dotenv>=1.0.0
pydantic>=2.0.0

# قواعد البيانات
sqlite3
sqlalchemy>=2.0.0

# معالجة النصوص والصور
Pillow>=10.0.0
opencv-python-headless>=4.8.0
numpy>=1.24.0
pandas>=2.0.0

# APIs والشبكات
google-api-python-client>=2.100.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=1.0.0
httpx>=0.24.0
aiohttp>=3.8.0

# الذكاء الاصطناعي
openai>=1.0.0
anthropic>=0.3.0

# معالجة التواريخ والوقت
python-dateutil>=2.8.0
pytz>=2023.3

# أدوات إضافية
pyyaml>=6.0
jsonschema>=4.17.0
validators>=0.20.0
urllib3>=2.0.0
certifi>=2023.7.22

# تحسين الأداء
cachetools>=5.3.0
redis>=4.6.0

# أمان
cryptography>=41.0.0
bcrypt>=4.0.0

# لوجينغ ومراقبة
structlog>=23.1.0
rich>=13.5.0

# واجهة المستخدم
jinja2>=3.1.0
markupsafe>=2.1.0

# معالجة الملفات
chardet>=5.2.0
python-magic>=0.4.27

# شبكات اجتماعية
tweepy>=4.14.0
python-telegram-bot>=20.0

# SEO وتحليلات
advertools>=0.14.0
yake>=0.4.8

# أدوات التطوير (اختيارية)
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0
