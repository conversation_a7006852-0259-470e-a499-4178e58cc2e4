# وحدة البحث عبر الويب باستخدام Google Custom Search API
import requests
from typing import List, Dict, Optional
from .logger import logger
from config.settings import google_api_manager, google_search_api_manager

class WebSearch:
    """فئة لإجراء عمليات بحث عبر الويب وجمع معلومات محدثة."""

    def __init__(self, search_engine_id: str):
        """
        تهيئة الباحث.
        :param search_engine_id: معرف محرك البحث المخصص (CX).
        """
        # استخدام مدير مفاتيح Google Search المخصص أولاً، ثم العام
        self.api_manager = google_search_api_manager or google_api_manager

        if not self.api_manager:
            raise Exception("Google API Key Manager is not initialized.")

        self.search_engine_id = search_engine_id
        self.base_url = "https://www.googleapis.com/customsearch/v1"

        if not self.search_engine_id:
            logger.warning("⚠️ معرف محرك البحث غير موجود. سيتم تعطيل ميزة البحث عبر الويب.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info(f"🔍 تم تهيئة البحث عبر الويب مع {len(self.api_manager.keys)} مفتاح Google Search")

    def search(self, query: str, num_results: int = 3) -> Optional[List[Dict]]:
        """
        إجراء بحث باستخدام Google Custom Search API.
        :param query: استعلام البحث (مثل اسم اللعبة).
        :param num_results: عدد النتائج المراد إرجاعها.
        :return: قائمة بالنتائج أو None في حالة الفشل.
        """
        if not self.enabled:
            logger.debug("🚫 تم تخطي البحث عبر الويب لأنه غير ممكّن.")
            return None

        params = {
            'key': self.api_manager.get_key(),
            'cx': self.search_engine_id,
            'q': query,
            'num': num_results
        }

        try:
            logger.info(f"🔍 إجراء بحث عبر الويب عن: '{query}'")
            response = requests.get(self.base_url, params=params, timeout=15)
            response.raise_for_status()  # إثارة خطأ لحالات HTTP 4xx/5xx

            search_results = response.json()
            
            if 'items' not in search_results:
                logger.warning(f"⚠️ لم يتم العثور على نتائج بحث لـ '{query}'")
                return []

            # تنسيق النتائج
            formatted_results = []
            for item in search_results.get('items', []):
                formatted_results.append({
                    'title': item.get('title'),
                    'link': item.get('link'),
                    'snippet': item.get('snippet')
                })
            
            logger.info(f"✅ تم العثور على {len(formatted_results)} نتيجة بحث لـ '{query}'")
            return formatted_results

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 403:
                logger.warning(f"⚠️ خطأ 403 (Forbidden) في Google Search API. جاري تبديل المفتاح...")
                try:
                    self.api_manager.rotate_key()
                    logger.info("🔄 إعادة محاولة البحث بالمفتاح الجديد...")
                    return self.search(query, num_results)
                except Exception as rotation_error:
                    logger.critical(f"🚨 فشل في تبديل مفتاح API بعد خطأ بحث: {rotation_error}")
                    return []
            elif e.response.status_code == 429:
                logger.warning(f"⚠️ تم تجاوز حد الطلبات لـ Google Search API. جاري تبديل المفتاح...")
                try:
                    self.api_manager.rotate_key()
                    logger.info("🔄 إعادة محاولة البحث بالمفتاح الجديد...")
                    return self.search(query, num_results)
                except Exception as rotation_error:
                    logger.warning(f"⚠️ تم استنفاد جميع مفاتيح Google Search API")
                    return []
            else:
                logger.error(f"❌ فشل في الاتصال بـ Google Search API | HTTP {e.response.status_code}: {e.response.reason}")
                return []
        except requests.exceptions.Timeout:
            logger.warning(f"⏰ انتهت مهلة البحث عن '{query}' - سيتم المحاولة لاحقاً")
            return []
        except requests.exceptions.ConnectionError:
            logger.warning(f"🌐 مشكلة في الاتصال أثناء البحث عن '{query}' - تحقق من الإنترنت")
            return []
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع أثناء البحث عن '{query}' | نوع الخطأ: {type(e).__name__} | التفاصيل: {str(e)}")
            return []

    def get_api_usage_stats(self) -> Dict:
        """الحصول على إحصائيات استخدام مفاتيح API"""
        if self.api_manager:
            return self.api_manager.get_usage_stats()
        return {}

    def reset_api_failures(self, key: str = None):
        """إعادة تعيين فشل مفاتيح API"""
        if self.api_manager:
            self.api_manager.reset_key_failures(key)
            logger.info("🔓 تم إعادة تعيين فشل مفاتيح Google Search API")

    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة"""
        if self.api_manager:
            return len(self.api_manager.keys) - len(self.api_manager.blacklisted_keys)
        return 0
