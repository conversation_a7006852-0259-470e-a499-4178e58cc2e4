# نظام الذاكرة المتقدم للوكيل الذكي
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

try:
    import pinecone
    from sentence_transformers import SentenceTransformer
    import faiss
    import numpy as np
    from sklearn.cluster import KMeans
    from sklearn.metrics.pairwise import cosine_similarity
    import networkx as nx
    ADVANCED_LIBS_AVAILABLE = True
except ImportError:
    ADVANCED_LIBS_AVAILABLE = False
    # تم إخفاء التحذير - الوكيل يعمل بشكل طبيعي بدون المكتبات المتقدمة

    # إنشاء بدائل وهمية
    class DummyGraph:
        def __init__(self):
            pass
        def add_node(self, *args, **kwargs):
            pass
        def add_edge(self, *args, **kwargs):
            pass
        def has_node(self, *args, **kwargs):
            return False
        def has_edge(self, *args, **kwargs):
            return False
        def number_of_nodes(self):
            return 0
        def number_of_edges(self):
            return 0

    class DummyNX:
        Graph = DummyGraph
        NetworkXNoPath = Exception
        def shortest_path_length(self, *args, **kwargs):
            raise self.NetworkXNoPath()

    nx = DummyNX()
    np = None
    cosine_similarity = lambda x, y: [[0.5]]

from .logger import logger
from config.settings import BotConfig

class MemoryType(Enum):
    """أنواع الذكريات"""
    ARTICLE = "article"           # ذكريات المقالات
    USER_INTERACTION = "user_interaction"  # تفاعلات المستخدمين
    SEARCH_QUERY = "search_query"  # استعلامات البحث
    PERFORMANCE = "performance"    # أداء المحتوى
    TREND = "trend"               # الاتجاهات
    PREFERENCE = "preference"     # التفضيلات

class MemoryImportance(Enum):
    """مستويات أهمية الذكريات"""
    CRITICAL = 5    # حرج - لا يُحذف أبداً
    HIGH = 4        # عالي - يُحذف بعد سنة
    MEDIUM = 3      # متوسط - يُحذف بعد 6 أشهر
    LOW = 2         # منخفض - يُحذف بعد 3 أشهر
    TEMPORARY = 1   # مؤقت - يُحذف بعد شهر

class MemoryCluster(Enum):
    """مجموعات الذاكرة"""
    GAMING_NEWS = "gaming_news"
    USER_PREFERENCES = "user_preferences"
    CONTENT_PERFORMANCE = "content_performance"
    SEARCH_PATTERNS = "search_patterns"
    TRENDING_TOPICS = "trending_topics"
    SEASONAL_CONTENT = "seasonal_content"

class SemanticRelation(Enum):
    """أنواع العلاقات الدلالية"""
    SIMILAR = "similar"
    RELATED = "related"
    OPPOSITE = "opposite"
    CAUSAL = "causal"
    TEMPORAL = "temporal"
    HIERARCHICAL = "hierarchical"

@dataclass
class Memory:
    """كلاس الذاكرة الأساسي"""
    id: str
    content: str
    memory_type: MemoryType
    importance: MemoryImportance
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    created_at: datetime = None
    last_accessed: datetime = None
    access_count: int = 0
    cluster_id: Optional[str] = None
    semantic_tags: Optional[List[str]] = None
    confidence_score: float = 1.0

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_accessed is None:
            self.last_accessed = self.created_at
        if self.semantic_tags is None:
            self.semantic_tags = []

@dataclass
class MemoryRelation:
    """علاقة بين الذكريات"""
    source_memory_id: str
    target_memory_id: str
    relation_type: SemanticRelation
    strength: float  # قوة العلاقة من 0 إلى 1
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class MemoryClusterInfo:
    """معلومات مجموعة الذاكرة"""
    cluster_id: str
    cluster_type: MemoryCluster
    centroid: List[float]
    memory_ids: List[str]
    created_at: datetime = None
    last_updated: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_updated is None:
            self.last_updated = self.created_at
    tags: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_accessed is None:
            self.last_accessed = datetime.now()
        if self.tags is None:
            self.tags = []

class AdvancedMemorySystem:
    """نظام الذاكرة المتقدم للوكيل"""

    def __init__(self):
        self.embedding_model = None
        self.pinecone_index = None
        self.local_memories = {}  # احتياطي محلي
        self.memory_relations = {}  # علاقات الذكريات
        self.memory_clusters = {}  # مجموعات الذاكرة
        self.semantic_graph = nx.Graph()  # الرسم البياني الدلالي
        self.faiss_index = None  # فهرس FAISS للبحث السريع
        self.cluster_centroids = {}  # مراكز المجموعات

        self.memory_stats = {
            'total_memories': 0,
            'memories_by_type': {},
            'memories_by_importance': {},
            'memories_by_cluster': {},
            'last_cleanup': datetime.now(),
            'retrieval_stats': {},
            'clustering_stats': {},
            'relation_stats': {}
        }

        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة نظام الذاكرة"""
        try:
            # تحميل نموذج التضمين
            logger.info("🧠 تحميل نموذج التضمين...")
            if ADVANCED_LIBS_AVAILABLE:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("✅ تم تحميل نموذج التضمين")

                # تهيئة فهرس FAISS
                self._initialize_faiss()
            else:
                logger.info("📝 استخدام نظام الذاكرة الأساسي (بدون المكتبات المتقدمة)")

            # تهيئة Pinecone إذا كان متوفراً
            if ADVANCED_LIBS_AVAILABLE and hasattr(BotConfig, 'PINECONE_API_KEY'):
                self._initialize_pinecone()
            else:
                logger.info("💾 استخدام الذاكرة المحلية")

        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة نظام الذاكرة: {e}")

    def _initialize_faiss(self):
        """تهيئة فهرس FAISS"""
        try:
            # إنشاء فهرس FAISS للبحث السريع
            dimension = 384  # بُعد نموذج all-MiniLM-L6-v2
            self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner Product للتشابه
            logger.info("✅ تم تهيئة فهرس FAISS")
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة FAISS: {e}")
            self.faiss_index = None
    
    def _initialize_pinecone(self):
        """تهيئة Pinecone"""
        try:
            pinecone.init(
                api_key=BotConfig.PINECONE_API_KEY,
                environment=getattr(BotConfig, 'PINECONE_ENVIRONMENT', 'us-west1-gcp')
            )

            index_name = "gaming-agent-memory"

            # إنشاء الفهرس إذا لم يكن موجوداً
            if index_name not in pinecone.list_indexes():
                pinecone.create_index(
                    name=index_name,
                    dimension=384,  # بُعد نموذج all-MiniLM-L6-v2
                    metric="cosine"
                )
                logger.info(f"✅ تم إنشاء فهرس Pinecone: {index_name}")

            self.pinecone_index = pinecone.Index(index_name)
            logger.info("✅ تم الاتصال بـ Pinecone بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Pinecone: {e}")
            self.pinecone_index = None

    async def perform_semantic_clustering(self, force_recluster: bool = False):
        """تنفيذ التجميع الدلالي للذكريات"""
        if not ADVANCED_LIBS_AVAILABLE or not self.embedding_model:
            return

        try:
            logger.info("🔄 بدء التجميع الدلالي للذكريات...")

            # جمع جميع التضمينات
            embeddings = []
            memory_ids = []

            for memory_id, memory in self.local_memories.items():
                if memory.embedding:
                    embeddings.append(memory.embedding)
                    memory_ids.append(memory_id)

            if len(embeddings) < 5:  # نحتاج عدد كافي للتجميع
                logger.info("📊 عدد الذكريات قليل للتجميع")
                return

            embeddings_array = np.array(embeddings)

            # تحديد عدد المجموعات المثلى
            n_clusters = min(10, max(3, len(embeddings) // 10))

            # تنفيذ K-Means
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings_array)

            # تحديث معلومات المجموعات
            for i, memory_id in enumerate(memory_ids):
                cluster_id = f"cluster_{cluster_labels[i]}"
                self.local_memories[memory_id].cluster_id = cluster_id

                # إنشاء معلومات المجموعة إذا لم تكن موجودة
                if cluster_id not in self.memory_clusters:
                    self.memory_clusters[cluster_id] = MemoryClusterInfo(
                        cluster_id=cluster_id,
                        cluster_type=self._determine_cluster_type(cluster_id),
                        centroid=kmeans.cluster_centers_[cluster_labels[i]].tolist(),
                        memory_ids=[]
                    )

                if memory_id not in self.memory_clusters[cluster_id].memory_ids:
                    self.memory_clusters[cluster_id].memory_ids.append(memory_id)

            # تحديث الإحصائيات
            self.memory_stats['clustering_stats'] = {
                'total_clusters': len(self.memory_clusters),
                'last_clustering': datetime.now(),
                'memories_clustered': len(memory_ids)
            }

            logger.info(f"✅ تم تجميع {len(memory_ids)} ذاكرة في {n_clusters} مجموعة")

        except Exception as e:
            logger.error(f"❌ خطأ في التجميع الدلالي: {e}")

    def _determine_cluster_type(self, cluster_id: str) -> MemoryCluster:
        """تحديد نوع المجموعة بناءً على محتواها"""
        try:
            if cluster_id not in self.memory_clusters:
                return MemoryCluster.GAMING_NEWS

            cluster_info = self.memory_clusters[cluster_id]
            memory_types = []

            for memory_id in cluster_info.memory_ids:
                if memory_id in self.local_memories:
                    memory_types.append(self.local_memories[memory_id].memory_type)

            # تحديد النوع الأكثر شيوعاً
            if not memory_types:
                return MemoryCluster.GAMING_NEWS

            type_counts = {}
            for mem_type in memory_types:
                type_counts[mem_type] = type_counts.get(mem_type, 0) + 1

            most_common_type = max(type_counts, key=type_counts.get)

            # ربط أنواع الذاكرة بمجموعات
            type_mapping = {
                MemoryType.ARTICLE: MemoryCluster.GAMING_NEWS,
                MemoryType.USER_INTERACTION: MemoryCluster.USER_PREFERENCES,
                MemoryType.PERFORMANCE: MemoryCluster.CONTENT_PERFORMANCE,
                MemoryType.SEARCH_QUERY: MemoryCluster.SEARCH_PATTERNS,
                MemoryType.TREND: MemoryCluster.TRENDING_TOPICS,
                MemoryType.PREFERENCE: MemoryCluster.USER_PREFERENCES
            }

            return type_mapping.get(most_common_type, MemoryCluster.GAMING_NEWS)

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع المجموعة: {e}")
            return MemoryCluster.GAMING_NEWS

    async def build_semantic_relations(self):
        """بناء العلاقات الدلالية بين الذكريات"""
        if not ADVANCED_LIBS_AVAILABLE or not self.embedding_model:
            return

        try:
            logger.info("🔗 بناء العلاقات الدلالية...")

            memory_items = list(self.local_memories.items())
            relations_created = 0

            for i, (memory_id1, memory1) in enumerate(memory_items):
                if not memory1.embedding:
                    continue

                for j, (memory_id2, memory2) in enumerate(memory_items[i+1:], i+1):
                    if not memory2.embedding:
                        continue

                    # حساب التشابه
                    similarity = cosine_similarity(
                        [memory1.embedding],
                        [memory2.embedding]
                    )[0][0]

                    # إنشاء علاقة إذا كان التشابه عالي
                    if similarity > 0.7:
                        relation_type = self._determine_relation_type(
                            memory1, memory2, similarity)

                        relation = MemoryRelation(
                            source_memory_id=memory_id1,
                            target_memory_id=memory_id2,
                            relation_type=relation_type,
                            strength=similarity
                        )

                        relation_key = f"{memory_id1}_{memory_id2}"
                        self.memory_relations[relation_key] = relation

                        # إضافة للرسم البياني الدلالي
                        self.semantic_graph.add_edge(
                            memory_id1, memory_id2,
                            weight=similarity,
                            relation_type=relation_type.value
                        )

                        relations_created += 1

            # تحديث الإحصائيات
            self.memory_stats['relation_stats'] = {
                'total_relations': len(self.memory_relations),
                'last_relation_build': datetime.now(),
                'relations_created_this_session': relations_created
            }

            logger.info(f"✅ تم إنشاء {relations_created} علاقة دلالية")

        except Exception as e:
            logger.error(f"❌ خطأ في بناء العلاقات الدلالية: {e}")

    def _determine_relation_type(self, memory1: Memory, memory2: Memory, similarity: float) -> SemanticRelation:
        """تحديد نوع العلاقة بين ذاكرتين"""
        try:
            # علاقة زمنية
            time_diff = abs((memory1.created_at - memory2.created_at).days)
            if time_diff <= 1:
                return SemanticRelation.TEMPORAL

            # علاقة هرمية (نفس النوع ولكن أهمية مختلفة)
            if (memory1.memory_type == memory2.memory_type and
                memory1.importance != memory2.importance):
                return SemanticRelation.HIERARCHICAL

            # علاقة سببية (أداء يتبع مقال)
            if (memory1.memory_type == MemoryType.ARTICLE and
                memory2.memory_type == MemoryType.PERFORMANCE):
                return SemanticRelation.CAUSAL

            # علاقة تشابه عالي
            if similarity > 0.9:
                return SemanticRelation.SIMILAR

            # علاقة ترابط عادية
            return SemanticRelation.RELATED

        except Exception as e:
            logger.error(f"❌ خطأ في تحديد نوع العلاقة: {e}")
            return SemanticRelation.RELATED

    async def smart_memory_retrieval(self, query: str, max_results: int = 10,
                                   use_relations: bool = True) -> List[Memory]:
        """استرجاع ذكي للذكريات مع استخدام العلاقات"""
        try:
            if not self.embedding_model:
                return []

            # إنشاء تضمين للاستعلام
            query_embedding = self._create_embedding(query)
            if not query_embedding:
                return []

            # البحث الأساسي
            basic_results = await self._basic_similarity_search(query_embedding, max_results * 2)

            if not use_relations or not basic_results:
                return basic_results[:max_results]

            # تحسين النتائج باستخدام العلاقات
            enhanced_results = await self._enhance_with_relations(basic_results, query)

            # ترتيب النتائج النهائية
            final_results = sorted(enhanced_results,
                                 key=lambda x: x.confidence_score,
                                 reverse=True)

            return final_results[:max_results]

        except Exception as e:
            logger.error(f"❌ خطأ في الاسترجاع الذكي: {e}")
            return []

    async def _basic_similarity_search(self, query_embedding: List[float], max_results: int) -> List[Memory]:
        """البحث الأساسي بالتشابه"""
        try:
            results = []

            for memory_id, memory in self.local_memories.items():
                if not memory.embedding:
                    continue

                # حساب التشابه
                similarity = cosine_similarity(
                    [query_embedding],
                    [memory.embedding]
                )[0][0]

                if similarity > 0.3:  # حد أدنى للتشابه
                    memory.confidence_score = similarity
                    results.append(memory)

            # ترتيب حسب التشابه
            results.sort(key=lambda x: x.confidence_score, reverse=True)
            return results[:max_results]

        except Exception as e:
            logger.error(f"❌ خطأ في البحث الأساسي: {e}")
            return []

    async def _enhance_with_relations(self, basic_results: List[Memory], query: str) -> List[Memory]:
        """تحسين النتائج باستخدام العلاقات الدلالية"""
        try:
            enhanced_results = basic_results.copy()

            for memory in basic_results:
                # البحث عن الذكريات المرتبطة
                related_memories = self._find_related_memories(memory.id)

                for related_memory in related_memories:
                    if related_memory not in enhanced_results:
                        # تقليل نقاط الثقة للذكريات المرتبطة
                        related_memory.confidence_score *= 0.8
                        enhanced_results.append(related_memory)

            return enhanced_results

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين النتائج: {e}")
            return basic_results

    def _find_related_memories(self, memory_id: str) -> List[Memory]:
        """العثور على الذكريات المرتبطة"""
        try:
            related_memories = []

            # البحث في العلاقات المباشرة
            for relation_key, relation in self.memory_relations.items():
                if relation.source_memory_id == memory_id:
                    target_id = relation.target_memory_id
                    if target_id in self.local_memories:
                        related_memory = self.local_memories[target_id]
                        related_memory.confidence_score = relation.strength
                        related_memories.append(related_memory)
                elif relation.target_memory_id == memory_id:
                    source_id = relation.source_memory_id
                    if source_id in self.local_memories:
                        related_memory = self.local_memories[source_id]
                        related_memory.confidence_score = relation.strength
                        related_memories.append(related_memory)

            # البحث في نفس المجموعة
            if memory_id in self.local_memories:
                memory = self.local_memories[memory_id]
                if memory.cluster_id:
                    cluster_info = self.memory_clusters.get(memory.cluster_id)
                    if cluster_info:
                        for cluster_memory_id in cluster_info.memory_ids:
                            if (cluster_memory_id != memory_id and
                                cluster_memory_id in self.local_memories):
                                cluster_memory = self.local_memories[cluster_memory_id]
                                cluster_memory.confidence_score = 0.6  # نقاط متوسطة للمجموعة
                                related_memories.append(cluster_memory)

            return related_memories

        except Exception as e:
            logger.error(f"❌ خطأ في العثور على الذكريات المرتبطة: {e}")
            return []

    async def get_memory_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى متقدمة حول الذاكرة"""
        try:
            insights = {
                'total_memories': len(self.local_memories),
                'total_clusters': len(self.memory_clusters),
                'total_relations': len(self.memory_relations),
                'memory_distribution': {},
                'cluster_analysis': {},
                'relation_analysis': {},
                'trending_topics': [],
                'memory_health': {}
            }

            # توزيع الذكريات حسب النوع
            for memory in self.local_memories.values():
                mem_type = memory.memory_type.value
                insights['memory_distribution'][mem_type] = \
                    insights['memory_distribution'].get(mem_type, 0) + 1

            # تحليل المجموعات
            for cluster_id, cluster_info in self.memory_clusters.items():
                insights['cluster_analysis'][cluster_id] = {
                    'type': cluster_info.cluster_type.value,
                    'size': len(cluster_info.memory_ids),
                    'created_at': cluster_info.created_at.isoformat(),
                    'last_updated': cluster_info.last_updated.isoformat()
                }

            # تحليل العلاقات
            relation_types = {}
            for relation in self.memory_relations.values():
                rel_type = relation.relation_type.value
                relation_types[rel_type] = relation_types.get(rel_type, 0) + 1
            insights['relation_analysis'] = relation_types

            # المواضيع الرائجة (من الذكريات الحديثة)
            recent_memories = [m for m in self.local_memories.values()
                             if (datetime.now() - m.created_at).days <= 7]

            topic_counts = {}
            for memory in recent_memories:
                for tag in memory.semantic_tags:
                    topic_counts[tag] = topic_counts.get(tag, 0) + 1

            insights['trending_topics'] = sorted(topic_counts.items(),
                                               key=lambda x: x[1], reverse=True)[:10]

            # صحة الذاكرة
            total_memories = len(self.local_memories)
            memories_with_embeddings = sum(1 for m in self.local_memories.values()
                                         if m.embedding)
            memories_with_clusters = sum(1 for m in self.local_memories.values()
                                       if m.cluster_id)

            insights['memory_health'] = {
                'embedding_coverage': (memories_with_embeddings / max(1, total_memories)) * 100,
                'clustering_coverage': (memories_with_clusters / max(1, total_memories)) * 100,
                'relation_density': len(self.memory_relations) / max(1, total_memories),
                'last_cleanup': self.memory_stats['last_cleanup'].isoformat()
            }

            return insights

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رؤى الذاكرة: {e}")
            return {}

    async def optimize_memory_performance(self):
        """تحسين أداء الذاكرة"""
        try:
            logger.info("⚡ بدء تحسين أداء الذاكرة...")

            # إعادة بناء فهرس FAISS
            if self.faiss_index and ADVANCED_LIBS_AVAILABLE:
                await self._rebuild_faiss_index()

            # إعادة تجميع الذكريات
            await self.perform_semantic_clustering(force_recluster=True)

            # إعادة بناء العلاقات
            await self.build_semantic_relations()

            # تنظيف الذكريات القديمة
            await self.cleanup_old_memories()

            logger.info("✅ تم تحسين أداء الذاكرة بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الأداء: {e}")

    async def _rebuild_faiss_index(self):
        """إعادة بناء فهرس FAISS"""
        try:
            if not self.faiss_index:
                return

            # جمع جميع التضمينات
            embeddings = []
            for memory in self.local_memories.values():
                if memory.embedding:
                    embeddings.append(memory.embedding)

            if embeddings:
                embeddings_array = np.array(embeddings, dtype=np.float32)

                # إعادة إنشاء الفهرس
                dimension = embeddings_array.shape[1]
                self.faiss_index = faiss.IndexFlatIP(dimension)
                self.faiss_index.add(embeddings_array)

                logger.info(f"✅ تم إعادة بناء فهرس FAISS مع {len(embeddings)} تضمين")

        except Exception as e:
            logger.error(f"❌ خطأ في إعادة بناء فهرس FAISS: {e}")
    
    def _generate_memory_id(self, content: str, memory_type: MemoryType) -> str:
        """توليد معرف فريد للذاكرة"""
        timestamp = str(int(time.time()))
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        return f"{memory_type.value}_{timestamp}_{content_hash}"
    
    def _create_embedding(self, text: str) -> List[float]:
        """إنشاء تضمين للنص"""
        try:
            if self.embedding_model:
                embedding = self.embedding_model.encode(text)
                return embedding.tolist()
            return []
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء التضمين: {e}")
            return []
    
    async def store_memory(self, 
                          content: str, 
                          memory_type: MemoryType,
                          importance: MemoryImportance,
                          metadata: Dict[str, Any] = None,
                          tags: List[str] = None) -> str:
        """حفظ ذاكرة جديدة"""
        try:
            # إنشاء معرف الذاكرة
            memory_id = self._generate_memory_id(content, memory_type)
            
            # إنشاء التضمين
            embedding = self._create_embedding(content)
            
            # إنشاء كائن الذاكرة
            memory = Memory(
                id=memory_id,
                content=content,
                memory_type=memory_type,
                importance=importance,
                metadata=metadata or {},
                embedding=embedding,
                tags=tags or []
            )
            
            # حفظ في Pinecone إذا كان متوفراً
            if self.pinecone_index and embedding:
                vector_data = {
                    'id': memory_id,
                    'values': embedding,
                    'metadata': {
                        'content': content,
                        'type': memory_type.value,
                        'importance': importance.value,
                        'created_at': memory.created_at.isoformat(),
                        'tags': tags or [],
                        **(metadata or {})
                    }
                }
                self.pinecone_index.upsert([vector_data])
            
            # حفظ محلياً كاحتياط
            self.local_memories[memory_id] = memory
            
            # تحديث الإحصائيات
            self._update_stats(memory_type, importance)
            
            logger.info(f"💾 تم حفظ ذاكرة: {memory_type.value} - {memory_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الذاكرة: {e}")
            return None
    
    async def retrieve_memories(self, 
                               query: str, 
                               memory_types: List[MemoryType] = None,
                               limit: int = 10,
                               min_importance: MemoryImportance = MemoryImportance.LOW) -> List[Memory]:
        """استرجاع الذكريات ذات الصلة"""
        try:
            # إنشاء تضمين للاستعلام
            query_embedding = self._create_embedding(query)
            
            memories = []
            
            # البحث في Pinecone إذا كان متوفراً
            if self.pinecone_index and query_embedding:
                # إنشاء فلتر للبحث
                filter_dict = {}
                if memory_types:
                    filter_dict['type'] = {'$in': [t.value for t in memory_types]}
                if min_importance:
                    filter_dict['importance'] = {'$gte': min_importance.value}
                
                # البحث
                results = self.pinecone_index.query(
                    vector=query_embedding,
                    top_k=limit,
                    filter=filter_dict if filter_dict else None,
                    include_metadata=True
                )
                
                # تحويل النتائج إلى كائنات Memory
                for match in results['matches']:
                    metadata = match['metadata']
                    memory = Memory(
                        id=match['id'],
                        content=metadata.get('content', ''),
                        memory_type=MemoryType(metadata.get('type', 'article')),
                        importance=MemoryImportance(metadata.get('importance', 2)),
                        metadata=metadata,
                        created_at=datetime.fromisoformat(metadata.get('created_at', datetime.now().isoformat())),
                        tags=metadata.get('tags', [])
                    )
                    memories.append(memory)
            
            # البحث المحلي كاحتياط
            if not memories:
                memories = self._search_local_memories(query, memory_types, limit, min_importance)
            
            # تحديث إحصائيات الوصول
            for memory in memories:
                self._update_access_stats(memory.id)
            
            logger.info(f"🔍 تم استرجاع {len(memories)} ذاكرة للاستعلام: {query}")
            return memories
            
        except Exception as e:
            logger.error(f"❌ خطأ في استرجاع الذكريات: {e}")
            return []
    
    def _search_local_memories(self, 
                              query: str, 
                              memory_types: List[MemoryType],
                              limit: int,
                              min_importance: MemoryImportance) -> List[Memory]:
        """البحث في الذكريات المحلية"""
        matching_memories = []
        query_lower = query.lower()
        
        for memory in self.local_memories.values():
            # فلترة حسب النوع
            if memory_types and memory.memory_type not in memory_types:
                continue
            
            # فلترة حسب الأهمية
            if memory.importance.value < min_importance.value:
                continue
            
            # البحث في المحتوى
            if query_lower in memory.content.lower():
                matching_memories.append(memory)
        
        # ترتيب حسب الأهمية والوقت
        matching_memories.sort(
            key=lambda m: (m.importance.value, m.created_at),
            reverse=True
        )
        
        return matching_memories[:limit]
    
    def _update_stats(self, memory_type: MemoryType, importance: MemoryImportance):
        """تحديث إحصائيات الذاكرة"""
        self.memory_stats['total_memories'] += 1
        
        # إحصائيات حسب النوع
        type_key = memory_type.value
        if type_key not in self.memory_stats['memories_by_type']:
            self.memory_stats['memories_by_type'][type_key] = 0
        self.memory_stats['memories_by_type'][type_key] += 1
        
        # إحصائيات حسب الأهمية
        importance_key = importance.name
        if importance_key not in self.memory_stats['memories_by_importance']:
            self.memory_stats['memories_by_importance'][importance_key] = 0
        self.memory_stats['memories_by_importance'][importance_key] += 1
    
    def _update_access_stats(self, memory_id: str):
        """تحديث إحصائيات الوصول"""
        if memory_id in self.local_memories:
            memory = self.local_memories[memory_id]
            memory.last_accessed = datetime.now()
            memory.access_count += 1
        
        # تحديث إحصائيات الاسترجاع
        if memory_id not in self.memory_stats['retrieval_stats']:
            self.memory_stats['retrieval_stats'][memory_id] = 0
        self.memory_stats['retrieval_stats'][memory_id] += 1
    
    async def cleanup_old_memories(self):
        """تنظيف الذكريات القديمة"""
        try:
            logger.info("🧹 بدء تنظيف الذكريات القديمة...")
            
            current_time = datetime.now()
            memories_to_delete = []
            
            # تحديد الذكريات المراد حذفها
            for memory_id, memory in self.local_memories.items():
                age = current_time - memory.created_at
                
                # تحديد مدة الاحتفاظ حسب الأهمية
                retention_days = {
                    MemoryImportance.CRITICAL: float('inf'),  # لا يُحذف أبداً
                    MemoryImportance.HIGH: 365,               # سنة
                    MemoryImportance.MEDIUM: 180,             # 6 أشهر
                    MemoryImportance.LOW: 90,                 # 3 أشهر
                    MemoryImportance.TEMPORARY: 30            # شهر
                }
                
                max_age = retention_days.get(memory.importance, 90)
                if age.days > max_age:
                    memories_to_delete.append(memory_id)
            
            # حذف الذكريات القديمة
            for memory_id in memories_to_delete:
                await self._delete_memory(memory_id)
            
            self.memory_stats['last_cleanup'] = current_time
            logger.info(f"✅ تم حذف {len(memories_to_delete)} ذاكرة قديمة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الذكريات: {e}")
    
    async def _delete_memory(self, memory_id: str):
        """حذف ذاكرة محددة"""
        try:
            # حذف من Pinecone
            if self.pinecone_index:
                self.pinecone_index.delete(ids=[memory_id])
            
            # حذف من الذاكرة المحلية
            if memory_id in self.local_memories:
                del self.local_memories[memory_id]
            
            logger.debug(f"🗑️ تم حذف الذاكرة: {memory_id}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الذاكرة {memory_id}: {e}")
    
    def get_memory_stats(self) -> Dict:
        """الحصول على إحصائيات الذاكرة"""
        return {
            **self.memory_stats,
            'local_memories_count': len(self.local_memories),
            'pinecone_available': self.pinecone_index is not None,
            'embedding_model_loaded': self.embedding_model is not None
        }

# إنشاء مثيل عام
memory_system = AdvancedMemorySystem()
