#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحسين التلقائي للمحتوى الضعيف
يقوم بإعادة صياغة العناوين والنصوص وتحسين الكلمات المفتاحية للمقالات ضعيفة الأداء
"""

import asyncio
import sqlite3
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import google.generativeai as genai

from .logger import logger
from .database import db
from .article_performance_analyzer import article_performance_analyzer
from config.settings import google_api_manager, SEOConfig

class ContentOptimizer:
    """محسن المحتوى التلقائي"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_optimizer_tables()
        self.setup_gemini()
        
        # معايير تحديد المحتوى الضعيف
        self.poor_performance_thresholds = {
            'engagement_score': 50.0,  # أقل من 50 نقطة تفاعل
            'seo_score': 60.0,         # أقل من 60 نقطة SEO
            'ctr': 2.0,                # أقل من 2% CTR
            'avg_read_time': 60.0,     # أقل من دقيقة قراءة
            'bounce_rate': 75.0        # أكثر من 75% معدل ارتداد
        }
        
        # قوالب تحسين العناوين
        self.title_improvement_templates = [
            "🔥 {original_title} - دليل شامل 2025",
            "⚡ كل ما تحتاج معرفته عن {main_topic}",
            "🎮 {original_title}: نصائح وحيل لا تفوتها",
            "🚀 {original_title} - التحديث الكامل",
            "💎 اكتشف أسرار {main_topic} في 2025",
            "🏆 أفضل دليل لـ {main_topic} على الإطلاق",
            "📱 {original_title}: كل التفاصيل الجديدة",
            "🎯 {original_title} - مراجعة شاملة ومفصلة"
        ]
    
    def setup_gemini(self):
        """إعداد Gemini API للتحسين"""
        try:
            if google_api_manager:
                current_key = google_api_manager.get_key()
                genai.configure(api_key=current_key)
                
                generation_config = {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }
                
                self.model = genai.GenerativeModel(
                    model_name="gemini-2.5-pro",  # استخدام Gemini 2.5 Pro فقط
                    generation_config=generation_config
                )
                
                logger.info("✅ تم إعداد Gemini للتحسين التلقائي")
            else:
                logger.warning("⚠️ لم يتم العثور على مدير مفاتيح Google API")
                self.model = None
                
        except Exception as e:
            logger.error("❌ فشل في إعداد Gemini للتحسين", e)
            self.model = None
    
    def _init_optimizer_tables(self):
        """إنشاء جداول نظام التحسين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول تاريخ التحسينات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS content_optimizations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        optimization_type TEXT NOT NULL,
                        original_value TEXT,
                        optimized_value TEXT,
                        performance_before REAL,
                        performance_after REAL,
                        optimization_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'applied',
                        improvement_score REAL DEFAULT 0.0,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول قائمة انتظار التحسين
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS optimization_queue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        priority_level TEXT DEFAULT 'medium',
                        issues_identified TEXT,
                        suggested_improvements TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        processed_at TIMESTAMP,
                        status TEXT DEFAULT 'pending',
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول نظام التحسين بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول نظام التحسين", e)
    
    async def identify_poor_performing_articles(self) -> List[Dict]:
        """تحديد المقالات ضعيفة الأداء"""
        try:
            logger.info("🔍 بدء تحديد المقالات ضعيفة الأداء...")
            
            # تشغيل تحليل الأداء أولاً
            performance_analysis = await article_performance_analyzer.analyze_all_articles_performance()
            
            if not performance_analysis:
                logger.warning("⚠️ لم يتم الحصول على تحليل الأداء")
                return []
            
            poor_articles = []
            individual_results = performance_analysis.get('individual_results', [])
            
            for result in individual_results:
                # فحص معايير الأداء الضعيف
                is_poor_performing = (
                    result.engagement_score < self.poor_performance_thresholds['engagement_score'] or
                    result.seo_score < self.poor_performance_thresholds['seo_score'] or
                    result.ctr < self.poor_performance_thresholds['ctr'] or
                    result.avg_read_time < self.poor_performance_thresholds['avg_read_time'] or
                    result.bounce_rate > self.poor_performance_thresholds['bounce_rate']
                )
                
                if is_poor_performing:
                    # تحديد المشاكل المحددة
                    issues = []
                    if result.engagement_score < self.poor_performance_thresholds['engagement_score']:
                        issues.append(f"تفاعل منخفض ({result.engagement_score:.1f})")
                    if result.seo_score < self.poor_performance_thresholds['seo_score']:
                        issues.append(f"SEO ضعيف ({result.seo_score:.1f})")
                    if result.ctr < self.poor_performance_thresholds['ctr']:
                        issues.append(f"CTR منخفض ({result.ctr:.2f}%)")
                    if result.avg_read_time < self.poor_performance_thresholds['avg_read_time']:
                        issues.append(f"وقت قراءة قصير ({result.avg_read_time:.1f}s)")
                    if result.bounce_rate > self.poor_performance_thresholds['bounce_rate']:
                        issues.append(f"معدل ارتداد مرتفع ({result.bounce_rate:.1f}%)")
                    
                    # تحديد مستوى الأولوية
                    priority = 'low'
                    if result.engagement_score < 30 or result.seo_score < 40:
                        priority = 'high'
                    elif result.engagement_score < 40 or result.seo_score < 50:
                        priority = 'medium'
                    
                    poor_articles.append({
                        'article_id': result.article_id,
                        'title': result.title,
                        'performance_metrics': result,
                        'issues': issues,
                        'priority': priority,
                        'overall_score': (result.engagement_score + result.seo_score) / 2
                    })
            
            # ترتيب حسب الأولوية والنقاط
            poor_articles.sort(key=lambda x: (
                {'high': 3, 'medium': 2, 'low': 1}[x['priority']], 
                -x['overall_score']
            ), reverse=True)
            
            logger.info(f"🎯 تم تحديد {len(poor_articles)} مقال يحتاج تحسين")
            
            return poor_articles
            
        except Exception as e:
            logger.error("❌ فشل في تحديد المقالات ضعيفة الأداء", e)
            return []
    
    
    async def safe_optimize_article_automatically(self, article_data: Dict) -> Dict:
        """تحسين مقال تلقائياً مع حماية من الأخطاء"""
        try:
            article_id = article_data.get('article_id')
            if not article_id:
                logger.error("❌ معرف المقال مفقود")
                return {}
            
            logger.info(f"🔧 بدء التحسين الآمن للمقال: {article_data.get('title', 'غير محدد')[:50]}...")
            
            # الحصول على بيانات المقال بطريقة آمنة
            full_article = self._safe_get_article_data(article_id)
            if not full_article:
                logger.error(f"❌ لم يتم العثور على بيانات المقال {article_id}")
                return {}
            
            optimization_results = {}
            
            # التحقق من وجود performance_metrics
            performance_metrics = article_data.get('performance_metrics')
            if not performance_metrics:
                logger.warning("⚠️ مقاييس الأداء غير متوفرة، سيتم تخطي بعض التحسينات")
                performance_metrics = type('obj', (object,), {
                    'ctr': 0.0,
                    'avg_read_time': 0.0,
                    'seo_score': 0.0
                })()
            
            # 1. تحسين العنوان (إذا كان CTR منخفض)
            try:
                ctr = getattr(performance_metrics, 'ctr', 0.0) or 0.0
                if ctr < self.poor_performance_thresholds.get('ctr', 2.0):
                    optimized_title = await self._optimize_title(full_article)
                    if optimized_title:
                        optimization_results['title'] = {
                            'original': full_article.get('title', ''),
                            'optimized': optimized_title,
                            'improvement_type': 'title_optimization'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين العنوان: {e}")
            
            # 2. تحسين المحتوى (إذا كان وقت القراءة منخفض)
            try:
                avg_read_time = getattr(performance_metrics, 'avg_read_time', 0.0) or 0.0
                if avg_read_time < self.poor_performance_thresholds.get('avg_read_time', 60.0):
                    optimized_content = self._safe_optimize_content(full_article)
                    if optimized_content:
                        optimization_results['content'] = {
                            'original': full_article.get('content', '')[:200] + "...",
                            'optimized': optimized_content[:200] + "...",
                            'improvement_type': 'content_enhancement'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين المحتوى: {e}")
            
            # 3. تحسين الكلمات المفتاحية (إذا كان SEO منخفض)
            try:
                seo_score = getattr(performance_metrics, 'seo_score', 0.0) or 0.0
                if seo_score < self.poor_performance_thresholds.get('seo_score', 60.0):
                    optimized_keywords = self._safe_optimize_keywords(full_article)
                    if optimized_keywords:
                        optimization_results['keywords'] = {
                            'original': full_article.get('keywords', []),
                            'optimized': optimized_keywords,
                            'improvement_type': 'keyword_optimization'
                        }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين الكلمات المفتاحية: {e}")
            
            # 4. تحسين التصنيف والوسوم
            try:
                optimized_category = await self._optimize_category_and_tags(full_article)
                if optimized_category:
                    optimization_results['category'] = {
                        'original': full_article.get('category', ''),
                        'optimized': optimized_category,
                        'improvement_type': 'category_optimization'
                    }
            except Exception as e:
                logger.warning(f"⚠️ فشل في تحسين التصنيف: {e}")
            
            # حفظ نتائج التحسين
            if optimization_results:
                try:
                    self._save_optimization_results(article_id, optimization_results, performance_metrics)
                    logger.info(f"✅ تم تحسين {len(optimization_results)} عنصر في المقال")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في حفظ نتائج التحسين: {e}")
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"❌ فشل في التحسين الآمن للمقال {article_data.get('article_id', 'غير محدد')}: {e}")
            return {}

    async def optimize_article_automatically(self, article_data: Dict) -> Dict:
        """تحسين مقال تلقائياً"""
        try:
            article_id = article_data['article_id']
            logger.info(f"🔧 بدء تحسين المقال: {article_data['title'][:50]}...")
            
            # الحصول على بيانات المقال الكاملة
            full_article = self._get_full_article_data(article_id)
            if not full_article:
                logger.error(f"❌ لم يتم العثور على بيانات المقال {article_id}")
                return {}
            
            optimization_results = {}
            
            # 1. تحسين العنوان
            if article_data['performance_metrics'].ctr < self.poor_performance_thresholds['ctr']:
                optimized_title = await self._optimize_title(full_article)
                if optimized_title:
                    optimization_results['title'] = {
                        'original': full_article['title'],
                        'optimized': optimized_title,
                        'improvement_type': 'title_optimization'
                    }
            
            # 2. تحسين المحتوى
            if article_data['performance_metrics'].avg_read_time < self.poor_performance_thresholds['avg_read_time']:
                optimized_content = await self._optimize_content(full_article)
                if optimized_content:
                    optimization_results['content'] = {
                        'original': full_article['content'][:200] + "...",
                        'optimized': optimized_content[:200] + "...",
                        'improvement_type': 'content_enhancement'
                    }
            
            # 3. تحسين الكلمات المفتاحية
            if article_data['performance_metrics'].seo_score < self.poor_performance_thresholds['seo_score']:
                optimized_keywords = await self._optimize_keywords(full_article)
                if optimized_keywords:
                    optimization_results['keywords'] = {
                        'original': full_article.get('keywords', ''),
                        'optimized': optimized_keywords,
                        'improvement_type': 'keyword_optimization'
                    }
            
            # 4. تحسين التصنيف والوسوم
            optimized_category = await self._optimize_category_and_tags(full_article)
            if optimized_category:
                optimization_results['category'] = {
                    'original': full_article.get('category', ''),
                    'optimized': optimized_category,
                    'improvement_type': 'category_optimization'
                }
            
            # حفظ نتائج التحسين
            if optimization_results:
                self._save_optimization_results(article_id, optimization_results, article_data['performance_metrics'])
                logger.info(f"✅ تم تحسين {len(optimization_results)} عنصر في المقال")
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"❌ فشل في تحسين المقال {article_data.get('article_id', 'غير محدد')}", e)
            return {}
    
    async def _optimize_title(self, article: Dict) -> Optional[str]:
        """تحسين عنوان المقال"""
        try:
            if not self.model:
                # استخدام قوالب جاهزة كبديل
                return self._optimize_title_with_templates(article)
            
            original_title = article['title']
            content_preview = article.get('content', '')[:500]
            
            prompt = f"""
            أنت خبير في تحسين عناوين المقالات لزيادة معدل النقر (CTR).
            
            العنوان الحالي: "{original_title}"
            معاينة المحتوى: "{content_preview}"
            
            اكتب عنوان محسن يحقق:
            1. جذب انتباه القارئ
            2. تحسين SEO
            3. وضوح الموضوع
            4. استخدام كلمات قوية ومثيرة
            5. طول مناسب (30-60 حرف)
            
            اكتب العنوان المحسن فقط بدون شرح:
            """
            
            response = self.model.generate_content(prompt)
            optimized_title = response.text.strip()
            
            # تنظيف العنوان
            optimized_title = re.sub(r'^["\'`]|["\'`]$', '', optimized_title)
            
            if len(optimized_title) > 10 and optimized_title != original_title:
                return optimized_title
            else:
                return self._optimize_title_with_templates(article)
                
        except Exception as e:
            logger.error("❌ فشل في تحسين العنوان باستخدام AI", e)
            return self._optimize_title_with_templates(article)
    
    def _optimize_title_with_templates(self, article: Dict) -> Optional[str]:
        """تحسين العنوان باستخدام القوالب الجاهزة"""
        try:
            original_title = article['title']
            
            # استخراج الموضوع الرئيسي
            main_topic = self._extract_main_topic(original_title)
            
            # اختيار قالب عشوائي
            import random
            template = random.choice(self.title_improvement_templates)
            
            # تطبيق القالب
            optimized_title = template.format(
                original_title=original_title,
                main_topic=main_topic
            )
            
            return optimized_title
            
        except Exception as e:
            logger.error("❌ فشل في تحسين العنوان بالقوالب", e)
            return None
    
    def _extract_main_topic(self, title: str) -> str:
        """استخراج الموضوع الرئيسي من العنوان"""
        try:
            # إزالة الرموز والكلمات الشائعة
            clean_title = re.sub(r'[🔥⚡🎮🚀💎🏆📱🎯]', '', title)
            clean_title = re.sub(r'\b(في|من|إلى|على|عن|مع|دليل|مراجعة|تحديث)\b', '', clean_title)
            
            # أخذ أول كلمتين مهمتين
            words = clean_title.split()[:2]
            return ' '.join(words) if words else title
            
        except Exception as e:
            return title

    async def _optimize_content(self, article: Dict) -> Optional[str]:
        """تحسين محتوى المقال"""
        try:
            if not self.model:
                return self._optimize_content_basic(article)

            original_content = article.get('content', '')
            title = article['title']

            if len(original_content) < 100:
                return None

            prompt = f"""
            أنت خبير في تحسين محتوى المقالات لزيادة وقت القراءة والتفاعل.

            العنوان: "{title}"
            المحتوى الحالي: "{original_content[:1000]}"

            حسن المحتوى ليصبح:
            1. أكثر جاذبية وإثارة للاهتمام
            2. منظم بشكل أفضل مع فقرات واضحة
            3. يحتوي على معلومات قيمة ومفيدة
            4. يشجع القارئ على المتابعة
            5. محسن لمحركات البحث

            اكتب المحتوى المحسن:
            """

            response = self.model.generate_content(prompt)
            optimized_content = response.text.strip()

            if len(optimized_content) > len(original_content) * 0.8:
                return optimized_content
            else:
                return self._optimize_content_basic(article)

        except Exception as e:
            logger.error("❌ فشل في تحسين المحتوى باستخدام AI", e)
            return self._optimize_content_basic(article)

    def _optimize_content_basic(self, article: Dict) -> Optional[str]:
        """تحسين أساسي للمحتوى"""
        try:
            original_content = article.get('content', '') or ''

            if len(original_content) < 10:
                return None

            # إضافة مقدمة جذابة
            intro = "🎮 في عالم الألعاب المتطور باستمرار، "

            # تحسين التنسيق
            improved_content = intro + original_content

            # إضافة فقرات وتنسيق
            improved_content = improved_content.replace('. ', '.\n\n')

            # إضافة خاتمة
            outro = "\n\n💡 ما رأيك في هذا الموضوع؟ شاركنا تعليقك أدناه!"
            improved_content += outro

            return improved_content

        except Exception as e:
            logger.error("❌ فشل في التحسين الأساسي للمحتوى", e)
            return None

    
    async def _optimize_keywords_fixed(self, article: Dict) -> Optional[str]:
        """تحسين الكلمات المفتاحية مع حماية شاملة من الأخطاء"""
        try:
            # التحقق من صحة البيانات
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return self._optimize_keywords_basic(article) if article else None
            
            title = article.get('title', '')
            content = article.get('content', '')
            current_keywords = article.get('keywords', [])
            
            # التحقق من وجود العنوان
            if not title or not isinstance(title, str):
                logger.warning("⚠️ العنوان غير صالح")
                return self._optimize_keywords_basic(article)
            
            # التحقق من المحتوى
            if not content or not isinstance(content, str):
                logger.warning("⚠️ المحتوى غير صالح")
                content = title  # استخدام العنوان كبديل
            
            # تنظيف الكلمات المفتاحية الحالية
            if current_keywords is None:
                current_keywords = []
            elif isinstance(current_keywords, str):
                current_keywords = [kw.strip() for kw in current_keywords.split(',') if kw.strip()]
            elif not isinstance(current_keywords, list):
                current_keywords = []
            
            # محاولة استخدام AI إذا كان متوفراً
            if self.model:
                try:
                    prompt = f"""
                    أنت خبير SEO متخصص في ألعاب الفيديو.

                    العنوان: "{title}"
                    المحتوى: "{content[:500]}"
                    الكلمات المفتاحية الحالية: "{', '.join(current_keywords)}"

                    اقترح كلمات مفتاحية محسنة:
                    1. ذات صلة بالمحتوى
                    2. شائعة البحث في مجال الألعاب
                    3. متوسطة المنافسة
                    4. باللغة العربية والإنجليزية
                    5. 8-12 كلمة مفتاحية

                    اكتب الكلمات المفتاحية مفصولة بفواصل:
                    """

                    response = self.model.generate_content(prompt)
                    
                    if response and response.text:
                        optimized_keywords = response.text.strip()
                        
                        # تنظيف الكلمات المفتاحية
                        import re
                        optimized_keywords = re.sub(r'["\']', '', optimized_keywords)
                        
                        # التحقق من صحة النتيجة
                        if optimized_keywords and len(optimized_keywords) > 10:
                            return optimized_keywords
                        else:
                            logger.warning("⚠️ نتيجة AI غير مناسبة، استخدام الطريقة الأساسية")
                            return self._optimize_keywords_basic(article)
                    else:
                        logger.warning("⚠️ لم يتم الحصول على استجابة من AI")
                        return self._optimize_keywords_basic(article)
                        
                except Exception as ai_error:
                    logger.warning(f"⚠️ فشل في استخدام AI لتحسين الكلمات المفتاحية: {ai_error}")
                    return self._optimize_keywords_basic(article)
            else:
                logger.info("ℹ️ AI غير متوفر، استخدام الطريقة الأساسية")
                return self._optimize_keywords_basic(article)

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الكلمات المفتاحية: {e}")
            return self._optimize_keywords_basic(article) if article else None

    async def _optimize_keywords(self, article: Dict) -> Optional[str]:
        """تحسين الكلمات المفتاحية"""
        try:
            if not self.model:
                return self._optimize_keywords_basic(article)

            title = article['title']
            content = article.get('content', '')[:500]
            current_keywords = article.get('keywords', '')

            prompt = f"""
            أنت خبير SEO متخصص في ألعاب الفيديو.

            العنوان: "{title}"
            المحتوى: "{content}"
            الكلمات المفتاحية الحالية: "{current_keywords}"

            اقترح كلمات مفتاحية محسنة:
            1. ذات صلة بالمحتوى
            2. شائعة البحث في مجال الألعاب
            3. متوسطة المنافسة
            4. باللغة العربية والإنجليزية
            5. 8-12 كلمة مفتاحية

            اكتب الكلمات المفتاحية مفصولة بفواصل:
            """

            response = self.model.generate_content(prompt)
            optimized_keywords = response.text.strip()

            # تنظيف الكلمات المفتاحية
            optimized_keywords = re.sub(r'["\']', '', optimized_keywords)

            return optimized_keywords

        except Exception as e:
            logger.error("❌ فشل في تحسين الكلمات المفتاحية باستخدام AI", e)
            return self._optimize_keywords_basic(article)

    def _optimize_keywords_basic(self, article: Dict) -> Optional[str]:
        """تحسين أساسي للكلمات المفتاحية"""
        try:
            title = article['title'].lower()

            # كلمات مفتاحية شائعة في الألعاب
            gaming_keywords = [
                'ألعاب', 'gaming', 'games', 'لعبة', 'مراجعة', 'review',
                'أخبار الألعاب', 'gaming news', 'تحديث', 'update',
                'دليل', 'guide', 'نصائح', 'tips', 'استراتيجية', 'strategy'
            ]

            # استخراج كلمات من العنوان
            title_words = re.findall(r'\b\w+\b', title)

            # دمج الكلمات
            all_keywords = []

            # إضافة كلمات من العنوان
            for word in title_words:
                if len(word) > 3:
                    all_keywords.append(word)

            # إضافة كلمات مفتاحية عامة
            all_keywords.extend(gaming_keywords[:5])

            # إزالة التكرار
            unique_keywords = list(dict.fromkeys(all_keywords))

            return ', '.join(unique_keywords[:10])

        except Exception as e:
            logger.error("❌ فشل في التحسين الأساسي للكلمات المفتاحية", e)
            return None

    async def _optimize_category_and_tags(self, article: Dict) -> Optional[str]:
        """تحسين التصنيف والوسوم"""
        try:
            title = (article.get('title', '') or '').lower()
            content = (article.get('content', '') or '').lower()

            # تصنيفات الألعاب
            categories = {
                'مراجعات': ['مراجعة', 'review', 'تقييم', 'رأي'],
                'أخبار': ['خبر', 'news', 'إعلان', 'announcement', 'تحديث'],
                'أدلة': ['دليل', 'guide', 'شرح', 'tutorial', 'نصائح'],
                'قوائم': ['أفضل', 'best', 'top', 'قائمة', 'list'],
                'تحليلات': ['تحليل', 'analysis', 'مقارنة', 'comparison'],
                'أخبار تقنية': ['تقني', 'technical', 'مشكلة', 'issue', 'خطأ']
            }

            # البحث عن التصنيف المناسب
            for category, keywords in categories.items():
                for keyword in keywords:
                    if keyword in title or keyword in content:
                        return category

            return 'عام'

        except Exception as e:
            logger.error("❌ فشل في تحسين التصنيف", e)
            return None

    
    def _safe_get_article_data(self, article_id: str) -> Optional[Dict]:
        """الحصول على بيانات المقال مع حماية من NoneType"""
        try:
            article = self._get_full_article_data(article_id)
            if article is None:
                logger.warning(f"⚠️ لم يتم العثور على المقال {article_id}")
                return None
            
            # التأكد من وجود الحقول المطلوبة
            required_fields = ['title', 'content']
            for field in required_fields:
                if field not in article or article[field] is None:
                    logger.warning(f"⚠️ حقل مفقود أو None في المقال {article_id}: {field}")
                    article[field] = ""  # قيمة افتراضية
            
            # التأكد من وجود keywords
            if 'keywords' not in article or article['keywords'] is None:
                article['keywords'] = []
            elif isinstance(article['keywords'], str):
                article['keywords'] = [kw.strip() for kw in article['keywords'].split(',') if kw.strip()]
            
            return article
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات المقال {article_id}: {e}")
            return None
    
    def _safe_optimize_content(self, article: Dict) -> Optional[str]:
        """تحسين المحتوى مع حماية من NoneType"""
        try:
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return None
            
            title = article.get('title', '')
            content = article.get('content', '')
            
            if not title or not content:
                logger.warning("⚠️ العنوان أو المحتوى فارغ")
                return None
            
            # استدعاء الدالة الأصلية مع حماية
            return self._optimize_content(article)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين المحتوى: {e}")
            return None
    
    def _safe_optimize_keywords(self, article: Dict) -> Optional[str]:
        """تحسين الكلمات المفتاحية مع حماية من NoneType"""
        try:
            if not article or not isinstance(article, dict):
                logger.warning("⚠️ بيانات المقال غير صالحة")
                return None
            
            title = article.get('title', '')
            content = article.get('content', '')
            keywords = article.get('keywords', [])
            
            if not title:
                logger.warning("⚠️ العنوان فارغ")
                return None
            
            # التأكد من أن keywords قائمة
            if keywords is None:
                keywords = []
            elif isinstance(keywords, str):
                keywords = [kw.strip() for kw in keywords.split(',') if kw.strip()]
            
            # تحديث المقال
            article['keywords'] = keywords
            
            # استدعاء الدالة الأصلية مع حماية
            return self._optimize_keywords(article)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الكلمات المفتاحية: {e}")
            return None

    def _get_full_article_data(self, article_id: int) -> Optional[Dict]:
        """الحصول على بيانات المقال الكاملة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT id, title, content, keywords, category, blogger_url, published_at
                    FROM published_articles
                    WHERE id = ?
                ''', (article_id,))

                row = cursor.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'keywords': row[3],
                        'category': row[4],
                        'blogger_url': row[5],
                        'published_at': row[6]
                    }

                return None

        except Exception as e:
            logger.error(f"❌ فشل في الحصول على بيانات المقال {article_id}", e)
            return None

    def _save_optimization_results(self, article_id: int, optimization_results: Dict, performance_before):
        """حفظ نتائج التحسين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for opt_type, opt_data in optimization_results.items():
                    cursor.execute('''
                        INSERT INTO content_optimizations
                        (article_id, optimization_type, original_value, optimized_value, performance_before)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        article_id,
                        opt_data['improvement_type'],
                        opt_data['original'],
                        opt_data['optimized'],
                        performance_before.engagement_score
                    ))

                conn.commit()

        except Exception as e:
            logger.error("❌ فشل في حفظ نتائج التحسين", e)

    async def run_automatic_optimization(self) -> Dict:
        """تشغيل التحسين التلقائي للمقالات ضعيفة الأداء"""
        try:
            logger.info("🚀 بدء التحسين التلقائي للمحتوى...")

            # تحديد المقالات ضعيفة الأداء
            poor_articles = await self.identify_poor_performing_articles()

            if not poor_articles:
                logger.info("✅ لا توجد مقالات تحتاج تحسين حالياً")
                return {'optimized_count': 0, 'message': 'لا توجد مقالات تحتاج تحسين'}

            optimized_count = 0
            optimization_summary = []

            # تحسين أول 5 مقالات (لتجنب الإفراط)
            for article in poor_articles[:5]:
                try:
                    logger.info(f"🔧 تحسين المقال: {article['title'][:50]}...")

                    optimization_result = await self.optimize_article_automatically(article)

                    if optimization_result:
                        optimized_count += 1
                        optimization_summary.append({
                            'article_id': article['article_id'],
                            'title': article['title'],
                            'improvements': list(optimization_result.keys()),
                            'priority': article['priority']
                        })

                        logger.info(f"✅ تم تحسين {len(optimization_result)} عنصر")

                except Exception as e:
                    logger.error(f"❌ فشل في تحسين المقال {article['article_id']}", e)
                    continue

            result = {
                'optimized_count': optimized_count,
                'total_identified': len(poor_articles),
                'optimization_summary': optimization_summary,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"🎉 تم تحسين {optimized_count} مقال من أصل {len(poor_articles)} مقال ضعيف الأداء")

            return result

        except Exception as e:
            logger.error("❌ فشل في التحسين التلقائي", e)
            return {'optimized_count': 0, 'error': str(e)}

# إنشاء مثيل عام لمحسن المحتوى
content_optimizer = ContentOptimizer()
