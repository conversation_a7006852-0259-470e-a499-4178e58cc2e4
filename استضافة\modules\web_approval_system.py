# نظام الموافقة على المحتوى عبر الواجهة الويب
import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Optional, Callable, List
from .logger import logger
from .database import db

class WebApprovalSystem:
    """نظام الموافقة على المحتوى عبر الواجهة الويب بدلاً من Telegram"""

    def __init__(self):
        self.pending_approvals = {}  # المحتوى في انتظار الموافقة
        self.approval_callbacks = {}  # callbacks للموافقة/الرفض
        self.approval_enabled = True  # نظام الموافقة مفعل دائماً
        self.auto_approve_timeout = 10  # 10 ثوان للموافقة التلقائية (مدة قصيرة)
        self.auto_approve_enabled = True  # الموافقة التلقائية مفعلة افتراضياً

        logger.info("✅ تم تهيئة نظام الموافقة الويب مع الموافقة التلقائية")

    async def request_content_approval(self, content_data: Dict, approval_callback: Callable, content_type: str = "article") -> str:
        """طلب موافقة على المحتوى"""
        try:
            # إنشاء معرف فريد للموافقة
            approval_id = str(uuid.uuid4())

            # تحضير بيانات الموافقة
            approval_data = {
                'id': approval_id,
                'type': content_type,
                'title': content_data.get('title', 'بدون عنوان'),
                'description': content_data.get('summary', content_data.get('content', ''))[:200] + '...',
                'source': content_data.get('source', 'غير محدد'),
                'url': content_data.get('url', ''),
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'content_data': content_data
            }

            # حفظ في قائمة الموافقات المعلقة
            self.pending_approvals[approval_id] = approval_data
            self.approval_callbacks[approval_id] = approval_callback

            logger.info(f"📋 طلب موافقة جديد: {approval_data['title']}")

            # إذا كانت الموافقة التلقائية مفعلة
            if self.auto_approve_enabled:
                logger.info(f"⏰ الموافقة التلقائية مفعلة - سيتم الموافقة خلال {self.auto_approve_timeout} ثانية")
                asyncio.create_task(self._auto_approve_after_timeout(approval_id))
            else:
                logger.info(f"📋 تم إرسال طلب موافقة للواجهة الويب: {approval_id}")

            return approval_id

        except Exception as e:
            logger.error(f"❌ خطأ في طلب الموافقة: {e}")
            return None

    async def request_video_approval(self, video_data: Dict, approval_callback: Callable, extracted_text: str = None) -> str:
        """طلب موافقة على فيديو YouTube"""
        try:
            # تحضير بيانات الفيديو للموافقة
            video_content = {
                'title': video_data.get('title', 'فيديو بدون عنوان'),
                'summary': extracted_text[:300] + '...' if extracted_text else video_data.get('description', ''),
                'source': 'YouTube',
                'url': f"https://www.youtube.com/watch?v={video_data.get('id', '')}",
                'duration': video_data.get('duration', 'غير محدد'),
                'channel': video_data.get('channel_title', 'غير محدد'),
                'published_date': video_data.get('published_at', ''),
                'video_data': video_data,
                'extracted_text': extracted_text
            }

            return await self.request_content_approval(video_content, approval_callback, "video")

        except Exception as e:
            logger.error(f"❌ خطأ في طلب موافقة الفيديو: {e}")
            return None

    async def approve_content(self, approval_id: str, approved_by: str = "web_user") -> bool:
        """الموافقة على المحتوى"""
        try:
            if approval_id not in self.pending_approvals:
                logger.warning(f"⚠️ طلب موافقة غير موجود: {approval_id}")
                return False

            approval_data = self.pending_approvals[approval_id]
            callback = self.approval_callbacks.get(approval_id)

            if callback:
                # تنفيذ callback الموافقة
                try:
                    await callback(True, f"تمت الموافقة بواسطة {approved_by}")
                except Exception as callback_error:
                    logger.warning(f"⚠️ خطأ في تنفيذ callback: {callback_error}")

                # تحديث حالة الموافقة
                approval_data['status'] = 'approved'
                approval_data['approved_by'] = approved_by
                approval_data['approved_at'] = datetime.now().isoformat()

                # إزالة من قائمة الانتظار
                del self.pending_approvals[approval_id]
                del self.approval_callbacks[approval_id]

                logger.info(f"✅ تمت الموافقة على: {approval_data['title']}")

                # حفظ في قاعدة البيانات للتتبع (إذا كانت الدالة متاحة)
                if hasattr(db, 'log_approval_action'):
                    db.log_approval_action(approval_id, 'approved', approved_by, approval_data['title'])

                return True
            else:
                logger.error(f"❌ لا يوجد callback للموافقة: {approval_id}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في الموافقة: {e}")
            return False

    async def reject_content(self, approval_id: str, rejected_by: str = "web_user", reason: str = "تم الرفض") -> bool:
        """رفض المحتوى"""
        try:
            if approval_id not in self.pending_approvals:
                logger.warning(f"⚠️ طلب موافقة غير موجود: {approval_id}")
                return False

            approval_data = self.pending_approvals[approval_id]
            callback = self.approval_callbacks.get(approval_id)

            if callback:
                # تنفيذ callback الرفض
                result = await callback(False, f"تم الرفض بواسطة {rejected_by}: {reason}")

                # تحديث حالة الموافقة
                approval_data['status'] = 'rejected'
                approval_data['rejected_by'] = rejected_by
                approval_data['rejected_at'] = datetime.now().isoformat()
                approval_data['rejection_reason'] = reason

                # إزالة من قائمة الانتظار
                del self.pending_approvals[approval_id]
                del self.approval_callbacks[approval_id]

                logger.info(f"❌ تم رفض: {approval_data['title']} - السبب: {reason}")

                # حفظ في قاعدة البيانات للتتبع (إذا كانت الدالة متاحة)
                if hasattr(db, 'log_approval_action'):
                    db.log_approval_action(approval_id, 'rejected', rejected_by, approval_data['title'], reason)

                return True
            else:
                logger.error(f"❌ لا يوجد callback للرفض: {approval_id}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في الرفض: {e}")
            return False

    def get_pending_approvals(self) -> List[Dict]:
        """الحصول على قائمة الموافقات المعلقة"""
        try:
            pending_list = []
            for approval_id, approval_data in self.pending_approvals.items():
                # إضافة معلومات إضافية للعرض
                display_data = {
                    'id': approval_id,
                    'type': approval_data['type'],
                    'title': approval_data['title'],
                    'description': approval_data['description'],
                    'source': approval_data['source'],
                    'url': approval_data['url'],
                    'created_at': approval_data['created_at'],
                    'waiting_time': self._calculate_waiting_time(approval_data['created_at'])
                }

                # إضافة معلومات خاصة بالفيديو إذا كان النوع فيديو
                if approval_data['type'] == 'video':
                    content_data = approval_data.get('content_data', {})
                    display_data.update({
                        'duration': content_data.get('duration', 'غير محدد'),
                        'channel': content_data.get('channel', 'غير محدد'),
                        'published_date': content_data.get('published_date', '')
                    })

                pending_list.append(display_data)

            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            pending_list.sort(key=lambda x: x['created_at'], reverse=True)

            return pending_list

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الموافقات المعلقة: {e}")
            return []

    def get_approval_stats(self) -> Dict:
        """الحصول على إحصائيات الموافقات"""
        try:
            stats = {
                'pending_count': len(self.pending_approvals),
                'auto_approve_enabled': self.auto_approve_enabled,
                'auto_approve_timeout': self.auto_approve_timeout,
                'total_processed_today': self._get_daily_approval_count()
            }

            return stats

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الموافقات: {e}")
            return {}

    def set_auto_approval(self, enabled: bool, timeout: int = 300):
        """تفعيل/تعطيل الموافقة التلقائية"""
        self.auto_approve_enabled = enabled
        self.auto_approve_timeout = timeout

        status = "مفعلة" if enabled else "معطلة"
        logger.info(f"⚙️ الموافقة التلقائية {status} - المهلة: {timeout} ثانية")

    async def _auto_approve_after_timeout(self, approval_id: str):
        """موافقة تلقائية بعد انتهاء المهلة"""
        try:
            await asyncio.sleep(self.auto_approve_timeout)

            # التحقق من أن الموافقة ما زالت معلقة
            if approval_id in self.pending_approvals:
                logger.info(f"⏰ انتهت مهلة الانتظار - موافقة تلقائية على: {self.pending_approvals[approval_id]['title']}")
                await self.approve_content(approval_id, "auto_system")

        except Exception as e:
            logger.error(f"❌ خطأ في الموافقة التلقائية: {e}")

    def _calculate_waiting_time(self, created_at: str) -> str:
        """حساب وقت الانتظار"""
        try:
            created_time = datetime.fromisoformat(created_at)
            waiting_time = datetime.now() - created_time

            if waiting_time.total_seconds() < 60:
                return f"{int(waiting_time.total_seconds())} ثانية"
            elif waiting_time.total_seconds() < 3600:
                return f"{int(waiting_time.total_seconds() / 60)} دقيقة"
            else:
                return f"{int(waiting_time.total_seconds() / 3600)} ساعة"

        except Exception as e:
            logger.error(f"❌ خطأ في حساب وقت الانتظار: {e}")
            return "غير محدد"

    def _get_daily_approval_count(self) -> int:
        """الحصول على عدد الموافقات اليومية"""
        try:
            # يمكن تحسين هذا بالحصول على البيانات من قاعدة البيانات
            return db.get_daily_approval_count() if hasattr(db, 'get_daily_approval_count') else 0
        except:
            return 0

    def cleanup_expired_approvals(self, max_age_hours: int = 24):
        """تنظيف الموافقات المنتهية الصلاحية"""
        try:
            current_time = datetime.now()
            expired_ids = []

            for approval_id, approval_data in self.pending_approvals.items():
                created_time = datetime.fromisoformat(approval_data['created_at'])
                age_hours = (current_time - created_time).total_seconds() / 3600

                if age_hours > max_age_hours:
                    expired_ids.append(approval_id)

            # إزالة الموافقات المنتهية الصلاحية
            for approval_id in expired_ids:
                approval_data = self.pending_approvals[approval_id]
                logger.warning(f"⏰ انتهت صلاحية طلب الموافقة: {approval_data['title']}")

                # تنفيذ callback الرفض
                callback = self.approval_callbacks.get(approval_id)
                if callback:
                    asyncio.create_task(callback(False, "انتهت صلاحية طلب الموافقة"))

                # إزالة من القوائم
                del self.pending_approvals[approval_id]
                if approval_id in self.approval_callbacks:
                    del self.approval_callbacks[approval_id]

            if expired_ids:
                logger.info(f"🧹 تم تنظيف {len(expired_ids)} طلب موافقة منتهي الصلاحية")

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الموافقات المنتهية الصلاحية: {e}")

# إنشاء مثيل عام من نظام الموافقة الويب
web_approval_system = WebApprovalSystem()