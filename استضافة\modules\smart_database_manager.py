# نظام إدارة قاعدة البيانات الذكي
import os
import sqlite3
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from .logger import logger

@dataclass
class TableSchema:
    """مخطط الجدول"""
    name: str
    columns: List[Dict[str, str]]
    indexes: List[str]
    version: int

@dataclass
class MigrationInfo:
    """معلومات الترحيل"""
    version: int
    description: str
    sql_commands: List[str]
    rollback_commands: List[str]

class SmartDatabaseManager:
    """مدير قاعدة البيانات الذكي"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._lock = threading.Lock()
        self.current_version = 1
        self.migrations: List[MigrationInfo] = []
        
        # تعريف مخططات الجداول
        self.table_schemas = self._define_table_schemas()
        
        # تعريف الترحيلات
        self._define_migrations()
    
    def _define_table_schemas(self) -> Dict[str, TableSchema]:
        """تعريف مخططات الجداول"""
        schemas = {}
        
        # جدول المقالات المنشورة
        schemas['published_articles'] = TableSchema(
            name='published_articles',
            columns=[
                {'name': 'id', 'type': 'INTEGER PRIMARY KEY AUTOINCREMENT'},
                {'name': 'title', 'type': 'TEXT NOT NULL'},
                {'name': 'content', 'type': 'TEXT'},
                {'name': 'content_hash', 'type': 'TEXT UNIQUE NOT NULL'},
                {'name': 'semantic_hash', 'type': 'TEXT NOT NULL'},
                {'name': 'keywords', 'type': 'TEXT'},
                {'name': 'category', 'type': 'TEXT'},
                {'name': 'source_url', 'type': 'TEXT'},
                {'name': 'blogger_url', 'type': 'TEXT'},
                {'name': 'telegram_message_id', 'type': 'INTEGER'},
                {'name': 'published_date', 'type': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'},
                {'name': 'views', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'engagement_score', 'type': 'REAL DEFAULT 0'},
                {'name': 'seo_score', 'type': 'REAL DEFAULT 0'},
                {'name': 'quality_score', 'type': 'REAL DEFAULT 0'},
                {'name': 'image_urls', 'type': 'TEXT'},
                {'name': 'tags', 'type': 'TEXT'},
                {'name': 'meta_description', 'type': 'TEXT'},
                {'name': 'reading_time', 'type': 'INTEGER'},
                {'name': 'word_count', 'type': 'INTEGER'},
                {'name': 'language', 'type': 'TEXT DEFAULT "ar"'},
                {'name': 'status', 'type': 'TEXT DEFAULT "published"'}
            ],
            indexes=[
                'CREATE INDEX IF NOT EXISTS idx_published_articles_hash ON published_articles(content_hash)',
                'CREATE INDEX IF NOT EXISTS idx_published_articles_semantic ON published_articles(semantic_hash)',
                'CREATE INDEX IF NOT EXISTS idx_published_articles_date ON published_articles(published_date)',
                'CREATE INDEX IF NOT EXISTS idx_published_articles_category ON published_articles(category)',
                'CREATE INDEX IF NOT EXISTS idx_published_articles_status ON published_articles(status)'
            ],
            version=1
        )
        
        # جدول المصادر المراقبة
        schemas['monitored_sources'] = TableSchema(
            name='monitored_sources',
            columns=[
                {'name': 'id', 'type': 'INTEGER PRIMARY KEY AUTOINCREMENT'},
                {'name': 'source_url', 'type': 'TEXT UNIQUE NOT NULL'},
                {'name': 'source_type', 'type': 'TEXT NOT NULL'},
                {'name': 'last_checked', 'type': 'TIMESTAMP'},
                {'name': 'success_count', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'failure_count', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'is_active', 'type': 'BOOLEAN DEFAULT 1'},
                {'name': 'priority', 'type': 'INTEGER DEFAULT 1'},
                {'name': 'check_interval', 'type': 'INTEGER DEFAULT 3600'},
                {'name': 'last_error', 'type': 'TEXT'},
                {'name': 'created_at', 'type': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'}
            ],
            indexes=[
                'CREATE INDEX IF NOT EXISTS idx_monitored_sources_type ON monitored_sources(source_type)',
                'CREATE INDEX IF NOT EXISTS idx_monitored_sources_active ON monitored_sources(is_active)',
                'CREATE INDEX IF NOT EXISTS idx_monitored_sources_priority ON monitored_sources(priority)'
            ],
            version=1
        )
        
        # جدول سجل الأخطاء
        schemas['error_log'] = TableSchema(
            name='error_log',
            columns=[
                {'name': 'id', 'type': 'INTEGER PRIMARY KEY AUTOINCREMENT'},
                {'name': 'error_type', 'type': 'TEXT NOT NULL'},
                {'name': 'error_message', 'type': 'TEXT'},
                {'name': 'stack_trace', 'type': 'TEXT'},
                {'name': 'source_url', 'type': 'TEXT'},
                {'name': 'timestamp', 'type': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'},
                {'name': 'severity', 'type': 'TEXT DEFAULT "error"'},
                {'name': 'resolved', 'type': 'BOOLEAN DEFAULT 0'},
                {'name': 'resolution_notes', 'type': 'TEXT'}
            ],
            indexes=[
                'CREATE INDEX IF NOT EXISTS idx_error_log_type ON error_log(error_type)',
                'CREATE INDEX IF NOT EXISTS idx_error_log_timestamp ON error_log(timestamp)',
                'CREATE INDEX IF NOT EXISTS idx_error_log_severity ON error_log(severity)',
                'CREATE INDEX IF NOT EXISTS idx_error_log_resolved ON error_log(resolved)'
            ],
            version=1
        )
        
        # جدول إحصائيات الأداء
        schemas['performance_stats'] = TableSchema(
            name='performance_stats',
            columns=[
                {'name': 'id', 'type': 'INTEGER PRIMARY KEY AUTOINCREMENT'},
                {'name': 'date', 'type': 'DATE UNIQUE NOT NULL'},
                {'name': 'articles_processed', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'articles_published', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'api_calls_gemini', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'api_calls_search', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'errors_count', 'type': 'INTEGER DEFAULT 0'},
                {'name': 'uptime_hours', 'type': 'REAL DEFAULT 0'},
                {'name': 'memory_usage_avg', 'type': 'REAL DEFAULT 0'},
                {'name': 'cpu_usage_avg', 'type': 'REAL DEFAULT 0'}
            ],
            indexes=[
                'CREATE INDEX IF NOT EXISTS idx_performance_stats_date ON performance_stats(date)'
            ],
            version=1
        )
        
        return schemas
    
    def _define_migrations(self):
        """تعريف الترحيلات"""
        # ترحيل إضافة أعمدة جديدة للمقالات
        self.migrations.append(MigrationInfo(
            version=2,
            description="إضافة أعمدة التحليلات المتقدمة للمقالات",
            sql_commands=[
                "ALTER TABLE published_articles ADD COLUMN click_through_rate REAL DEFAULT 0",
                "ALTER TABLE published_articles ADD COLUMN bounce_rate REAL DEFAULT 0",
                "ALTER TABLE published_articles ADD COLUMN time_on_page INTEGER DEFAULT 0",
                "ALTER TABLE published_articles ADD COLUMN social_shares INTEGER DEFAULT 0"
            ],
            rollback_commands=[
                # SQLite لا يدعم DROP COLUMN، لذا نحتاج إعادة إنشاء الجدول
                "CREATE TABLE published_articles_backup AS SELECT id, title, content, content_hash, semantic_hash, keywords, category, source_url, blogger_url, telegram_message_id, published_date, views, engagement_score, seo_score, quality_score, image_urls, tags, meta_description, reading_time, word_count, language, status FROM published_articles",
                "DROP TABLE published_articles",
                "ALTER TABLE published_articles_backup RENAME TO published_articles"
            ]
        ))
    
    def check_and_initialize_database(self) -> bool:
        """فحص وتهيئة قاعدة البيانات (نسخة محسنة بدون تعليق)"""
        logger.info("🔍 فحص وتهيئة قاعدة البيانات...")

        try:
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not os.path.exists(self.db_path):
                logger.info("📁 إنشاء قاعدة بيانات جديدة...")
                self._create_new_database_simple()
                return True

            # فحص بسيط للجداول
            try:
                with sqlite3.connect(self.db_path, timeout=5.0) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    existing_tables = [row[0] for row in cursor.fetchall()]

                    # التأكد من وجود الجداول الأساسية
                    required_tables = ['published_articles', 'content_sources', 'article_performance']
                    missing_tables = [table for table in required_tables if table not in existing_tables]

                    if missing_tables:
                        logger.info(f"📋 إنشاء الجداول المفقودة: {', '.join(missing_tables)}")
                        # استخدام قاعدة البيانات الموجودة لإنشاء الجداول
                        from modules.database import db
                        db.init_database()

                    logger.info("✅ تم فحص وتهيئة قاعدة البيانات بنجاح")
                    return True

            except sqlite3.Error as e:
                logger.warning(f"⚠️ مشكلة في قاعدة البيانات: {e}")
                # إعادة إنشاء قاعدة البيانات
                self._create_new_database_simple()
                return True

        except Exception as e:
            logger.error(f"❌ فشل في فحص وتهيئة قاعدة البيانات: {e}")
            # محاولة أخيرة - استخدام النظام التقليدي
            try:
                from modules.database import db
                db.init_database()
                logger.info("✅ تم استخدام النظام التقليدي لتهيئة قاعدة البيانات")
                return True
            except:
                return False
    
    def _create_new_database_simple(self):
        """إنشاء قاعدة بيانات جديدة (مبسطة)"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            # استخدام النظام التقليدي لإنشاء قاعدة البيانات
            from modules.database import db
            db.init_database()

            logger.info("✅ تم إنشاء قاعدة البيانات الجديدة بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء قاعدة البيانات: {e}")
            raise

    def _create_new_database(self):
        """إنشاء قاعدة بيانات جديدة"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # إنشاء جدول إصدار قاعدة البيانات
            cursor.execute('''
                CREATE TABLE database_version (
                    version INTEGER PRIMARY KEY,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            ''')
            
            # تسجيل الإصدار الحالي
            cursor.execute(
                "INSERT INTO database_version (version, description) VALUES (?, ?)",
                (self.current_version, "إنشاء قاعدة البيانات الأولي")
            )
            
            # إنشاء جميع الجداول
            for schema in self.table_schemas.values():
                self._create_table(schema, cursor)
            
            conn.commit()
            logger.info("✅ تم إنشاء قاعدة البيانات الجديدة")
    
    def _create_table(self, schema: TableSchema, cursor: sqlite3.Cursor = None):
        """إنشاء جدول من المخطط"""
        should_close = cursor is None
        
        if cursor is None:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
        
        try:
            # بناء استعلام إنشاء الجدول
            columns_sql = []
            for col in schema.columns:
                columns_sql.append(f"{col['name']} {col['type']}")
            
            create_sql = f"CREATE TABLE IF NOT EXISTS {schema.name} ({', '.join(columns_sql)})"
            cursor.execute(create_sql)
            
            # إنشاء الفهارس
            for index_sql in schema.indexes:
                cursor.execute(index_sql)
            
            if should_close:
                conn.commit()
                conn.close()
            
            logger.info(f"✅ تم إنشاء الجدول: {schema.name}")
            
        except Exception as e:
            if should_close and 'conn' in locals():
                conn.close()
            raise e

    def _get_existing_tables(self) -> List[str]:
        """الحصول على قائمة الجداول الموجودة"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            return [row[0] for row in cursor.fetchall()]

    def _get_database_version(self) -> int:
        """الحصول على إصدار قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT MAX(version) FROM database_version")
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0
        except sqlite3.OperationalError:
            # جدول الإصدار غير موجود
            return 0

    def _apply_migrations(self, from_version: int):
        """تطبيق الترحيلات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for migration in self.migrations:
                if migration.version > from_version:
                    logger.info(f"🔄 تطبيق الترحيل {migration.version}: {migration.description}")

                    try:
                        # تطبيق أوامر الترحيل
                        for sql_command in migration.sql_commands:
                            cursor.execute(sql_command)

                        # تسجيل الترحيل
                        cursor.execute(
                            "INSERT INTO database_version (version, description) VALUES (?, ?)",
                            (migration.version, migration.description)
                        )

                        conn.commit()
                        logger.info(f"✅ تم تطبيق الترحيل {migration.version}")

                    except Exception as e:
                        logger.error(f"❌ فشل في تطبيق الترحيل {migration.version}: {e}")
                        conn.rollback()
                        raise

    def _create_missing_indexes(self):
        """إنشاء الفهارس المفقودة"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for schema in self.table_schemas.values():
                for index_sql in schema.indexes:
                    try:
                        cursor.execute(index_sql)
                    except sqlite3.OperationalError as e:
                        if "already exists" not in str(e):
                            logger.warning(f"⚠️ فشل في إنشاء فهرس: {e}")

            conn.commit()

    def _optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # تحليل الجداول
                cursor.execute("ANALYZE")

                # فحص الحاجة للتنظيف
                cursor.execute("PRAGMA freelist_count")
                free_pages = cursor.fetchone()[0]

                if free_pages > 100:  # إذا كان هناك أكثر من 100 صفحة فارغة
                    logger.info("🧹 تنظيف قاعدة البيانات...")
                    cursor.execute("VACUUM")

                conn.commit()

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحسين قاعدة البيانات: {e}")

    def backup_database(self, backup_path: str = None) -> str:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_database_{timestamp}.db"

        try:
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)

            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            raise

    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        if not os.path.exists(backup_path):
            logger.error(f"❌ ملف النسخة الاحتياطية غير موجود: {backup_path}")
            return False

        try:
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            current_backup = self.backup_database(f"current_backup_{int(time.time())}.db")

            # استعادة من النسخة الاحتياطية
            with sqlite3.connect(backup_path) as source:
                with sqlite3.connect(self.db_path) as target:
                    source.backup(target)

            logger.info(f"✅ تم استعادة قاعدة البيانات من: {backup_path}")
            logger.info(f"📁 النسخة الاحتياطية من قاعدة البيانات السابقة: {current_backup}")

            return True

        except Exception as e:
            logger.error(f"❌ فشل في استعادة قاعدة البيانات: {e}")
            return False

    def get_database_health_report(self) -> Dict[str, Any]:
        """تقرير صحة قاعدة البيانات"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'file_size_mb': 0,
            'total_tables': 0,
            'total_records': 0,
            'integrity_check': 'unknown',
            'vacuum_needed': False,
            'missing_tables': [],
            'missing_indexes': [],
            'performance_issues': [],
            'recommendations': []
        }

        try:
            # حجم الملف
            if os.path.exists(self.db_path):
                report['file_size_mb'] = os.path.getsize(self.db_path) / (1024 * 1024)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # فحص السلامة
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()
                report['integrity_check'] = integrity_result[0]

                # عدد الجداول
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                report['total_tables'] = cursor.fetchone()[0]

                # عدد السجلات الإجمالي
                existing_tables = self._get_existing_tables()
                total_records = 0

                for table in existing_tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        total_records += cursor.fetchone()[0]
                    except:
                        pass

                report['total_records'] = total_records

                # فحص الجداول المفقودة
                for table_name in self.table_schemas.keys():
                    if table_name not in existing_tables:
                        report['missing_tables'].append(table_name)

                # فحص الحاجة للتنظيف
                cursor.execute("PRAGMA freelist_count")
                free_pages = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_count")
                total_pages = cursor.fetchone()[0]

                if total_pages > 0 and (free_pages / total_pages) > 0.1:
                    report['vacuum_needed'] = True
                    report['recommendations'].append("تنظيف قاعدة البيانات (VACUUM)")

                # فحص الأداء
                if report['file_size_mb'] > 100:
                    report['performance_issues'].append("حجم قاعدة البيانات كبير")
                    report['recommendations'].append("أرشفة البيانات القديمة")

                if report['missing_tables']:
                    report['recommendations'].append("إنشاء الجداول المفقودة")

                if report['integrity_check'] != 'ok':
                    report['recommendations'].append("إصلاح مشاكل سلامة قاعدة البيانات")

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء تقرير صحة قاعدة البيانات: {e}")
            report['error'] = str(e)

        return report

    def cleanup_old_data(self, days: int = 30):
        """تنظيف البيانات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # تنظيف سجل الأخطاء القديمة
                cursor.execute(
                    "DELETE FROM error_log WHERE timestamp < ? AND resolved = 1",
                    (cutoff_date.isoformat(),)
                )
                deleted_errors = cursor.rowcount

                # تنظيف إحصائيات الأداء القديمة (الاحتفاظ بآخر 90 يوم)
                old_stats_date = datetime.now() - timedelta(days=90)
                cursor.execute(
                    "DELETE FROM performance_stats WHERE date < ?",
                    (old_stats_date.date().isoformat(),)
                )
                deleted_stats = cursor.rowcount

                conn.commit()

                if deleted_errors > 0 or deleted_stats > 0:
                    logger.info(f"🧹 تم تنظيف البيانات القديمة: {deleted_errors} خطأ، {deleted_stats} إحصائية")

        except Exception as e:
            logger.error(f"❌ فشل في تنظيف البيانات القديمة: {e}")

# إنشاء مثيل مدير قاعدة البيانات الذكي
smart_db_manager = SmartDatabaseManager("gaming_news_bot.db")
