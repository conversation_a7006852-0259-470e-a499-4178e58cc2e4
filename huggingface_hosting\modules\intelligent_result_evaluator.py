# نظام التقييم الذكي للنتائج
import asyncio
import json
import time
import re
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, Counter
import statistics

from .logger import logger
from .fallback_ai_manager import fallback_ai_manager
from .semantic_search_engine import semantic_search_engine, SemanticQuery

class EvaluationCriteria(Enum):
    """معايير التقييم"""
    RELEVANCE = "relevance"             # الصلة بالاستعلام
    QUALITY = "quality"                 # جودة المحتوى
    FRESHNESS = "freshness"             # حداثة المعلومات
    AUTHORITY = "authority"             # مصداقية المصدر
    COMPLETENESS = "completeness"       # اكتمال المعلومات
    UNIQUENESS = "uniqueness"           # تفرد المحتوى
    ENGAGEMENT = "engagement"           # إمكانية الجذب
    SEMANTIC_MATCH = "semantic_match"   # التطابق الدلالي

class EvaluationMethod(Enum):
    """طرق التقييم"""
    RULE_BASED = "rule_based"           # قائم على القواعد
    ML_BASED = "ml_based"              # قائم على التعلم الآلي
    AI_ENHANCED = "ai_enhanced"         # معزز بالذكاء الاصطناعي
    SEMANTIC = "semantic"               # دلالي
    HYBRID = "hybrid"                   # مختلط
    CROWD_SOURCED = "crowd_sourced"     # جماعي

@dataclass
class EvaluationScore:
    """نقاط التقييم"""
    criterion: EvaluationCriteria
    score: float
    confidence: float
    explanation: str
    method_used: EvaluationMethod
    metadata: Dict[str, Any]

@dataclass
class ResultEvaluation:
    """تقييم شامل للنتيجة"""
    result_id: str
    individual_scores: List[EvaluationScore]
    overall_score: float
    weighted_score: float
    ranking_score: float
    quality_tier: str  # excellent, good, fair, poor
    recommendation: str
    evaluation_time: float
    evaluator_confidence: float

class IntelligentResultEvaluator:
    """نظام التقييم الذكي للنتائج"""
    
    def __init__(self):
        # أوزان معايير التقييم
        self.evaluation_weights = {
            EvaluationCriteria.RELEVANCE: 0.25,
            EvaluationCriteria.QUALITY: 0.20,
            EvaluationCriteria.FRESHNESS: 0.15,
            EvaluationCriteria.AUTHORITY: 0.15,
            EvaluationCriteria.COMPLETENESS: 0.10,
            EvaluationCriteria.UNIQUENESS: 0.05,
            EvaluationCriteria.ENGAGEMENT: 0.05,
            EvaluationCriteria.SEMANTIC_MATCH: 0.05
        }
        
        # إعدادات التقييم
        self.evaluation_config = {
            'use_ai_enhancement': True,
            'semantic_analysis': True,
            'quality_threshold': 0.6,
            'relevance_threshold': 0.7,
            'freshness_decay_days': 30,
            'authority_boost': 0.1,
            'uniqueness_penalty': 0.2
        }
        
        # قوائم المصادر الموثوقة
        self.authority_sources = {
            'tier_1': [  # مصادر عالية المصداقية
                'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
                'eurogamer.net', 'pcgamer.com', 'gameinformer.com'
            ],
            'tier_2': [  # مصادر متوسطة المصداقية
                'destructoid.com', 'rockpapershotgun.com', 'gamesradar.com',
                'theverge.com', 'arstechnica.com', 'engadget.com'
            ],
            'tier_3': [  # مصادر منخفضة المصداقية
                'reddit.com', 'twitter.com', 'youtube.com', 'twitch.tv'
            ]
        }
        
        # إحصائيات التقييم
        self.evaluation_stats = {
            'total_evaluations': 0,
            'ai_enhanced_evaluations': 0,
            'semantic_evaluations': 0,
            'average_evaluation_time': 0.0,
            'quality_distribution': defaultdict(int),
            'method_usage': defaultdict(int)
        }
        
        logger.info("🎯 تم تهيئة نظام التقييم الذكي للنتائج")
    
    async def evaluate_results(self, 
                             results: List[Dict], 
                             query: str,
                             semantic_query: Optional[SemanticQuery] = None,
                             evaluation_method: EvaluationMethod = EvaluationMethod.HYBRID) -> List[ResultEvaluation]:
        """تقييم شامل لقائمة النتائج"""
        
        start_time = time.time()
        self.evaluation_stats['total_evaluations'] += len(results)
        self.evaluation_stats['method_usage'][evaluation_method.value] += 1
        
        try:
            evaluations = []
            
            # تحضير السياق الدلالي إذا لم يكن متوفراً
            if not semantic_query and self.evaluation_config['semantic_analysis']:
                semantic_query = await semantic_search_engine.semantic_search(query)
                self.evaluation_stats['semantic_evaluations'] += 1
            
            # تقييم كل نتيجة
            for i, result in enumerate(results):
                evaluation = await self._evaluate_single_result(
                    result, query, semantic_query, evaluation_method, f"result_{i}"
                )
                if evaluation:
                    evaluations.append(evaluation)
            
            # ترتيب النتائج
            ranked_evaluations = self._rank_evaluations(evaluations)
            
            # تحديث الإحصائيات
            evaluation_time = time.time() - start_time
            self._update_evaluation_stats(ranked_evaluations, evaluation_time)
            
            logger.info(f"🎯 تقييم مكتمل: {len(ranked_evaluations)} نتيجة في {evaluation_time:.2f}ث")
            
            return ranked_evaluations
            
        except Exception as e:
            logger.error(f"❌ فشل في تقييم النتائج: {e}")
            return []
    
    async def _evaluate_single_result(self, 
                                    result: Dict, 
                                    query: str,
                                    semantic_query: Optional[SemanticQuery],
                                    method: EvaluationMethod,
                                    result_id: str) -> Optional[ResultEvaluation]:
        """تقييم نتيجة واحدة"""
        
        start_time = time.time()
        
        try:
            individual_scores = []
            
            # تقييم كل معيار
            for criterion in EvaluationCriteria:
                score = await self._evaluate_criterion(
                    result, query, semantic_query, criterion, method
                )
                if score:
                    individual_scores.append(score)
            
            if not individual_scores:
                return None
            
            # حساب النقاط الإجمالية
            overall_score = self._calculate_overall_score(individual_scores)
            weighted_score = self._calculate_weighted_score(individual_scores)
            
            # تحديد مستوى الجودة
            quality_tier = self._determine_quality_tier(weighted_score)
            
            # إنشاء توصية
            recommendation = self._generate_recommendation(individual_scores, quality_tier)
            
            # حساب ثقة المقيم
            evaluator_confidence = self._calculate_evaluator_confidence(individual_scores)
            
            evaluation_time = time.time() - start_time
            
            evaluation = ResultEvaluation(
                result_id=result_id,
                individual_scores=individual_scores,
                overall_score=overall_score,
                weighted_score=weighted_score,
                ranking_score=weighted_score,  # سيتم تحديثه في الترتيب
                quality_tier=quality_tier,
                recommendation=recommendation,
                evaluation_time=evaluation_time,
                evaluator_confidence=evaluator_confidence
            )
            
            return evaluation
            
        except Exception as e:
            logger.error(f"❌ فشل في تقييم النتيجة {result_id}: {e}")
            return None
    
    async def _evaluate_criterion(self, 
                                result: Dict, 
                                query: str,
                                semantic_query: Optional[SemanticQuery],
                                criterion: EvaluationCriteria,
                                method: EvaluationMethod) -> Optional[EvaluationScore]:
        """تقييم معيار واحد"""
        
        try:
            if criterion == EvaluationCriteria.RELEVANCE:
                return await self._evaluate_relevance(result, query, semantic_query, method)
            elif criterion == EvaluationCriteria.QUALITY:
                return await self._evaluate_quality(result, method)
            elif criterion == EvaluationCriteria.FRESHNESS:
                return await self._evaluate_freshness(result, method)
            elif criterion == EvaluationCriteria.AUTHORITY:
                return await self._evaluate_authority(result, method)
            elif criterion == EvaluationCriteria.COMPLETENESS:
                return await self._evaluate_completeness(result, method)
            elif criterion == EvaluationCriteria.UNIQUENESS:
                return await self._evaluate_uniqueness(result, method)
            elif criterion == EvaluationCriteria.ENGAGEMENT:
                return await self._evaluate_engagement(result, method)
            elif criterion == EvaluationCriteria.SEMANTIC_MATCH:
                return await self._evaluate_semantic_match(result, semantic_query, method)
            
        except Exception as e:
            logger.error(f"❌ فشل في تقييم معيار {criterion.value}: {e}")
            return None
    
    async def _evaluate_relevance(self, result: Dict, query: str, semantic_query: Optional[SemanticQuery], method: EvaluationMethod) -> EvaluationScore:
        """تقييم الصلة بالاستعلام"""
        
        title = result.get('title', '').lower()
        content = result.get('content', '').lower()
        query_lower = query.lower()
        
        # التقييم الأساسي القائم على القواعد
        query_words = set(query_lower.split())
        title_words = set(title.split())
        content_words = set(content.split())
        
        # تطابق العنوان
        title_overlap = len(query_words & title_words) / max(len(query_words), 1)
        
        # تطابق المحتوى
        content_overlap = len(query_words & content_words) / max(len(query_words), 1)
        
        # النقاط الأساسية
        base_score = (title_overlap * 0.7) + (content_overlap * 0.3)
        
        # تحسين دلالي
        semantic_boost = 0.0
        if semantic_query and method in [EvaluationMethod.SEMANTIC, EvaluationMethod.HYBRID]:
            semantic_boost = self._calculate_semantic_relevance(result, semantic_query)
        
        # تحسين بالذكاء الاصطناعي
        ai_boost = 0.0
        if method in [EvaluationMethod.AI_ENHANCED, EvaluationMethod.HYBRID] and self.evaluation_config['use_ai_enhancement']:
            ai_boost = await self._ai_relevance_analysis(result, query)
            if ai_boost > 0:
                self.evaluation_stats['ai_enhanced_evaluations'] += 1
        
        final_score = min(base_score + semantic_boost + ai_boost, 1.0)
        confidence = 0.8 if semantic_boost > 0 or ai_boost > 0 else 0.6
        
        explanation = f"تطابق العنوان: {title_overlap:.2f}, تطابق المحتوى: {content_overlap:.2f}"
        if semantic_boost > 0:
            explanation += f", تحسين دلالي: {semantic_boost:.2f}"
        if ai_boost > 0:
            explanation += f", تحسين AI: {ai_boost:.2f}"
        
        return EvaluationScore(
            criterion=EvaluationCriteria.RELEVANCE,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'title_overlap': title_overlap,
                'content_overlap': content_overlap,
                'semantic_boost': semantic_boost,
                'ai_boost': ai_boost
            }
        )

    async def _evaluate_freshness(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم حداثة المعلومات"""

        score = 0.5  # نقاط أساسية

        # فحص تاريخ النشر
        published_date = result.get('published_date')
        if published_date:
            try:
                if isinstance(published_date, str):
                    from dateutil import parser
                    pub_date = parser.parse(published_date)
                else:
                    pub_date = published_date

                days_old = (datetime.now() - pub_date).days

                if days_old <= 1:
                    score = 1.0
                elif days_old <= 7:
                    score = 0.9
                elif days_old <= 30:
                    score = 0.7
                elif days_old <= 90:
                    score = 0.5
                else:
                    score = 0.3

            except Exception:
                score = 0.4  # تاريخ غير صالح
        else:
            # فحص مؤشرات الحداثة في النص
            text = f"{result.get('title', '')} {result.get('content', '')}".lower()
            freshness_indicators = ['today', 'yesterday', 'breaking', 'latest', '2025', 'just announced']

            indicator_count = sum(1 for indicator in freshness_indicators if indicator in text)
            score += min(indicator_count * 0.1, 0.3)

        final_score = min(score, 1.0)
        confidence = 0.8 if published_date else 0.5

        explanation = f"تاريخ النشر: {published_date or 'غير متوفر'}"
        if not published_date:
            explanation += ", فحص مؤشرات الحداثة في النص"

        return EvaluationScore(
            criterion=EvaluationCriteria.FRESHNESS,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'published_date': published_date,
                'has_date': bool(published_date)
            }
        )

    async def _evaluate_authority(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم مصداقية المصدر"""

        url = result.get('url', '').lower()
        source = result.get('source', '').lower()

        score = 0.5  # نقاط أساسية
        tier = 'unknown'

        # فحص المصادر الموثوقة
        for tier_name, sources in self.authority_sources.items():
            for trusted_source in sources:
                if trusted_source in url or trusted_source in source:
                    if tier_name == 'tier_1':
                        score = 0.95
                        tier = 'high'
                    elif tier_name == 'tier_2':
                        score = 0.75
                        tier = 'medium'
                    elif tier_name == 'tier_3':
                        score = 0.55
                        tier = 'low'
                    break
            if tier != 'unknown':
                break

        # مؤشرات إضافية للمصداقية
        if 'official' in source or 'press release' in result.get('content', '').lower():
            score += 0.1

        if result.get('author'):
            score += 0.05

        final_score = min(score, 1.0)
        confidence = 0.9 if tier != 'unknown' else 0.4

        explanation = f"مستوى المصداقية: {tier}, المصدر: {source or 'غير محدد'}"

        return EvaluationScore(
            criterion=EvaluationCriteria.AUTHORITY,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'authority_tier': tier,
                'source': source,
                'has_author': bool(result.get('author'))
            }
        )

    async def _evaluate_completeness(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم اكتمال المعلومات"""

        score = 0.0
        completeness_factors = []

        # العناصر الأساسية
        if result.get('title'):
            score += 0.2
            completeness_factors.append('عنوان')

        if result.get('content') and len(result['content']) > 100:
            score += 0.3
            completeness_factors.append('محتوى كافي')

        if result.get('summary'):
            score += 0.15
            completeness_factors.append('ملخص')

        if result.get('url'):
            score += 0.1
            completeness_factors.append('رابط')

        if result.get('published_date'):
            score += 0.1
            completeness_factors.append('تاريخ')

        if result.get('author'):
            score += 0.05
            completeness_factors.append('كاتب')

        if result.get('source'):
            score += 0.1
            completeness_factors.append('مصدر')

        final_score = min(score, 1.0)
        confidence = 0.8

        explanation = f"العناصر المتوفرة: {', '.join(completeness_factors)}"

        return EvaluationScore(
            criterion=EvaluationCriteria.COMPLETENESS,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'completeness_factors': completeness_factors,
                'factor_count': len(completeness_factors)
            }
        )

    async def _evaluate_uniqueness(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم تفرد المحتوى"""

        # هذا تقييم مبسط - يمكن تحسينه بمقارنة مع نتائج أخرى
        content = result.get('content', '')
        title = result.get('title', '')

        score = 0.7  # افتراض التفرد

        # فحص مؤشرات التكرار
        if 'duplicate' in content.lower() or 'copy' in content.lower():
            score -= 0.2

        # فحص طول المحتوى (المحتوى القصير قد يكون مكرر)
        if len(content) < 200:
            score -= 0.1

        # فحص التنوع في الكلمات
        if content:
            words = content.split()
            unique_words = set(words)
            diversity_ratio = len(unique_words) / max(len(words), 1)
            if diversity_ratio < 0.3:  # تكرار عالي للكلمات
                score -= 0.2

        final_score = max(score, 0.1)  # حد أدنى
        confidence = 0.6  # ثقة متوسطة لأن التقييم مبسط

        explanation = f"تقييم التفرد بناءً على طول المحتوى وتنوع الكلمات"

        return EvaluationScore(
            criterion=EvaluationCriteria.UNIQUENESS,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'content_length': len(content),
                'estimated_uniqueness': final_score
            }
        )

    async def _evaluate_engagement(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم إمكانية الجذب"""

        title = result.get('title', '')
        content = result.get('content', '')

        score = 0.5  # نقاط أساسية

        # عوامل الجذب في العنوان
        engaging_words = ['exclusive', 'breaking', 'revealed', 'secret', 'amazing', 'incredible', 'must-see']
        title_engagement = sum(1 for word in engaging_words if word.lower() in title.lower())
        score += min(title_engagement * 0.1, 0.2)

        # وجود وسائط متعددة
        if 'image' in result or 'video' in result or 'screenshot' in content.lower():
            score += 0.1

        # طول مناسب للقراءة
        if 300 <= len(content) <= 2000:
            score += 0.1

        # وجود أرقام أو إحصائيات
        if re.search(r'\d+%|\d+\.\d+|\$\d+', content):
            score += 0.05

        final_score = min(score, 1.0)
        confidence = 0.6

        explanation = f"عوامل الجذب: عنوان جذاب، طول مناسب، وسائط متعددة"

        return EvaluationScore(
            criterion=EvaluationCriteria.ENGAGEMENT,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'title_engagement_words': title_engagement,
                'content_length_appropriate': 300 <= len(content) <= 2000
            }
        )

    async def _evaluate_semantic_match(self, result: Dict, semantic_query: Optional[SemanticQuery], method: EvaluationMethod) -> EvaluationScore:
        """تقييم التطابق الدلالي"""

        if not semantic_query:
            return EvaluationScore(
                criterion=EvaluationCriteria.SEMANTIC_MATCH,
                score=0.5,
                confidence=0.3,
                explanation="لا يوجد تحليل دلالي متاح",
                method_used=method,
                metadata={}
            )

        content = f"{result.get('title', '')} {result.get('content', '')}".lower()
        score = 0.5

        # تطابق المفاهيم الدلالية
        concept_matches = []
        for concept in semantic_query.concepts:
            if concept.term.lower() in content:
                concept_matches.append(concept.term)
                score += concept.weight * 0.1

        # تطابق التوسيعات الدلالية
        expansion_matches = 0
        for expansion in semantic_query.semantic_expansions:
            expansion_words = set(expansion.lower().split())
            content_words = set(content.split())
            overlap = len(expansion_words & content_words) / max(len(expansion_words), 1)
            if overlap > 0.5:
                expansion_matches += 1
                score += 0.05

        final_score = min(score, 1.0)
        confidence = semantic_query.confidence_score

        explanation = f"تطابق المفاهيم: {len(concept_matches)}, تطابق التوسيعات: {expansion_matches}"

        return EvaluationScore(
            criterion=EvaluationCriteria.SEMANTIC_MATCH,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'concept_matches': concept_matches,
                'expansion_matches': expansion_matches,
                'semantic_confidence': semantic_query.confidence_score
            }
        )

    def _calculate_semantic_relevance(self, result: Dict, semantic_query: SemanticQuery) -> float:
        """حساب الصلة الدلالية"""
        content = f"{result.get('title', '')} {result.get('content', '')}".lower()

        relevance_score = 0.0

        # تطابق المفاهيم
        for concept in semantic_query.concepts:
            if concept.term.lower() in content:
                relevance_score += concept.weight * 0.1

        # تطابق التوسيعات
        for expansion in semantic_query.semantic_expansions:
            if expansion.lower() in content:
                relevance_score += 0.05

        return min(relevance_score, 0.3)  # حد أقصى 0.3

    async def _ai_relevance_analysis(self, result: Dict, query: str) -> float:
        """تحليل الصلة باستخدام الذكاء الاصطناعي"""
        try:
            analysis_request = {
                'query': query,
                'result_title': result.get('title', ''),
                'result_content': result.get('content', '')[:1000],  # أول 1000 حرف
                'task': 'relevance_analysis'
            }

            ai_result = await fallback_ai_manager.analyze_result_relevance(analysis_request)

            if ai_result and 'relevance_score' in ai_result:
                return min(ai_result['relevance_score'] * 0.2, 0.2)  # حد أقصى 0.2

        except Exception as e:
            logger.error(f"❌ فشل في تحليل الصلة بالذكاء الاصطناعي: {e}")

        return 0.0

    def _assess_language_quality(self, text: str) -> float:
        """تقييم جودة اللغة (مبسط)"""
        if not text:
            return 0.0

        score = 0.5

        # فحص طول الجمل
        sentences = text.split('.')
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            if 10 <= avg_sentence_length <= 30:  # طول مناسب
                score += 0.2

        # فحص التنوع في الكلمات
        words = text.split()
        if words:
            unique_words = set(words)
            diversity = len(unique_words) / len(words)
            if diversity > 0.5:
                score += 0.2

        # فحص علامات الترقيم
        punctuation_ratio = len(re.findall(r'[.!?]', text)) / max(len(text.split()), 1)
        if 0.05 <= punctuation_ratio <= 0.2:
            score += 0.1

        return min(score, 1.0)

    def _calculate_overall_score(self, scores: List[EvaluationScore]) -> float:
        """حساب النقاط الإجمالية"""
        if not scores:
            return 0.0

        total_score = sum(score.score for score in scores)
        return total_score / len(scores)

    def _calculate_weighted_score(self, scores: List[EvaluationScore]) -> float:
        """حساب النقاط المرجحة"""
        if not scores:
            return 0.0

        weighted_sum = 0.0
        total_weight = 0.0

        for score in scores:
            weight = self.evaluation_weights.get(score.criterion, 0.1)
            weighted_sum += score.score * weight
            total_weight += weight

        return weighted_sum / max(total_weight, 1.0)

    def _determine_quality_tier(self, weighted_score: float) -> str:
        """تحديد مستوى الجودة"""
        if weighted_score >= 0.85:
            return 'excellent'
        elif weighted_score >= 0.70:
            return 'good'
        elif weighted_score >= 0.50:
            return 'fair'
        else:
            return 'poor'

    def _generate_recommendation(self, scores: List[EvaluationScore], quality_tier: str) -> str:
        """إنشاء توصية"""
        if quality_tier == 'excellent':
            return "نتيجة ممتازة - موصى بها بشدة"
        elif quality_tier == 'good':
            return "نتيجة جيدة - مناسبة للاستخدام"
        elif quality_tier == 'fair':
            return "نتيجة مقبولة - قد تحتاج مراجعة"
        else:
            # تحديد نقاط الضعف
            weak_criteria = [s.criterion.value for s in scores if s.score < 0.5]
            if weak_criteria:
                return f"نتيجة ضعيفة - مشاكل في: {', '.join(weak_criteria)}"
            else:
                return "نتيجة ضعيفة - غير موصى بها"

    def _calculate_evaluator_confidence(self, scores: List[EvaluationScore]) -> float:
        """حساب ثقة المقيم"""
        if not scores:
            return 0.0

        confidence_scores = [score.confidence for score in scores]
        return sum(confidence_scores) / len(confidence_scores)

    def _rank_evaluations(self, evaluations: List[ResultEvaluation]) -> List[ResultEvaluation]:
        """ترتيب التقييمات"""
        # ترتيب حسب النقاط المرجحة
        sorted_evaluations = sorted(evaluations, key=lambda x: x.weighted_score, reverse=True)

        # تحديث نقاط الترتيب
        for i, evaluation in enumerate(sorted_evaluations):
            # إضافة مكافأة للترتيب
            position_bonus = (len(sorted_evaluations) - i) / len(sorted_evaluations) * 0.1
            evaluation.ranking_score = evaluation.weighted_score + position_bonus

        return sorted_evaluations

    def _update_evaluation_stats(self, evaluations: List[ResultEvaluation], evaluation_time: float):
        """تحديث إحصائيات التقييم"""
        try:
            # تحديث متوسط وقت التقييم
            total_evaluations = self.evaluation_stats['total_evaluations']
            current_avg = self.evaluation_stats['average_evaluation_time']

            self.evaluation_stats['average_evaluation_time'] = (
                (current_avg * (total_evaluations - len(evaluations)) + evaluation_time) / total_evaluations
            )

            # تحديث توزيع الجودة
            for evaluation in evaluations:
                self.evaluation_stats['quality_distribution'][evaluation.quality_tier] += 1

        except Exception as e:
            logger.error(f"❌ فشل في تحديث إحصائيات التقييم: {e}")

    def get_evaluation_insights(self) -> Dict[str, Any]:
        """الحصول على رؤى التقييم"""
        return {
            'total_evaluations': self.evaluation_stats['total_evaluations'],
            'ai_enhancement_rate': (
                self.evaluation_stats['ai_enhanced_evaluations'] /
                max(self.evaluation_stats['total_evaluations'], 1)
            ),
            'semantic_analysis_rate': (
                self.evaluation_stats['semantic_evaluations'] /
                max(self.evaluation_stats['total_evaluations'], 1)
            ),
            'average_evaluation_time': self.evaluation_stats['average_evaluation_time'],
            'quality_distribution': dict(self.evaluation_stats['quality_distribution']),
            'method_usage': dict(self.evaluation_stats['method_usage']),
            'evaluation_weights': self.evaluation_weights
        }

# إنشاء مثيل عام
intelligent_result_evaluator = IntelligentResultEvaluator()
    
    async def _evaluate_quality(self, result: Dict, method: EvaluationMethod) -> EvaluationScore:
        """تقييم جودة المحتوى"""
        
        title = result.get('title', '')
        content = result.get('content', '')
        
        score = 0.5  # نقاط أساسية
        
        # طول المحتوى
        content_length_score = min(len(content) / 1000, 0.2)  # حتى 0.2 لـ 1000 حرف
        score += content_length_score
        
        # جودة العنوان
        title_quality = 0.0
        if 10 <= len(title) <= 100:
            title_quality = 0.1
        if not re.search(r'[!]{2,}|[?]{2,}|[.]{3,}', title):  # تجنب العناوين المبالغ فيها
            title_quality += 0.05
        score += title_quality
        
        # وجود معلومات إضافية
        if result.get('summary'):
            score += 0.1
        if result.get('published_date'):
            score += 0.05
        if result.get('author'):
            score += 0.05
        
        # فحص جودة اللغة (مبسط)
        language_quality = self._assess_language_quality(content)
        score += language_quality * 0.1
        
        final_score = min(score, 1.0)
        confidence = 0.7
        
        explanation = f"طول المحتوى: {len(content)}, جودة العنوان: {title_quality:.2f}, جودة اللغة: {language_quality:.2f}"
        
        return EvaluationScore(
            criterion=EvaluationCriteria.QUALITY,
            score=final_score,
            confidence=confidence,
            explanation=explanation,
            method_used=method,
            metadata={
                'content_length': len(content),
                'title_length': len(title),
                'has_summary': bool(result.get('summary')),
                'has_date': bool(result.get('published_date')),
                'language_quality': language_quality
            }
        )
