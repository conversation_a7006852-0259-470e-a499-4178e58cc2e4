# واجهة الويب المحسنة للتحكم في الوكيل
from flask import Flask, render_template, jsonify, request, redirect, url_for
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import os

# استيراد الأنظمة الذكية
try:
    from .agent_state_manager import agent_state_manager, AgentState
    from .smart_database_manager import smart_db_manager
    from .operation_manager import operation_manager, OperationType, OperationPriority
    from .smart_lifecycle_manager import lifecycle_manager, StartupMode, ShutdownReason
    from .enhanced_error_handler import enhanced_error_handler
    from .logger import logger
except ImportError:
    # للاختبار المحلي
    import sys
    sys.path.append('..')

app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.secret_key = 'gaming_news_bot_secret_key_2025'

class EnhancedWebInterface:
    """واجهة الويب المحسنة"""
    
    def __init__(self):
        self.app = app
        self.is_running = False
        self.server_thread = None
        
        # تسجيل المسارات
        self._register_routes()
    
    def _register_routes(self):
        """تسجيل مسارات الواجهة"""
        
        @self.app.route('/')
        def dashboard():
            """لوحة التحكم الرئيسية"""
            return render_template('enhanced_dashboard.html')
        
        @self.app.route('/api/status')
        def get_status():
            """الحصول على حالة النظام"""
            try:
                # حالة الوكيل
                agent_status = agent_state_manager.get_current_state_info()
                
                # حالة قاعدة البيانات
                db_health = smart_db_manager.get_database_health_report()
                
                # إحصائيات العمليات
                operation_stats = operation_manager.get_statistics()
                
                # تقرير الصحة العام
                health_report = lifecycle_manager.get_health_report()
                
                # إحصائيات الأخطاء
                error_stats = enhanced_error_handler.get_error_statistics(timedelta(hours=24))
                
                return jsonify({
                    'success': True,
                    'timestamp': datetime.now().isoformat(),
                    'agent_status': agent_status.to_dict(),
                    'database_health': db_health,
                    'operation_stats': operation_stats,
                    'health_report': health_report,
                    'error_stats': error_stats
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في الحصول على حالة النظام: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/operations')
        def get_operations():
            """الحصول على العمليات النشطة"""
            try:
                active_operations = operation_manager.get_active_operations()
                all_operations = operation_manager.get_all_operations()
                
                return jsonify({
                    'success': True,
                    'active_operations': active_operations,
                    'all_operations': all_operations[-20:],  # آخر 20 عملية
                    'total_operations': len(all_operations)
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في الحصول على العمليات: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/control/start', methods=['POST'])
        def start_agent():
            """بدء تشغيل الوكيل"""
            try:
                data = request.get_json() or {}
                mode = data.get('mode', 'normal')
                
                # تحويل النص إلى enum
                startup_mode = StartupMode.NORMAL
                if mode == 'recovery':
                    startup_mode = StartupMode.RECOVERY
                elif mode == 'maintenance':
                    startup_mode = StartupMode.MAINTENANCE
                elif mode == 'safe_mode':
                    startup_mode = StartupMode.SAFE_MODE
                
                # بدء التشغيل في thread منفصل
                def start_async():
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(lifecycle_manager.smart_startup(startup_mode))
                    loop.close()
                    return success
                
                thread = threading.Thread(target=start_async)
                thread.start()
                
                return jsonify({
                    'success': True,
                    'message': f'تم بدء تشغيل الوكيل في وضع {mode}',
                    'mode': mode
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في بدء تشغيل الوكيل: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/control/stop', methods=['POST'])
        def stop_agent():
            """إيقاف الوكيل"""
            try:
                data = request.get_json() or {}
                reason = data.get('reason', 'user_request')
                emergency = data.get('emergency', False)
                
                # إيقاف في thread منفصل
                def stop_async():
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    if emergency:
                        success = loop.run_until_complete(
                            lifecycle_manager.emergency_shutdown(reason)
                        )
                    else:
                        shutdown_reason = ShutdownReason.USER_REQUEST
                        if reason == 'maintenance':
                            shutdown_reason = ShutdownReason.MAINTENANCE
                        elif reason == 'error':
                            shutdown_reason = ShutdownReason.ERROR
                        
                        success = loop.run_until_complete(
                            lifecycle_manager.graceful_shutdown(
                                reason=shutdown_reason,
                                initiated_by="web_interface"
                            )
                        )
                    
                    loop.close()
                    return success
                
                thread = threading.Thread(target=stop_async)
                thread.start()
                
                return jsonify({
                    'success': True,
                    'message': f'تم إيقاف الوكيل - السبب: {reason}',
                    'emergency': emergency
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف الوكيل: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/operations/<operation_id>/cancel', methods=['POST'])
        def cancel_operation(operation_id):
            """إلغاء عملية"""
            try:
                success = operation_manager.cancel_operation(operation_id)
                
                return jsonify({
                    'success': success,
                    'message': f'تم إلغاء العملية {operation_id}' if success else 'فشل في إلغاء العملية'
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في إلغاء العملية: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/operations/<operation_id>/pause', methods=['POST'])
        def pause_operation(operation_id):
            """إيقاف عملية مؤقتاً"""
            try:
                success = operation_manager.pause_operation(operation_id)
                
                return jsonify({
                    'success': success,
                    'message': f'تم إيقاف العملية {operation_id} مؤقتاً' if success else 'فشل في إيقاف العملية'
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف العملية: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/operations/<operation_id>/resume', methods=['POST'])
        def resume_operation(operation_id):
            """استئناف عملية"""
            try:
                success = operation_manager.resume_operation(operation_id)
                
                return jsonify({
                    'success': success,
                    'message': f'تم استئناف العملية {operation_id}' if success else 'فشل في استئناف العملية'
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في استئناف العملية: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/database/health')
        def get_database_health():
            """الحصول على تقرير صحة قاعدة البيانات"""
            try:
                health_report = smart_db_manager.get_database_health_report()
                
                return jsonify({
                    'success': True,
                    'health_report': health_report
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في الحصول على تقرير صحة قاعدة البيانات: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/database/backup', methods=['POST'])
        def create_database_backup():
            """إنشاء نسخة احتياطية من قاعدة البيانات"""
            try:
                backup_path = smart_db_manager.backup_database()
                
                return jsonify({
                    'success': True,
                    'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                    'backup_path': backup_path
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/logs')
        def get_logs():
            """الحصول على السجلات الأخيرة"""
            try:
                # قراءة آخر 100 سطر من ملف السجل
                log_file = 'logs/gaming_news_bot.log'
                logs = []
                
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        logs = lines[-100:]  # آخر 100 سطر
                
                return jsonify({
                    'success': True,
                    'logs': logs,
                    'total_lines': len(logs)
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في قراءة السجلات: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/errors')
        def get_errors():
            """الحصول على إحصائيات الأخطاء"""
            try:
                error_stats = enhanced_error_handler.get_error_statistics(timedelta(hours=24))
                
                return jsonify({
                    'success': True,
                    'error_stats': error_stats
                })
                
            except Exception as e:
                logger.error(f"❌ خطأ في الحصول على إحصائيات الأخطاء: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    def start_server(self, host='localhost', port=5000, debug=False):
        """بدء خادم الواجهة"""
        if self.is_running:
            logger.warning("⚠️ خادم الواجهة يعمل بالفعل")
            return
        
        def run_server():
            try:
                self.app.run(host=host, port=port, debug=debug, use_reloader=False)
            except Exception as e:
                logger.error(f"❌ خطأ في تشغيل خادم الواجهة: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        
        logger.info(f"🌐 تم بدء خادم الواجهة على http://{host}:{port}")
    
    def stop_server(self):
        """إيقاف خادم الواجهة"""
        if not self.is_running:
            return
        
        # Flask لا يدعم الإيقاف المباشر، لذا نعتمد على daemon thread
        self.is_running = False
        logger.info("🛑 تم إيقاف خادم الواجهة")

# إنشاء مثيل الواجهة المحسنة
enhanced_web_interface = EnhancedWebInterface()
