# وكيل أخبار الألعاب - نسخة Hugging Face

## نظرة عامة
هذا هو وكيل ذكي لنشر أخبار الألعاب على مدونة Blogger باستخدام الذكاء الاصطناعي. تم تحضير هذه النسخة خصيصاً للاستضافة على منصة Hugging Face Spaces.

## الميزات الرئيسية
- 🤖 نشر تلقائي لأخبار الألعاب
- 🔍 بحث ذكي عن الأخبار من مصادر متعددة
- 🖼️ توليد وإدارة الصور تلقائياً
- 📝 تحسين المحتوى لمحركات البحث (SEO)
- 🎯 تصنيف ذكي للألعاب والأخبار
- 📊 تحليل الأداء والإحصائيات
- 🌐 واجهة ويب تفاعلية

## متطلبات التشغيل
- Python 3.8+
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> API للخدمات المختلفة
- ح<PERSON><PERSON>ب Google للوصول إلى Blogger API

## ملفات التكوين المطلوبة
1. `config/api_config.py` - إعدادات مفاتيح API
2. `config/bot_config.json` - إعدادات البوت
3. `client_secret.json` - بيانات اعتماد Google

## التشغيل
```bash
python app.py
```

## الملاحظات المهمة
- تأكد من إعداد جميع مفاتيح API قبل التشغيل
- راجع ملفات التكوين في مجلد `config/`
- تحقق من وجود جميع التبعيات في `requirements.txt`

## الدعم
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة الوثائق الأصلية للمشروع.
