# نظام إنشاء الصور اليدوية كبديل للذكاء الاصطناعي
import os
import json
import hashlib
import random
import glob
from datetime import datetime
from typing import Dict, Optional, Tuple, List
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import textwrap
import requests
from io import BytesIO
import base64

# مكتبات معالجة النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("✅ تم تحميل مكتبات معالجة النصوص العربية")
except ImportError:
    ARABIC_SUPPORT = False
    print("⚠️ مكتبات معالجة النصوص العربية غير متاحة")

from modules.logger import logger

class ManualImageGenerator:
    """مولد الصور اليدوية - إنشاء صور بسيطة مع نص وخلفيات جميلة"""
    
    def __init__(self, website_name: str = "Gaming News"):
        self.website_name = website_name
        self.output_dir = "images/manual"
        self.cache_dir = "cache/manual_images"

        # مجلدات الخطوط والخلفيات
        self.fonts_dir = "assets/fonts"
        self.backgrounds_dir = "assets/backgrounds"

        # إنشاء المجلدات
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(self.fonts_dir, exist_ok=True)
        os.makedirs(f"{self.fonts_dir}/arabic", exist_ok=True)
        os.makedirs(f"{self.fonts_dir}/english", exist_ok=True)
        os.makedirs(self.backgrounds_dir, exist_ok=True)

        # إنشاء مجلدات الخلفيات حسب نوع الألعاب
        self.background_categories = {
            'action': 'العاب_اكشن',
            'classic': 'العاب_كلاسيكية',
            'adventure': 'العاب_مغامرة',
            'horror': 'العاب_رعب',
            'sports': 'العاب_رياضية',
            'racing': 'العاب_سباق',
            'rpg': 'العاب_ار_بي_جي',
            'strategy': 'العاب_استراتيجية',
            'simulation': 'العاب_محاكاة',
            'puzzle': 'العاب_الغاز',
            'fighting': 'العاب_قتال',
            'shooter': 'العاب_اطلاق_نار',
            'platform': 'العاب_منصات',
            'misc': 'العاب_متنوعة'
        }

        # إنشاء مجلدات الخلفيات
        for category, folder_name in self.background_categories.items():
            os.makedirs(f"{self.backgrounds_dir}/{folder_name}", exist_ok=True)

        # إعدادات الصورة
        self.image_size = (1200, 800)  # عرض × ارتفاع
        self.watermark_size = 24
        self.title_font_size = 48
        self.subtitle_font_size = 32
        
        # ألوان وخلفيات حسب الموضوع (كخلفيات احتياطية)
        self.fallback_theme_backgrounds = {
            'nintendo': {
                'colors': [(255, 0, 0), (0, 100, 200)],  # أحمر وأزرق نينتندو
                'gradient_type': 'radial',
                'blur_intensity': 15
            },
            'playstation': {
                'colors': [(0, 50, 150), (100, 150, 255)],  # أزرق بلايستيشن
                'gradient_type': 'linear',
                'blur_intensity': 12
            },
            'xbox': {
                'colors': [(0, 120, 0), (50, 200, 50)],  # أخضر إكس بوكس
                'gradient_type': 'radial',
                'blur_intensity': 10
            },
            'pc_gaming': {
                'colors': [(50, 50, 50), (150, 150, 150)],  # رمادي للكمبيوتر
                'gradient_type': 'linear',
                'blur_intensity': 8
            },
            'mobile_gaming': {
                'colors': [(255, 100, 0), (255, 200, 0)],  # برتقالي للموبايل
                'gradient_type': 'radial',
                'blur_intensity': 12
            },
            'esports': {
                'colors': [(100, 0, 200), (200, 0, 100)],  # بنفسجي للرياضات الإلكترونية
                'gradient_type': 'linear',
                'blur_intensity': 15
            },
            'general': {
                'colors': [(30, 30, 60), (60, 60, 120)],  # أزرق داكن عام
                'gradient_type': 'radial',
                'blur_intensity': 10
            }
        }

        # ربط المواضيع بفئات الخلفيات
        self.theme_to_category_mapping = {
            'nintendo': 'classic',
            'playstation': 'action',
            'xbox': 'action',
            'pc_gaming': 'misc',
            'mobile_gaming': 'misc',
            'esports': 'sports',
            'general': 'misc',
            'action': 'action',
            'adventure': 'adventure',
            'horror': 'horror',
            'racing': 'racing',
            'sports': 'sports',
            'rpg': 'rpg',
            'strategy': 'strategy',
            'simulation': 'simulation',
            'puzzle': 'puzzle',
            'fighting': 'fighting',
            'shooter': 'shooter',
            'platform': 'platform'
        }
        
        logger.info(f"🎨 تم تهيئة مولد الصور اليدوية المحسن - الموقع: {self.website_name}")

        # إنشاء ملفات README للمجلدات
        self._create_readme_files()

    def _create_readme_files(self):
        """إنشاء ملفات README للمجلدات"""
        try:
            # README للخطوط العربية
            arabic_readme = f"{self.fonts_dir}/arabic/README.md"
            if not os.path.exists(arabic_readme):
                with open(arabic_readme, 'w', encoding='utf-8') as f:
                    f.write("""# مجلد الخطوط العربية

ضع هنا ملفات الخطوط العربية بصيغة TTF أو OTF

## الخطوط المدعومة:
- .ttf
- .otf

## أمثلة على أسماء الملفات:
- NotoSansArabic-Regular.ttf
- Cairo-Regular.ttf
- Amiri-Regular.ttf
- Tajawal-Regular.ttf

## ملاحظة:
تأكد من أن الخطوط تدعم اللغة العربية بشكل صحيح
""")

            # README للخطوط الإنجليزية
            english_readme = f"{self.fonts_dir}/english/README.md"
            if not os.path.exists(english_readme):
                with open(english_readme, 'w', encoding='utf-8') as f:
                    f.write("""# English Fonts Directory

Place English font files here in TTF or OTF format

## Supported formats:
- .ttf
- .otf

## Example file names:
- Arial-Regular.ttf
- Roboto-Regular.ttf
- OpenSans-Regular.ttf
- Montserrat-Regular.ttf

## Note:
Make sure fonts support English characters properly
""")

            # README لكل فئة خلفيات
            for category, folder_name in self.background_categories.items():
                readme_path = f"{self.backgrounds_dir}/{folder_name}/README.md"
                if not os.path.exists(readme_path):
                    with open(readme_path, 'w', encoding='utf-8') as f:
                        f.write(f"""# مجلد خلفيات {folder_name}

ضع هنا صور الخلفيات المناسبة لفئة {category}

## الصيغ المدعومة:
- .jpg
- .jpeg
- .png
- .webp

## المواصفات المطلوبة:
- الحد الأدنى: 1200x800 بكسل
- الحد الأقصى: 4000x3000 بكسل
- جودة عالية ووضوح جيد

## ملاحظة:
تأكد من أن الصور مناسبة لموضوع {category} وذات جودة عالية
""")

        except Exception as e:
            logger.warning(f"⚠️ فشل في إنشاء ملفات README: {e}")

    def get_available_fonts(self, language: str) -> List[str]:
        """الحصول على قائمة الخطوط المتاحة للغة معينة"""
        try:
            font_dir = f"{self.fonts_dir}/{language}"
            if not os.path.exists(font_dir):
                return []

            font_extensions = ['*.ttf', '*.otf', '*.TTF', '*.OTF']
            fonts = []

            for ext in font_extensions:
                fonts.extend(glob.glob(os.path.join(font_dir, ext)))

            return fonts

        except Exception as e:
            logger.warning(f"⚠️ فشل في الحصول على الخطوط المتاحة: {e}")
            return []

    def get_available_backgrounds(self, category: str) -> List[str]:
        """الحصول على قائمة الخلفيات المتاحة لفئة معينة"""
        try:
            folder_name = self.background_categories.get(category, 'العاب_متنوعة')
            bg_dir = f"{self.backgrounds_dir}/{folder_name}"

            if not os.path.exists(bg_dir):
                return []

            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.webp', '*.JPG', '*.JPEG', '*.PNG', '*.WEBP']
            backgrounds = []

            for ext in image_extensions:
                backgrounds.extend(glob.glob(os.path.join(bg_dir, ext)))

            return backgrounds

        except Exception as e:
            logger.warning(f"⚠️ فشل في الحصول على الخلفيات المتاحة: {e}")
            return []

    def detect_text_language(self, text: str) -> str:
        """تحديد لغة النص (عربي أو إنجليزي)"""
        try:
            # فحص وجود أحرف عربية
            arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
            total_chars = len([char for char in text if char.isalpha()])

            if total_chars == 0:
                return 'english'  # افتراضي

            arabic_ratio = arabic_chars / total_chars

            if arabic_ratio > 0.3:  # إذا كان أكثر من 30% عربي
                return 'arabic'
            else:
                return 'english'

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحديد لغة النص: {e}")
            return 'english'

    def process_arabic_text(self, text: str) -> str:
        """معالجة النص العربي لعرضه بشكل صحيح"""
        try:
            if not ARABIC_SUPPORT:
                logger.warning("⚠️ مكتبات معالجة النصوص العربية غير متاحة")
                return text

            # تشكيل النص العربي وترتيبه
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)

            logger.info(f"🔄 تم معالجة النص العربي: {text[:20]}...")
            return bidi_text

        except Exception as e:
            logger.warning(f"⚠️ فشل في معالجة النص العربي: {e}")
            return text

    def detect_mixed_language_text(self, text: str) -> Dict:
        """تحديد النصوص المختلطة (عربي + إنجليزي)"""
        try:
            # فصل النص إلى كلمات
            words = text.split()
            arabic_words = []
            english_words = []

            for word in words:
                # إزالة علامات الترقيم للفحص
                clean_word = ''.join(char for char in word if char.isalnum())

                if any('\u0600' <= char <= '\u06FF' for char in clean_word):
                    arabic_words.append(word)
                elif any('a' <= char.lower() <= 'z' for char in clean_word):
                    english_words.append(word)
                else:
                    # كلمات مختلطة أو أرقام - إضافة للغة الأكثر
                    if len(arabic_words) >= len(english_words):
                        arabic_words.append(word)
                    else:
                        english_words.append(word)

            arabic_text = ' '.join(arabic_words).strip()
            english_text = ' '.join(english_words).strip()

            is_mixed = bool(arabic_text and english_text)

            return {
                'is_mixed': is_mixed,
                'arabic_text': arabic_text,
                'english_text': english_text,
                'primary_language': 'arabic' if len(arabic_words) > len(english_words) else 'english'
            }

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحليل النص المختلط: {e}")
            return {
                'is_mixed': False,
                'arabic_text': text,
                'english_text': '',
                'primary_language': self.detect_text_language(text)
            }

    def enhance_text_for_display(self, text: str, language: str = None) -> str:
        """تحسين النص للعرض بدون رموز تعبيرية"""
        try:
            if language is None:
                language = self.detect_text_language(text)

            if language == 'arabic':
                # معالجة النص العربي لإصلاح مشكلة الحروف المنفصلة
                enhanced_text = self.process_arabic_text(text.strip())
                return enhanced_text
            else:
                # تحسينات للنص الإنجليزي
                enhanced_text = text.strip().title()
                return enhanced_text

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحسين النص: {e}")
            return text.strip()
    
    def detect_theme_from_article(self, article: Dict) -> str:
        """تحديد الموضوع من المقال مع دعم فئات أكثر"""
        try:
            text = f"{article.get('title', '')} {article.get('content', '')}".lower()

            # كلمات مفتاحية لكل موضوع مع فئات أكثر تفصيلاً
            theme_keywords = {
                # منصات الألعاب
                'nintendo': ['nintendo', 'switch', 'mario', 'zelda', 'pokemon', 'نينتندو', 'سويتش'],
                'playstation': ['playstation', 'ps5', 'ps4', 'ps3', 'sony', 'بلايستيشن', 'سوني'],
                'xbox': ['xbox', 'microsoft', 'halo', 'forza', 'إكس بوكس', 'مايكروسوفت'],
                'pc_gaming': ['pc', 'steam', 'computer', 'كمبيوتر', 'ستيم', 'epic games'],
                'mobile_gaming': ['mobile', 'android', 'ios', 'phone', 'موبايل', 'هاتف', 'أندرويد'],

                # أنواع الألعاب
                'action': ['action', 'fighting', 'combat', 'battle', 'اكشن', 'قتال', 'معركة', 'حرب'],
                'adventure': ['adventure', 'exploration', 'quest', 'مغامرة', 'استكشاف', 'مهمة'],
                'horror': ['horror', 'scary', 'zombie', 'survival horror', 'رعب', 'مخيف', 'زومبي'],
                'racing': ['racing', 'car', 'formula', 'speed', 'سباق', 'سيارة', 'سرعة'],
                'sports': ['sports', 'football', 'soccer', 'basketball', 'رياضة', 'كرة قدم', 'كرة سلة'],
                'rpg': ['rpg', 'role playing', 'fantasy', 'magic', 'ار بي جي', 'لعب أدوار', 'خيال'],
                'strategy': ['strategy', 'tactical', 'war', 'civilization', 'استراتيجية', 'تكتيكي'],
                'simulation': ['simulation', 'sim', 'life', 'city', 'محاكاة', 'حياة', 'مدينة'],
                'puzzle': ['puzzle', 'brain', 'logic', 'الغاز', 'منطق', 'ذكاء'],
                'shooter': ['shooter', 'fps', 'gun', 'shooting', 'اطلاق نار', 'بندقية'],
                'platform': ['platform', 'jump', 'platformer', 'منصات', 'قفز'],

                # مواضيع خاصة
                'esports': ['esports', 'tournament', 'championship', 'competitive', 'رياضات إلكترونية', 'بطولة', 'منافسة'],
                'classic': ['retro', 'classic', 'vintage', 'old school', 'كلاسيكي', 'قديم', 'تراثي']
            }

            # البحث عن الكلمات المفتاحية مع إعطاء أولوية للفئات الأكثر تحديداً
            theme_scores = {}

            for theme, keywords in theme_keywords.items():
                score = 0
                for keyword in keywords:
                    if keyword in text:
                        # إعطاء نقاط أكثر للكلمات الأطول والأكثر تحديداً
                        score += len(keyword.split()) * 2 if len(keyword.split()) > 1 else 1

                if score > 0:
                    theme_scores[theme] = score

            if theme_scores:
                # اختيار الموضوع بأعلى نقاط
                best_theme = max(theme_scores, key=theme_scores.get)
                logger.info(f"🎯 تم تحديد الموضوع: {best_theme} (نقاط: {theme_scores[best_theme]})")
                return best_theme

            return 'general'

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحديد الموضوع: {e}")
            return 'general'
    
    def create_background_from_image(self, theme: str) -> Optional[Image.Image]:
        """إنشاء خلفية من صورة حقيقية حسب الموضوع"""
        try:
            # تحديد فئة الخلفية المناسبة
            category = self.theme_to_category_mapping.get(theme, 'misc')

            # الحصول على الخلفيات المتاحة
            available_backgrounds = self.get_available_backgrounds(category)

            if not available_backgrounds:
                logger.info(f"🖼️ لا توجد خلفيات متاحة لفئة {category}, استخدام الخلفية الاحتياطية")
                return None

            # اختيار خلفية عشوائية
            selected_bg = random.choice(available_backgrounds)
            logger.info(f"🖼️ تم اختيار خلفية: {os.path.basename(selected_bg)}")

            # تحميل وتحسين الصورة
            with Image.open(selected_bg) as img:
                # تحويل إلى RGB إذا لزم الأمر
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # تغيير حجم الصورة للمقاس المطلوب
                img = img.resize(self.image_size, Image.Resampling.LANCZOS)

                # تطبيق تأثير خفيف لجعل النص أكثر وضوحاً
                # إضافة طبقة شفافة داكنة
                overlay = Image.new('RGBA', self.image_size, (0, 0, 0, 80))
                img_rgba = img.convert('RGBA')
                img_with_overlay = Image.alpha_composite(img_rgba, overlay)

                return img_with_overlay.convert('RGB')

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل خلفية الصورة: {e}")
            return None

    def create_gradient_background(self, colors: list, gradient_type: str = 'linear') -> Image.Image:
        """إنشاء خلفية متدرجة"""
        try:
            img = Image.new('RGB', self.image_size, colors[0])
            draw = ImageDraw.Draw(img)
            
            if gradient_type == 'linear':
                # تدرج خطي من الأعلى للأسفل
                for y in range(self.image_size[1]):
                    ratio = y / self.image_size[1]
                    r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                    g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                    b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)
                    draw.line([(0, y), (self.image_size[0], y)], fill=(r, g, b))
            
            elif gradient_type == 'radial':
                # تدرج دائري من المركز
                center_x, center_y = self.image_size[0] // 2, self.image_size[1] // 2
                max_distance = ((center_x ** 2) + (center_y ** 2)) ** 0.5
                
                for x in range(self.image_size[0]):
                    for y in range(self.image_size[1]):
                        distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                        ratio = min(distance / max_distance, 1.0)
                        
                        r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                        g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                        b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)
                        
                        img.putpixel((x, y), (r, g, b))
            
            return img
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الخلفية المتدرجة: {e}")
            # خلفية بسيطة كبديل
            return Image.new('RGB', self.image_size, colors[0])
    
    def apply_blur_effect(self, img: Image.Image, intensity: int = 10) -> Image.Image:
        """تطبيق تأثير الضبابية على الخلفية"""
        try:
            return img.filter(ImageFilter.GaussianBlur(radius=intensity))
        except Exception as e:
            logger.warning(f"⚠️ فشل في تطبيق الضبابية: {e}")
            return img
    
    def get_font(self, size: int, bold: bool = False, arabic_support: bool = True) -> ImageFont.ImageFont:
        """الحصول على خط مناسب للنص مع دعم العربية والإنجليزية المحسن"""
        try:
            # تحديد اللغة المطلوبة
            language = 'arabic' if arabic_support else 'english'

            # الحصول على الخطوط المتاحة من المجلد المخصص
            available_fonts = self.get_available_fonts(language)

            if available_fonts:
                # اختيار خط عشوائي من الخطوط المتاحة
                selected_font = random.choice(available_fonts)
                logger.info(f"🔤 تم اختيار خط {language}: {os.path.basename(selected_font)}")
                return ImageFont.truetype(selected_font, size)

            # إذا لم توجد خطوط مخصصة، استخدام الخطوط الافتراضية
            logger.info(f"🔤 لا توجد خطوط مخصصة لـ {language}, استخدام الخطوط الافتراضية")

            # خطوط افتراضية محسنة
            if arabic_support:
                system_arabic_fonts = [
                    # خطوط Windows العربية المحسنة
                    "C:/Windows/Fonts/segoeui.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    # خطوط Linux العربية
                    "/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    # خطوط macOS العربية
                    "/System/Library/Fonts/Arial.ttf",
                    "/System/Library/Fonts/Helvetica.ttc"
                ]

                for font_path in system_arabic_fonts:
                    if os.path.exists(font_path):
                        return ImageFont.truetype(font_path, size)
            else:
                system_english_fonts = [
                    # خطوط Windows الإنجليزية
                    "C:/Windows/Fonts/segoeui.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/times.ttf",
                    # خطوط Linux الإنجليزية
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    # خطوط macOS الإنجليزية
                    "/System/Library/Fonts/Arial.ttf",
                    "/System/Library/Fonts/Helvetica.ttc"
                ]

                for font_path in system_english_fonts:
                    if os.path.exists(font_path):
                        return ImageFont.truetype(font_path, size)

            # استخدام الخط الافتراضي كحل أخير
            logger.warning("⚠️ لم يتم العثور على خطوط مناسبة، استخدام الخط الافتراضي")
            return ImageFont.load_default()

        except Exception as e:
            logger.warning(f"⚠️ فشل في تحميل الخط: {e}")
            return ImageFont.load_default()
    
    def add_text_with_outline(self, draw: ImageDraw.Draw, text: str, position: tuple, 
                             font: ImageFont.ImageFont, fill_color: tuple = (255, 255, 255), 
                             outline_color: tuple = (0, 0, 0), outline_width: int = 2):
        """إضافة نص مع حواف سوداء"""
        try:
            x, y = position
            
            # رسم الحواف السوداء
            for dx in range(-outline_width, outline_width + 1):
                for dy in range(-outline_width, outline_width + 1):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill=outline_color)
            
            # رسم النص الأساسي
            draw.text(position, text, font=font, fill=fill_color)
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إضافة النص مع الحواف: {e}")
            # رسم النص بدون حواف كبديل
            draw.text(position, text, font=font, fill=fill_color)

    def draw_mixed_language_text(self, draw: ImageDraw.Draw, text_analysis: Dict):
        """رسم النصوص المختلطة (عربي + إنجليزي) بترتيب منظم"""
        try:
            arabic_text = text_analysis['arabic_text']
            english_text = text_analysis['english_text']
            primary_language = text_analysis['primary_language']

            # تحسين النصوص
            enhanced_arabic = self.enhance_text_for_display(arabic_text, 'arabic') if arabic_text else ""
            enhanced_english = self.enhance_text_for_display(english_text, 'english') if english_text else ""

            # الحصول على الخطوط
            arabic_font = self.get_font(self.title_font_size, bold=True, arabic_support=True)
            english_font = self.get_font(self.title_font_size - 4, bold=False, arabic_support=False)  # خط أصغر قليلاً للإنجليزي

            # تحديد ترتيب النصوص (الأساسي فوق)
            if primary_language == 'arabic':
                first_text, first_font = enhanced_arabic, arabic_font
                second_text, second_font = enhanced_english, english_font
                logger.info("📝 ترتيب النص: عربي فوق، إنجليزي تحت")
            else:
                first_text, first_font = enhanced_english, english_font
                second_text, second_font = enhanced_arabic, arabic_font
                logger.info("📝 ترتيب النص: إنجليزي فوق، عربي تحت")

            # تقسيم النصوص لأسطر
            max_width = self.image_size[0] - 100
            first_lines = self.wrap_text(first_text, first_font, max_width) if first_text else []
            second_lines = self.wrap_text(second_text, second_font, max_width) if second_text else []

            # حساب المساحات
            first_line_height = self.title_font_size + 8
            second_line_height = (self.title_font_size - 4) + 8
            spacing_between_texts = 20  # مسافة بين النصين

            total_first_height = len(first_lines) * first_line_height
            total_second_height = len(second_lines) * second_line_height
            total_height = total_first_height + total_second_height + spacing_between_texts

            # موقع البداية (وسط الصورة)
            start_y = (self.image_size[1] - total_height) // 2

            # رسم النص الأول
            current_y = start_y
            for line in first_lines:
                bbox = first_font.getbbox(line)
                text_width = bbox[2] - bbox[0]
                x = (self.image_size[0] - text_width) // 2

                self.add_text_with_outline(
                    draw, line, (x, current_y), first_font,
                    fill_color=(255, 255, 255),
                    outline_color=(0, 0, 0),
                    outline_width=3
                )
                current_y += first_line_height

            # إضافة مسافة بين النصين
            current_y += spacing_between_texts

            # رسم النص الثاني
            for line in second_lines:
                bbox = second_font.getbbox(line)
                text_width = bbox[2] - bbox[0]
                x = (self.image_size[0] - text_width) // 2

                self.add_text_with_outline(
                    draw, line, (x, current_y), second_font,
                    fill_color=(255, 255, 255),
                    outline_color=(0, 0, 0),
                    outline_width=2  # حواف أرفع للنص الثانوي
                )
                current_y += second_line_height

        except Exception as e:
            logger.error(f"❌ فشل في رسم النص المختلط: {e}")
            # العودة للطريقة التقليدية
            combined_text = f"{text_analysis['arabic_text']} {text_analysis['english_text']}".strip()
            self.draw_single_language_text(draw, combined_text, text_analysis['primary_language'])

    def draw_single_language_text(self, draw: ImageDraw.Draw, text: str, language: str):
        """رسم نص بلغة واحدة"""
        try:
            # تحديد الخط المناسب
            arabic_support = (language == 'arabic')
            font = self.get_font(self.title_font_size, bold=True, arabic_support=arabic_support)

            # تقسيم النص لأسطر
            max_width = self.image_size[0] - 100
            lines = self.wrap_text(text, font, max_width)

            # حساب الارتفاع الإجمالي
            line_height = self.title_font_size + 10
            total_height = len(lines) * line_height

            # موقع البداية (وسط الصورة)
            start_y = (self.image_size[1] - total_height) // 2

            # رسم النص
            for i, line in enumerate(lines):
                bbox = font.getbbox(line)
                text_width = bbox[2] - bbox[0]
                x = (self.image_size[0] - text_width) // 2
                y = start_y + (i * line_height)

                self.add_text_with_outline(
                    draw, line, (x, y), font,
                    fill_color=(255, 255, 255),
                    outline_color=(0, 0, 0),
                    outline_width=3
                )

        except Exception as e:
            logger.error(f"❌ فشل في رسم النص المفرد: {e}")
    
    def wrap_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> list:
        """تقسيم النص لعدة أسطر"""
        try:
            words = text.split()
            lines = []
            current_line = []
            
            for word in words:
                test_line = ' '.join(current_line + [word])
                bbox = font.getbbox(test_line)
                text_width = bbox[2] - bbox[0]
                
                if text_width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        # الكلمة طويلة جداً، قسمها
                        lines.append(word)
            
            if current_line:
                lines.append(' '.join(current_line))
            
            return lines
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في تقسيم النص: {e}")
            return [text]  # إرجاع النص كما هو
    
    def add_watermark(self, img: Image.Image) -> Image.Image:
        """إضافة العلامة المائية في أسفل يمين الصورة"""
        try:
            draw = ImageDraw.Draw(img)
            font = self.get_font(self.watermark_size)
            
            # حساب موقع العلامة المائية
            bbox = font.getbbox(self.website_name)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # موقع أسفل يمين مع هامش
            margin = 20
            x = self.image_size[0] - text_width - margin
            y = self.image_size[1] - text_height - margin
            
            # إضافة النص مع شفافية
            self.add_text_with_outline(
                draw, self.website_name, (x, y), font,
                fill_color=(255, 255, 255, 180),  # أبيض شبه شفاف
                outline_color=(0, 0, 0, 100),     # أسود شبه شفاف
                outline_width=1
            )
            
            return img
            
        except Exception as e:
            logger.warning(f"⚠️ فشل في إضافة العلامة المائية: {e}")
            return img
    
    async def generate_manual_image(self, article: Dict) -> Optional[Dict]:
        """إنشاء صورة يدوية للمقال"""
        try:
            logger.info(f"🎨 بدء إنشاء صورة يدوية للمقال: {article.get('title', '')[:50]}...")
            
            # تحديد الموضوع
            theme = self.detect_theme_from_article(article)

            # محاولة إنشاء خلفية من صورة حقيقية أولاً
            background = self.create_background_from_image(theme)

            if background is None:
                # إذا لم تتوفر صورة، استخدام الخلفية المتدرجة كبديل
                logger.info("🎨 استخدام خلفية متدرجة كبديل")
                theme_config = self.fallback_theme_backgrounds.get(theme, self.fallback_theme_backgrounds['general'])

                background = self.create_gradient_background(
                    theme_config['colors'],
                    theme_config['gradient_type']
                )

                # تطبيق الضبابية
                background = self.apply_blur_effect(background, theme_config['blur_intensity'])
            else:
                logger.info("🖼️ تم استخدام خلفية صورة حقيقية")
            
            # إعداد الرسم
            draw = ImageDraw.Draw(background)
            
            # إعداد النصوص
            title = article.get('title', 'Gaming News')

            # تحليل النص للتحقق من وجود لغات مختلطة
            text_analysis = self.detect_mixed_language_text(title)

            if text_analysis['is_mixed']:
                # التعامل مع النصوص المختلطة
                logger.info("🔤 تم اكتشاف نص مختلط (عربي + إنجليزي)")
                self.draw_mixed_language_text(draw, text_analysis)
            else:
                # التعامل مع نص بلغة واحدة
                language = text_analysis['primary_language']
                enhanced_title = self.enhance_text_for_display(title, language)
                self.draw_single_language_text(draw, enhanced_title, language)
            
            # إضافة العلامة المائية
            final_image = self.add_watermark(background)
            
            # حفظ الصورة
            filename = f"manual_{hashlib.md5(title.encode()).hexdigest()[:8]}.png"
            filepath = os.path.join(self.output_dir, filename)
            final_image.save(filepath, 'PNG', quality=95)
            
            # إنشاء URL محلي للصورة
            image_url = f"file://{os.path.abspath(filepath)}"
            
            result = {
                'url': image_url,
                'local_path': filepath,
                'filename': filename,
                'description': f'Manual image for: {title[:50]}...',
                'source': 'Manual Generator',
                'license': 'Generated Content',
                'attribution': f'Created by {self.website_name}',
                'width': self.image_size[0],
                'height': self.image_size[1],
                'format': 'PNG',
                'generation_method': 'manual_generation',
                'theme': theme,
                'creation_date': datetime.now().isoformat()
            }
            
            logger.info(f"✅ تم إنشاء صورة يدوية بنجاح: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الصورة اليدوية: {e}")
            return None
    
    def set_website_name(self, name: str):
        """تعديل اسم الموقع للعلامة المائية"""
        self.website_name = name
        logger.info(f"🏷️ تم تحديث اسم الموقع إلى: {name}")

# إنشاء مثيل عام
manual_image_generator = ManualImageGenerator()
